import { Box, Button } from "@mui/material";
import React, { useEffect, useState } from "react";
import { AgGridReact } from "ag-grid-react";
import { Constants } from "../utils/constants";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import { useLocation, useNavigate } from "react-router-dom";
import SnackBarMessage from "../components/SnackBarMessage";
import { NotificationType } from "../types";
import AduUsersListGridDefs from "./AduUsersListGridDefs";
import { Confirm } from "react-admin";
import { deleteAduUser } from "../service/mutations";

const AduUsersList = () => {
  const {
    columnDefs,
    defaultColDef,
    onGridReady,
    allLoadList,
    openDeleteUser,
    setOpenDeleteUser,
    deletingUser,
    getAduUsersList,
  } = AduUsersListGridDefs();

  const navigate = useNavigate();
  const location = useLocation();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState("");
  const [statusMessageType, setStatusMessageType] =
    useState<NotificationType>("success");

  useEffect(() => {
    if (location.state?.status) {
      setStatusMessage(location.state?.status);
      setOpenSnackbar(true);
      setStatusMessageType("warning");
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const handleClose = () => {
    setOpenDeleteUser(false);
  };

  const handleConfirm = () => {
    setDeleteLoading(true);
    deleteAduUser(deletingUser).then((res: any) => {
      if (res.status === "success") {
        getAduUsersList();
      } else {
        setStatusMessageType("error");
      }
      setDeleteLoading(false);
      setOpenDeleteUser(false);
      setOpenSnackbar(true);
      setStatusMessage(res.message);
    });
  };

  return (
    <Box sx={{ paddingX: "10px", width: "100%", marginTop: "20px" }}>
      <Confirm
        isOpen={openDeleteUser}
        loading={deleteLoading}
        title="Delete ADU User"
        content={`The user will be removed from all realms. Do you want to continue?`}
        confirm="Continue"
        onConfirm={handleConfirm}
        onClose={handleClose}
      />
      <Box sx={{ width: "100%", display: "flex", justifyContent: "flex-end" }}>
        <Button
          variant="outlined"
          startIcon={<PersonAddIcon fontSize="small"></PersonAddIcon>}
          onClick={() => navigate("create")}
        >
          Add User
        </Button>
      </Box>
      <Box sx={{ width: "100%", marginTop: "10px" }}>
        <div
          className={Constants.ag_grid_theme}
          style={{ height: "85vh", width: "100%" }}
        >
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            rowData={allLoadList}
            defaultColDef={defaultColDef}
            rowSelection="single"
            onGridReady={(params: any) => onGridReady(params)}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            stopEditingWhenCellsLoseFocus={true}
          />
        </div>
      </Box>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={statusMessageType}
      />
    </Box>
  );
};

export default AduUsersList;
