import React, { useEffect, useState } from "react";
import IconButton from "@mui/material/IconButton";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import SaveIcon from "@mui/icons-material/Save";
import CancelIcon from "@mui/icons-material/Cancel";
import SettingsQueries from "../../../service/Mutations/Settings";
import { Confirm } from "react-admin"; // <<--- make sure imported

const ActionCellRenderer: React.FC<any> = (props: any) => {
  const { api, rowIndex, node, onSaveSuccess } = props;
  const [isRowEditing, setIsRowEditing] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const { UpsertDbSettings, DeleteServer } = SettingsQueries;

  useEffect(() => {
    const checkIsEditing = () => {
      const editingCells = api.getEditingCells();
      const isEditing = editingCells.some(
        (cell: any) => cell.rowIndex === rowIndex
      );
      setIsRowEditing(isEditing);
    };

    api.addEventListener("cellEditingStarted", checkIsEditing);
    api.addEventListener("cellEditingStopped", checkIsEditing);

    checkIsEditing();

    return () => {
      api.removeEventListener("cellEditingStarted", checkIsEditing);
      api.removeEventListener("cellEditingStopped", checkIsEditing);
    };
  }, [api, rowIndex]);

  const handleEdit = (event: { preventDefault: () => void }) => {
    event.preventDefault();

    api.startEditingCell({
      rowIndex,
      colKey: "serverNo",
    });
  };

  const handleSave = (event: { preventDefault: () => void }) => {
    event.preventDefault();

    api.stopEditing(false);
    const updatedData = node.data;
    const inputEladdress = document.querySelector(
      ".element-ipAddress-" + rowIndex
    ) as HTMLElement;
    const inputEldbanme = document.querySelector(
      ".element-dbName-" + rowIndex
    ) as HTMLElement;
    const inputElusername = document.querySelector(
      ".element-dbUser-" + rowIndex
    ) as HTMLElement;
    const inputElpassword = document.querySelector(
      ".element-dbPassword-" + rowIndex
    ) as HTMLElement;
    const inputElport = document.querySelector(
      ".element-dbPort-" + rowIndex
    ) as HTMLElement;

    const ipPattern =
      /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/;
    const dbPattern = /^[a-zA-Z0-9]+$/;
    const dbUserPattern = /^[a-zA-Z0-9]+$/;
    const dbPassPattern = /^[a-zA-Z0-9]+$/;
    const dbPortPattern = /^[a-zA-Z0-9]+$/;
    // ip address
    if (!ipPattern.test(updatedData.ipAddress)) {
      if (inputEladdress) {
        inputEladdress.style.setProperty("border-color", "red", "important");
      }
      handleEdit(event);
      return false;
    } else {
      inputEladdress.style.setProperty("border-color", "", "important");
    }

    // db name
    if (!dbPattern.test(updatedData.dbName)) {
      if (inputEldbanme) {
        inputEldbanme.style.setProperty("border-color", "red", "important");
      }
      handleEdit(event);
      return false;
    } else {
      inputEldbanme.style.setProperty("border-color", "", "important");
    }

    // username
    if (!dbUserPattern.test(updatedData.dbUser)) {
      if (inputElusername) {
        inputElusername.style.setProperty("border-color", "red", "important");
      }
      handleEdit(event);
      return false;
    } else {
      inputElusername.style.setProperty("border-color", "", "important");
    }
    // password
    if (!dbPassPattern.test(updatedData.dbPassword)) {
      if (inputElpassword) {
        inputElpassword.style.setProperty("border-color", "red", "important");
      }
      handleEdit(event);
      return false;
    } else {
      inputElpassword.style.setProperty("border-color", "", "important");
    }

    // port
    if (!dbPortPattern.test(updatedData.dbPort)) {
      if (inputElport) {
        inputElport.style.setProperty("border-color", "red", "important");
      }
      handleEdit(event);
      return false;
    } else {
      inputElport.style.setProperty("border-color", "", "important");
    }

    updatedData.dbPort = parseInt(updatedData.dbPort);
    console.log("uuuuuu===", updatedData);
    UpsertDbSettings(updatedData)
      .then((res: any) => {
        onSaveSuccess("success", res, true);
      })
      .catch((err) => {
        onSaveSuccess("error", "something went wrong", true);
      });
  };

  const handleCancel = () => {
    api.stopEditing(true);
  };

  const handleDelete = () => {
    setConfirmOpen(true);
  };

  const handleConfirmDelete = () => {
    const deleteData = node.data;
    deleteData.dbPort = parseInt(deleteData.dbPort);

    DeleteServer(deleteData)
      .then((res: any) => {
        onSaveSuccess("success", res, true);
      })
      .catch((err) => {
        onSaveSuccess("error", "something went wrong", true);
      })
      .finally(() => {
        setConfirmOpen(false);
      });
  };

  return (
    <div style={{ display: "flex", justifyContent: "center", gap: "0.5rem" }}>
      {isRowEditing ? (
        <>
          <IconButton size="small" onClick={handleSave} title="Save">
            <SaveIcon fontSize="small" />
          </IconButton>
          <IconButton size="small" onClick={handleCancel} title="Cancel">
            <CancelIcon fontSize="small" />
          </IconButton>
        </>
      ) : (
        <>
          <IconButton size="small" onClick={handleEdit} title="Edit">
            <EditIcon fontSize="small" />
          </IconButton>
          <IconButton size="small" onClick={handleDelete} title="Delete">
            <DeleteIcon fontSize="small" />
          </IconButton>
        </>
      )}

      <Confirm
        isOpen={confirmOpen}
        title="Delete Db Settings"
        content="Are you sure you want to delete these details?"
        onConfirm={handleConfirmDelete}
        onClose={() => setConfirmOpen(false)}
      />
    </div>
  );
};

export default ActionCellRenderer;
