import * as React from "react";
import { Box } from "@mui/material";
import { Constants } from "../utils/constants";

const getColorFromStatus = (status: string) =>
  status === "Completed"
    ? "#7dbde8"
    : status === "Pending"
    ? "#e88b7d"
    : status === "In Progress"
    ? "#a4e87d"
    : status === "Failed"
    ? "red"
    : "#000";

export const Status = ({ status }: { status: string }) => (
  <Box
  marginRight={1}
    marginLeft={0.5}
    width={8}
    height={8}
    display="inline-block"
    borderRadius="5px"
    bgcolor={getColorFromStatus(status)}
    component="span"
  />
);
