import { Box, Card, CardContent, Typography } from "@mui/material";
import React from "react";
import InfoIcon from "@mui/icons-material/InfoOutlined";
import DeleteSweepIcon from "@mui/icons-material/DeleteSweep";
import ChecklistIcon from "@mui/icons-material/Checklist";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import RecommendIcon from "@mui/icons-material/Recommend";
import RateReviewIcon from "@mui/icons-material/RateReview";
import { useNavigate } from "react-router-dom";
import EngineeringIcon from "@mui/icons-material/Engineering";
const DashboardCard = (props: any) => {
  const {
    cardName,
    setIsHovered,
    isHovered,
    isCardSelected,
    setIsCardSelected,
    count,
  } = props;
  const navigate = useNavigate();
  const fontColor =
    cardName === "onboarding"
      ? "#f89d1b"
      : cardName === "launched" || cardName === "nsQaValidation"
      ? "green"
      : cardName === "review"
      ? "#003399"
      : cardName === "readyToLaunch"
      ? "#9900ff"
      : cardName === "cancelled"
      ? "red"
      : "#647d96";
  return (
    <Card
      onMouseOver={() => setIsHovered(cardName)}
      onMouseOut={() => setIsHovered("")}
      elevation={isHovered === cardName ? 10 : 5} // Elevate the card when hovered
      style={{
        transition: "box-shadow 0.3s", // Smooth transition for elevation change
        cursor: "pointer", // Change cursor on hover
      }}
      raised
      sx={{
        width: "15rem",
        borderRadius: "15px",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        mr: "2%",
        mb: "1%",
        backgroundColor: isCardSelected === cardName ? "#e2eefe" : "white",
      }}
      onClick={() => {
        const dashboard = { filter: cardName };
        localStorage.setItem("dashboard", JSON.stringify(dashboard));
        navigate("/statelessServiceBzoTenants/dashboard/" + cardName);
      }}
    >
      <CardContent
        style={{
          paddingBottom: "5px",
        }}
        sx={{
          width: "90%",
          height: "90%",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "5px",
          paddingTop: "5px",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-start",
            alignItems: "center",
          }}
        >
          {cardName === "onboarding" ? (
            <InfoIcon sx={{ color: fontColor, fontSize: "1.5rem" }} />
          ) : cardName === "nsQaValidation" ? (
            <ChecklistIcon sx={{ color: fontColor, fontSize: "1.5rem" }} />
          ) : cardName === "launched" ? (
            <CheckCircleIcon sx={{ color: fontColor, fontSize: "1.5rem" }} />
          ) : cardName === "readyToLaunch" ? (
            <RecommendIcon sx={{ color: fontColor, fontSize: "1.5rem" }} />
          ) : cardName === "review" ? (
            <RateReviewIcon sx={{ color: fontColor, fontSize: "1.5rem" }} />
          ) : cardName === "cancelled" ? (
            <DeleteSweepIcon sx={{ color: fontColor, fontSize: "1.5rem" }} />
          ) : (
            <EngineeringIcon sx={{ color: fontColor, fontSize: "1.5rem" }} />
          )}
          <Typography
            sx={{
              fontSize: "0.8rem",
              color: "#647d96",
              ml: "5px",
            }}
          >
            {cardName === "cancelled"
              ? "Cancelled"
              : cardName === "onboarding"
              ? "Onboarding"
              : cardName === "nsQaValidation"
              ? "NS QA Validation"
              : cardName === "readyToLaunch"
              ? "Ready to Launch"
              : cardName === "review"
              ? "In Review"
              : cardName === "launched"
              ? "Launched"
              : "Test Tenant"}
          </Typography>
        </Box>
        <Typography
          sx={{ fontWeight: "bold", fontSize: "1rem", color: fontColor }}
        >
          {count}
        </Typography>
      </CardContent>
    </Card>
  );
};

export default DashboardCard;
