import * as React from "react";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import { Box, IconButton, Tooltip } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import { updateMatrixType } from "../service/mutations";
import CancelIcon from "@mui/icons-material/Cancel";
import {
  Confirm,
  Form,
  SaveButton,
  TextInput,
  useRecordContext,
  useTranslate,
} from "react-admin";
import { Constants } from "../utils/constants";
import EditIcon from "@mui/icons-material/Edit";
import { Delete } from "@material-ui/icons";

export default function AddMatrixType(props: any) {
  const translate = useTranslate();
  const record: any = useRecordContext();
  const {
    fetchPartsMatrixRowData,
    isEdit,
    isMatrixModal,
    matrixName,
    getMatrixTypesList,
    setStatusMessage,
    setOpenSnackbar,
    setStatusMessageType,
  } = props;
  const [open, setOpen] = React.useState(false);
  const [openDeleteConfirm, setOpenDeleteConfirm] = React.useState(false);
  const [errorMsg, setErrorMsg] = React.useState("");

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setErrorMsg("");
    setOpenDeleteConfirm(false);
  };
  const submitMatrixType = async (matrixtype: any, action: string) => {
    const input = {
      inNewMatrixType: matrixtype,
      inOldMatrixType: action === "edit" ? matrixName : matrixtype,
      inStoreId: record.storeId,
      inTenantId: record.tenantId,
      Realm: record.realmName,
      inActivity:
        action === "edit"
          ? "update"
          : action === "delete"
          ? "delete"
          : "insert",
    };
    try {
      const res: any = await updateMatrixType(input);
      if (res.status === 1) {
        const statusMessage = translate(res.msg);
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
        action === "delete" && setOpenDeleteConfirm(false);
        action !== "delete" && fetchPartsMatrixRowData();
        (action === "edit" || action === "delete") &&
          getMatrixTypesList(record);
        setStatusMessageType("success");
        handleClose();
      } else {
        const statusMessage = translate(res.msg);
        action === "delete" && setOpenDeleteConfirm(false);
        setStatusMessageType("error");
        setStatusMessage(statusMessage);
        // setOpenSnackbar(true);
        setErrorMsg(statusMessage);
      }
    } catch (error) {
      const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
      action === "delete" && setOpenDeleteConfirm(false);
      setStatusMessageType("error");
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
      console.log(error);
    }
  };

  const submitForm = async (matrixtype: any) => {
    const matrixType = matrixtype.matrixtype.trim();
    if (!matrixType || /\s{2,}/.test(matrixType)) {
      setErrorMsg("Invalid Matrix Type");
    } else {
      isEdit
        ? submitMatrixType(matrixType, "edit")
        : submitMatrixType(matrixType, "insert");

      // setErrorMsg("");
    }
  };

  const validateMatrixType = (value: any) => {
    if (!value) return "Please enter Matrix Name";
    const trimmedValue = value.trim();
    if (value.length > 50)
      return "Matrix name cannot be more than 50 characters";
    if (trimmedValue !== value)
      return "Matrix Name cannot start or end with spaces";
    if (/\s{2,}/.test(trimmedValue))
      return "Matrix Name cannot contain multiple consecutive spaces";
    if (!Constants.patterns.gridName.test(value))
      return "Matrix Name cannot contain special characters";
    return undefined;
  };

  const handleConfirm = () => {
    submitMatrixType(matrixName, "delete");
  };

  return (
    <>
      {/* To edit matrix name from the list */}
      {isEdit ? (
        <Box sx={{ display: "flex" }}>
          <Tooltip title="Edit">
            <IconButton
              onClick={handleClickOpen}
              sx={{
                color: "#003d6b",
              }}
            >
              <EditIcon style={{ fontSize: 15, color: "#003d6b" }} />
            </IconButton>
          </Tooltip>
          <Tooltip title="Delete">
            <IconButton
              onClick={() => setOpenDeleteConfirm(true)}
              sx={{
                color: "#003d6b",
              }}
            >
              <Delete style={{ fontSize: 15, color: "#003d6b" }} />
            </IconButton>
          </Tooltip>
        </Box>
      ) : // When list modal has no matrix name, so add a name
      !isEdit && isMatrixModal ? (
        <Button
          color="primary"
          variant="outlined"
          sx={{ textTransform: "none", mt: 1 }}
          onClick={handleClickOpen}
        >
          Add new matrix name
        </Button>
      ) : (
        // From the new matrix insert form
        <IconButton
          onClick={handleClickOpen}
          style={{ height: "fit-content", marginTop: isEdit ? "" : "14px" }}
        >
          <AddIcon />
        </IconButton>
      )}

      <Dialog fullWidth open={open} onClose={handleClose}>
        <DialogTitle sx={{ padding: "16px 20px 4px" }}>
          {isEdit ? "Edit Matrix Name" : "Add Matrix Name"}
        </DialogTitle>
        <Form onSubmit={submitForm}>
          <DialogContent sx={{ padding: "0px 20px" }}>
            <TextInput
              defaultValue={matrixName && matrixName}
              autoFocus
              margin="dense"
              id="matrixtype"
              name="matrixtype"
              label="Matrix Type"
              type="text"
              fullWidth
              variant="standard"
              source="matrixtype"
              validate={validateMatrixType}
              helperText={false}
              onChange={() => {
                setErrorMsg("");
              }}
            />
            {errorMsg && (
              <div style={{ color: "red", fontSize: 12 }}>{errorMsg}</div>
            )}
          </DialogContent>
          <DialogActions>
            <SaveButton label="Submit" />
            <Button
              color="primary"
              variant="contained"
              startIcon={<CancelIcon />}
              sx={{ m: 2 }}
              onClick={() => handleClose()}
            >
              {translate("BUTTONS.CANCEL")}
            </Button>
          </DialogActions>
        </Form>
      </Dialog>
      <Confirm
        isOpen={openDeleteConfirm}
        // loading={loading}
        title="Delete Matrix Name"
        content={`Do you want to delete ${matrixName}?`}
        onConfirm={handleConfirm}
        onClose={handleClose}
      />
    </>
  );
}
