import { But<PERSON>, <PERSON>ir<PERSON>P<PERSON>ress, <PERSON><PERSON><PERSON><PERSON>on, <PERSON><PERSON><PERSON> } from "@mui/material";
import React, { useState } from "react";
import { Confirm, useRecordContext } from "react-admin";

const OpcodeConfirmationModal: React.FC<any> = (props) => {
  const { submitOpcodeDetails, RowData, isDisabled } = props;

  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const record: any = useRecordContext();
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setOpen(true);
  };

  const submitDisable = RowData.some(
    (item: any) => item.opcategory === "-----"
  );

  const handleDialogClose = () => setOpen(false);

  const handleConfirm = () => {
    setLoading(true);
    submitOpcodeDetails().finally(() => {
      setLoading(false);
      setO<PERSON>(false);
    });
  };
  return (
    <>
      { !isDisabled&&<Button
        disabled={submitDisable}
        size="small"
        variant="contained"
        color="primary"
        onClick={handleClick}
        style={{
          position: "absolute",
          top: 0,
          right: "77px",
          transform: "translateY(-50%)",
          textTransform: "none",
          fontSize: "12px",
        }}
      >
        Submit
      </Button>}
      <Confirm
        isOpen={open}
        loading={loading}
        title="Confirm Submission"
        content="Do you want to submit your changes?"
        onConfirm={handleConfirm}
        onClose={handleDialogClose}
      />
    </>
  );
};

export default OpcodeConfirmationModal;
