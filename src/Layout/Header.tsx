import React from "react";
import {
  Tabs,
  Tab,
  Toolbar,
  AppB<PERSON>,
  <PERSON>,
  Typography,
  Tooltip,
} from "@mui/material";
import { Link, matchPath, useLocation, useNavigate } from "react-router-dom";
import {
  UserMenu,
  Logout,
  LoadingIndicator,
  useTranslate,
  Button,
} from "react-admin";
import { useKeycloak } from "@react-keycloak/web";
import { PageRoutes } from "../utils/pageRoutes";
import HomeIcon from "@mui/icons-material/Home";
import noLogo from "../images/armatus-new-logo.png";
import ViewTimelineIcon from "@mui/icons-material/ViewTimeline";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";

const Header = () => {
  const location = useLocation();
  const { keycloak } = useKeycloak();
  const translate = useTranslate();
  const navigate = useNavigate();
  let currentPath = PageRoutes.root;
  if (
    !!matchPath(
      PageRoutes.statelessServiceBzoTenantsDynamicSegment,
      location.pathname
    )
  ) {
    currentPath = PageRoutes.statelessServiceBzoTenants;
  } else if (
    !!matchPath(
      PageRoutes.statelessServiceBzoStoreDetailsDynamicSegment,
      location.pathname
    )
  ) {
    currentPath = PageRoutes.statelessServiceBzoStoreDetails;
  } else if (!!matchPath(PageRoutes.launchReports, location.pathname)) {
    currentPath = PageRoutes.launchReports;
  } else if (!!matchPath(PageRoutes.changeLogs, location.pathname)) {
    currentPath = PageRoutes.changeLogs;
  } else if (!!matchPath(PageRoutes.neverEvents, location.pathname)) {
    currentPath = PageRoutes.neverEvents;
  } else if (!!matchPath(PageRoutes.activityLog, location.pathname)) {
    currentPath = PageRoutes.activityLog;
  } else if (!!matchPath(PageRoutes.testResults, location.pathname)) {
    currentPath = PageRoutes.testResults;
  } else if (!!matchPath(PageRoutes.allStores, location.pathname)) {
    currentPath = PageRoutes.allStores;
  }

  const handleLogout = () => {
    localStorage.clear();
    keycloak.logout();
  };

  return (
    <Box component="nav" sx={{ flexGrow: 1 }}>
      <Toolbar variant="dense">
        <Box flex={1} display="flex" justifyContent="space-between">
          <Box display="flex" alignItems="center" flex={4}>
            <Box
              component="img"
              sx={{ marginRight: "1em", height: 30 }}
              src={noLogo}
              alt="Armatus Logo"
            />
            <Typography component="span" variant="h5">
              {translate("TITLES.HOME_TITLE")}
            </Typography>
          </Box>
          <Box flex={10}></Box>
          <Box display="flex" flex={1} alignItems="center">
            <Tooltip title="01/11/24 Version 3.0.0">
              <Typography
                variant="body2"
                sx={{ marginRight: "8px", whiteSpace: "nowrap" }}
              >
                <Link
                  to={PageRoutes.changeLogs}
                  style={{
                    fontStyle: "italic",
                    textDecoration: "underline",
                    fontSize: "small",
                    color: "inherit", // Ensure the text color is inherited
                  }}
                >
                  See What's New
                </Link>
              </Typography>
            </Tooltip>
            <Box width="40px">
              <Button
                sx={{
                  color: "white",
                  minWidth: 0,
                  width: "40px",
                  height: "40px",
                  padding: "8px",
                }}
                aria-label="Home"
                onClick={() => navigate("/")}
              >
                <Tooltip title="Home">
                  <HomeIcon sx={{ "&:first-of-type": { fontSize: "23px" } }} />
                </Tooltip>
              </Button>
            </Box>
            {/* <LoadingIndicator
                sx={{
                  "& .RaLoadingIndicator-loader": {
                    marginTop: 2,
                  },
                }}
              /> */}
            <UserMenu>
              <Box sx={{margin: "0 16px", minWidth: "100px", display: "flex", alignItems: "center"}}>
                <AccountCircleIcon sx={{marginRight: "16px"}}/>
                <Typography>{localStorage.getItem("user")}</Typography>
              </Box>
              <Logout onClick={handleLogout} />
            </UserMenu>
          </Box>
        </Box>
      </Toolbar>
    </Box>
  );
};

export default Header;
