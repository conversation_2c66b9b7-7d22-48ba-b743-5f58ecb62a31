import { Button } from "@mui/material";
import { ArrowBack } from "@mui/icons-material";
import { useNavigate } from "react-router";

const BackButton = (props: any) => {
  const { url } = props;
  const navigate = useNavigate();
  const handleBackClick = () => {
    if (url) navigate(url);
    else navigate(-1);
  };
  return (
    <Button
      startIcon={<ArrowBack />}
      color="primary"
      onClick={() => handleBackClick()}
      sx={{ mt: 1.5 }}
    >
      BACK
    </Button>
  );
};

export default BackButton;