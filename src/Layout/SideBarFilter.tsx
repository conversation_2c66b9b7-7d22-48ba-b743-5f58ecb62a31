import { Box, Card, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { GetDmsList } from "../service/dataFetchQueries";

const SideBarFilter = () => {
  const dmsNames = ["Dealertrack", "CDK Global", "Automate", "Reynolds"];

  useEffect(() => {
    GetDmsList().then((res) => {});
  }, []);
  return (
    <Card
      sx={{
        display: "flex",
        width: "17vw",
        marginRight: "40px",
        height: "85vh",
        borderRadius: "10px",
        padding: "10px 15px",
      }}
    >
      <Typography variant="subtitle1" fontWeight={"bold"} color={"#3e698a"}>
        DMS
      </Typography>
    </Card>
  );
};

export default SideBarFilter;
