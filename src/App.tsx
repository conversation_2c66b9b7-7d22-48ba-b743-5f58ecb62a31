import React, { useEffect, useState } from "react";
import "./App.css";
import { Admin, CustomRoutes, Resource } from "react-admin";
import tenants from "./tenants";
import stores from "./stores";
import Layout from "./Layout/Layout";
import { ReactKeycloakProvider } from "@react-keycloak/web";
import { DMSListGrid } from "./tenants/ListDms";
import pgDataProvider from "ra-postgraphile";
import { ApolloProvider } from "@apollo/client";
import { keycloak, onToken, onKeycloakEvent } from "./keycloakConfig";
import polyglotI18nProvider from "ra-i18n-polyglot";
import englishMessages from "./i18n/en";
import { Checklist } from "./Checklist/checklist";
import { BrowserRouter, Navigate, Route } from "react-router-dom";
import apolloConfig from "./service/apolloConfig";
import Changelogs from "./Changelog/Changelogs";
import ActivityLog from "./ActivityLog/ActivityLog";
import NeverEventsDetailsTable from "./NeverEvents/NeverEventDetailsTable";
import LaunchReportGrid from "./LaunchReport/LaunchReportGrid";
import TestResults from "./TestResults/testResults";
import { QueryClient } from "react-query";
import StoreList from "./stores/StoreList";
import AllDetails from "./stores/AllModuleDetails/AllDetails";
import DailyLoad from "./DailyLoad/dailyLoad";
import ErrorStatuses from "./NeverEvents/ErrorStatuses";
import UsersList from "./NeverEvents/UserRoles/userRoles";
import { routes } from "./routes";
import DMSBilling from "./DMSBilling/DMSBilling";
import { SnackbarProvider } from "notistack";
import { Fade } from "@mui/material";
import DailyDataAsOf from "./NeverEvents/DataAsOf/DailyDataAsOf";
import DailyLogin from "./NeverEvents/DailyLogins/DailyLogin";
import AduUsersList from "./ADU/AduUsersList";
import AddUser from "./ADU/AddUser";
import AllTenantsList from "./tenants/AllTenants/AllTenantsList";
import EmailReport from "./NeverEvents/EmailReport/EmailReport";
import RO13MonthReport from "./NeverEvents/RO13MonthReport/RO13MonthReport";
import SchemaConfig from "./Settings/SchemaConfiguration/SchemaConfig";
// import the view page of the DbSettings.tsx
import DbSettings from "./Settings/DbSettings/DbSettings";
import AddDbSettings from "./Settings/DbSettings/AddDbSettings";
import MissedReport from "./NeverEvents/MissedReport/MissedReport";

require("dotenv").config();
const client = apolloConfig();
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
});
const i18nProvider = polyglotI18nProvider((locale) => {
  return englishMessages;
}, "en");

function App() {
  const [dataProvider, setDataProvider] = useState(null);
  const dashboardCard = JSON.parse(localStorage.getItem("dashboard") || "[]");
  useEffect(() => {
    (async () => {
      const dataProvider = await pgDataProvider(client);
      setDataProvider(() => dataProvider);
    })();
  }, []);
  return (
    <ApolloProvider client={client}>
      <ReactKeycloakProvider
        authClient={keycloak}
        LoadingComponent={<div></div>}
        initOptions={{
          onLoad: "login-required",
          redirectUri:
            dashboardCard.length === 0 && process.env.REACT_APP_DASHBOARD_URL,
        }}
        onTokens={onToken}
        onEvent={onKeycloakEvent}>
        <BrowserRouter>
          <SnackbarProvider
            maxSnack={4}
            anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
            TransitionComponent={Fade} // Change slide to fade
          >
            {dataProvider && (
              <Admin
                queryClient={queryClient}
                dataProvider={dataProvider}
                layout={Layout}
                loginPage={false}
                i18nProvider={i18nProvider}>
                <CustomRoutes>
                  {routes.map((route, index) => (
                    <Route
                      key={index}
                      path={route.path}
                      element={<route.Component />}
                    />
                  ))}
                </CustomRoutes>
                <CustomRoutes>
                  {/* <Route
                  path="statelessServiceBzoTenants/dashboard/:tenantStatus"
                  element={<Dashboard />}
                />*/}
                  <Route
                    path="/"
                    element={
                      <Navigate to="/statelessServiceBzoTenants/dashboard/onboarding" />
                    }
                  />
                  <Route
                    path="/statelessServiceBzoTenants"
                    element={
                      <Navigate
                        to={
                          dashboardCard.length !== 0
                            ? "/statelessServiceBzoTenants/dashboard/" +
                              dashboardCard.filter
                            : "/statelessServiceBzoTenants/dashboard/onboarding"
                        }
                      />
                    }
                  />
                  <Route
                    path="/statelessServiceBzoTenants/dashboard"
                    element={
                      <Navigate
                        to={
                          dashboardCard.length !== 0
                            ? "/statelessServiceBzoTenants/dashboard/" +
                              dashboardCard.filter
                            : "/statelessServiceBzoTenants/dashboard/onboarding"
                        }
                      />
                    }
                  />
                  <Route
                    path="/"
                    element={
                      <Navigate
                        to={
                          dashboardCard.length !== 0
                            ? "/statelessServiceBzoTenants/dashboard/" +
                              dashboardCard.filter
                            : "/statelessServiceBzoTenants/dashboard/onboarding"
                        }
                      />
                    }
                  />
                  <Route
                    path="/statelessServiceBzoStoreDetails/AllDetails"
                    element={<AllDetails />}
                  />
                </CustomRoutes>
                <Resource name="statelessServiceBzoTenants" {...tenants} />
                <Resource name="tenantMasters" {...tenants} />
                <Resource name="storeMasters" {...stores} />
                <Resource name="statelessServiceBzoStoreDetails" {...stores} />
                <Resource name="groupingMasters" />
                <Resource name="dmsMasters" list={DMSListGrid} />
                <CustomRoutes>
                  <Route
                    path="statelessServiceBzoTenants/dashboard/allTenants"
                    element={<AllTenantsList />}
                  />
                  <Route
                    path="/statelessServiceBzoStoreDetails"
                    element={<StoreList />}
                  />
                  <Route path="/LaunchReport" element={<LaunchReportGrid />} />
                  <Route path="/NeverEvents/EDI" element={<DailyLoad />} />
                  <Route
                    path="/NeverEvents/FOPC"
                    element={<ErrorStatuses eventType="never" />}
                  />
                  <Route
                    path="NeverEvents/maintainance"
                    element={<ErrorStatuses eventType="maintenance" />}
                  />
                  <Route path="/NeverEvents/userRole" element={<UsersList />} />
                  <Route
                    path="/NeverEvents/dailyDataAsOf"
                    element={<DailyDataAsOf />}
                  />
                  <Route
                    path="/NeverEvents/dailyLogins"
                    element={<DailyLogin />}
                  />
                  <Route
                    path="/NeverEvents/aduUsers"
                    element={<AduUsersList />}
                  />
                  <Route
                    path="/NeverEvents/aduUsers/create"
                    element={<AddUser />}
                  />
                  <Route
                    path="/NeverEvents/EmailReport"
                    element={<EmailReport />}
                  />
                  <Route
                    path="/NeverEvents/RO13MonthReport"
                    element={<RO13MonthReport />}
                  />
                  <Route path="/settings/dbSettings" element={<DbSettings />} />
                  <Route
                    path="/settings/dbSettings/create"
                    element={<AddDbSettings />}
                  />
                  <Route
                    path="/settings/schemaConfiguration"
                    element={<SchemaConfig />}
                  />
                  <Route
                    path="/NeverEvents/MissedReport"
                    element={<MissedReport />}
                  />

                  <Route path="/ActivityLog" element={<ActivityLog />} />
                  <Route path="/changeLogs" element={<Changelogs />} />
                  <Route path="/dmsBilling" element={<DMSBilling />} />
                  <Route
                    path="/TestResults/regression"
                    element={<TestResults eventType="regression" />}
                  />
                  <Route
                    path="/TestResults/smoke"
                    element={<TestResults eventType="smoke" />}
                  />
                  <Route
                    path="/NeverEvents/:clientName"
                    element={<NeverEventsDetailsTable />}
                  />
                  <Route
                    path="/statelessServiceBzoTenants/:id/show/:tenantId/checklist"
                    element={<Checklist />}
                  />
                  <Route
                    path="/statelessServiceBzoTenants/:id/show/:tenantId/:storeId/checklist"
                    element={<Checklist />}
                  />
                  <Route
                    path="/statelessServiceBzoTenants/:id/show/:tenantId/:storeId/checklist/:viewStatus"
                    element={<Checklist />}
                  />
                  {/*
                 <Route
                  path="/statelessServiceBzoStoreDetails/:id/show/RevenueSummaryDrilldown"
                  element={<RevenueSummaryDrilldown />}
                /> 
                */}
                </CustomRoutes>
              </Admin>
            )}
          </SnackbarProvider>
        </BrowserRouter>
      </ReactKeycloakProvider>
    </ApolloProvider>
  );
}

export default App;
