import { ColDef } from "ag-grid-community";
import moment from "moment";
import React, { useState } from "react";
import StoreQueries from "../../service/DataFetchQueries/storeQueries";
import TenantQueries from "../../service/DataFetchQueries/tenantQueries";
import { exportMultipleSheetsAsExcel } from "ag-grid-enterprise";

const AllDetailsGridDefs = (props?: any) => {
  const { GetStoreListQuery, GetFilteredModuleDetailsQuery } = StoreQueries;
  const { GetAllTenantsQuery } = TenantQueries;
  const [filteredDetailsList, setFilteredDetailsList] = useState<any>([]);
  const [allTenants, setAllTenants] = useState<any>([]);
  const [tenant, setTenant] = useState<string>("");
  const [store, setStore] = useState<string>("");
  const [allStores, setAllStores] = useState<any>([]);
  const [selectedModule, setSelectedModule] = useState<string>("Labor Grid");
  const [agGridApi, setAgGridApi] = useState<any>();
  const fetchAllTenantsList = async () => {
    try {
      const res = await GetAllTenantsQuery();
      const sortedTenants = [...res].sort((a, b) =>
        a.tenantName.localeCompare(b.tenantName)
      );
      setAllTenants(sortedTenants);
    } catch (error) {
      console.error("Error fetching launch reports list:", error);
    }
  };

  const fetchFilteredDetailsList = async (
    filterTenant: string,
    filterStore: string,
    filterModule: string
  ) => {
    const tempInput = {
      tenant: filterTenant,
      store: filterStore,
      module: filterModule,
    };
    try {
      const res = await GetFilteredModuleDetailsQuery(tempInput);
      setFilteredDetailsList(res);
    } catch (error) {
      console.error("Error fetching launch reports list:", error);
    }
  };

  const onGridReady = (params: any) => {
    setAgGridApi(params.api);
    params?.api?.showLoadingOverlay();
    fetchFilteredDetailsList(tenant, store, selectedModule).then(() => {
      params?.api?.hideOverlay();
    });
  };
  const commonColumnDefs: ColDef[] = [
    {
      headerName: "Tenant Name",
      field: "tenantName",
      width: 340,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Store Name",
      field: "storeName",
      width: 350,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "DMS",
      field: "dms",
      width: 150,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Module Type",
      field: "callType",
      width: 150,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "FOPC Calculated Date From",
      field: "calculatedDateFrom",
      cellStyle: { textAlign: "left" },
      width: 200,
      valueFormatter: (params: any) => {
        return params.value && moment(params.value).format("MM/DD/YYYY");
      },
    },
    {
      headerName: "Store Install Date",
      field: "storeInstallDate",
      width: 200,
      cellStyle: { textAlign: "left" },
      valueFormatter: (params: any) => {
        return params.value && moment(params.value).format("MM/DD/YYYY");
      },
    },
  ];
  const columnDefsLabor: ColDef[] = [
    ...commonColumnDefs,
    {
      headerName: "Grid Type",
      field: "gridFor",
      width: 150,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Grid Name",
      field: "gridType",
      width: 300,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Grid Period",
      field: "gridPeriod",
      width: 150,
      cellStyle: { textAlign: "left" },
    },
  ];

  const columnDefsParts: ColDef[] = [
    ...commonColumnDefs,
    {
      headerName: "Matrix Name",
      field: "gridType",
      width: 300,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Matrix Period",
      field: "gridPeriod",
      width: 150,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Source",
      field: "matrixSource",
      width: 300,
      cellStyle: { textAlign: "left" },
    },
  ];

  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { textAlign: "center" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      width: 180,
      suppressSizeToFit: true,
      filterParams: {
        applyMiniFilterWhileTyping: true,
      },
    };
  }, [filteredDetailsList]);

  const handleExport = (api: any, fileName: string, sheetName: string) => {
    const spreadsheets: any[] = [];
    spreadsheets.push(api.getSheetDataForExcel({ sheetName }));

    exportMultipleSheetsAsExcel({
      data: spreadsheets,
      fileName,
    });
  };

  const onBtnExport = () => {
    handleExport(agGridApi!, "Module Details.xlsx", "Module Details");
  };
  const handleTenantFilter = async (e: any, allTenants: any) => {
    const selectedTenant = allTenants?.find(
      (item: any) => item.tenantId === e.target.value
    );
    const realmName = selectedTenant?.realmName;
    agGridApi?.showLoadingOverlay();
    setTenant(e.target.value);
    setStore("");
    fetchFilteredDetailsList(e.target.value, "", selectedModule);
    try {
      const res = await GetStoreListQuery(e.target.value, false, realmName);
      setAllStores(res);
    } catch (error) {
      console.error("Error fetching launch reports list:", error);
    }
  };
  const handleStoreFilter = async (e: any) => {
    agGridApi?.showLoadingOverlay();
    setStore(e.target.value);
    fetchFilteredDetailsList(tenant, e.target.value, selectedModule);
  };
  const handleModuleFilter = async (e: any) => {
    agGridApi?.showLoadingOverlay();
    setSelectedModule(e.target.value);
    fetchFilteredDetailsList(tenant, store, e.target.value);
  };
  const handleReset = () => {
    agGridApi?.showLoadingOverlay();
    setTenant("");
    setStore("");
    fetchFilteredDetailsList("", "", selectedModule);
  };
  return {
    columnDefsLabor,
    defaultColDef,
    onGridReady,
    tenant,
    allTenants,
    fetchAllTenantsList,
    setTenant,
    handleTenantFilter,
    store,
    handleStoreFilter,
    allStores,
    selectedModule,
    handleModuleFilter,
    filteredDetailsList,
    handleReset,
    columnDefsParts,
    onBtnExport,
  };
};

export default AllDetailsGridDefs;
