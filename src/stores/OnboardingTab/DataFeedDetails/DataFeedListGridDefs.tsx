import React, { useRef, useState } from "react";
import { ColDef } from "ag-grid-community";
import moment from "moment";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Constants } from "../../../utils/constants";
import {
  faBan,
  faPencil,
  faFloppyDisk,
  faTrash,
} from "@fortawesome/free-solid-svg-icons";
import { useGetList } from "react-admin";

const DataFeedListGridDefs = (props: any) => {
  const {allLoadList} = props;
  const { data, isLoading } = useGetList("dmsMasters");
  const buttonStyle = {
    borderRadius: "3px",
    height: "22px",
    border: "0",
    fontSize: "12px",
    cursor: "pointer",
    lineHeight: "13px",
    margin: "0 4px",
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "DMS",
      field: "dms",
      cellStyle: { textAlign: "left" },
      flex: 4,
      cellEditor: "agSelectCellEditor",
      cellEditorParams: {
        values: data?.map((item: any) => item.dms),
      },
    },
    {
      headerName: "Dealer Id",
      field: "dealerId",
      flex: 5,
      cellStyle: { textAlign: "left" },
      valueFormatter: (params: any) => (params.value ? params.value : "----"),
      editable: (params) => params.data.dms === "Dealertrack" ? false : true,
    },

    {
      headerName: "Enterprise Code",
      field: "enterpriseCode",
      flex: 5,
      cellStyle: { textAlign: "left" },
      valueFormatter: (params: any) => (params.value ? params.value : "----"),
      editable: (params) => params.data.dms === "Dealertrack" ? true : false,
    },
    {
      headerName: "Company Number",
      field: "companyNumber",
      flex: 5,
      cellStyle: { textAlign: "left" },
      valueFormatter: (params: any) => (params.value ? params.value : "----"),
      editable: (params) => params.data.dms === "Dealertrack" ? true : false,
    },
    {
      headerName: "Server Name",
      field: "serverName",
      flex: 5,
      cellStyle: { textAlign: "left" },
      valueFormatter: (params: any) => (params.value ? params.value : "----"),
      editable: (params) => params.data.dms === "Dealertrack" ? true : false,
    },
    {
      headerName: "Effective Date",
      field: "effectiveDate",
      cellStyle: { textAlign: "left" },
      flex: 2,
      valueFormatter: (params: any) => 
        params.value ? moment(params.value).format("MM/DD/YYYY") : params.value,

    },
    {
      headerName: "Action",
      minWidth: 150,
      flex: 2,
      cellStyle: { border: "none" },
      suppressNavigable: true,
      sortable: false,
      filter: false,
      unSortIcon: false,
      cellRenderer: function (params: any) {
        const editingCells = params?.api?.getEditingCells();
        const isCurrentRowEditing = editingCells?.some(
          (cell: any) => cell.rowIndex === params.node.rowIndex
        );
        if (isCurrentRowEditing) {
          return (
            <div style={{ marginTop: "0px" }}>
              <button
                style={buttonStyle}
                className="action-button update"
                data-action={Constants.actions.update}
              >
                <FontAwesomeIcon icon={faFloppyDisk} />
              </button>
              <button
                style={buttonStyle}
                className="action-button cancel"
                data-action={Constants.actions.cancel}
                disabled={allLoadList.length > 1 ? false : true}
              >
                <FontAwesomeIcon icon={faBan} />
              </button>
            </div>
          );
        } else {
          return (
            <div style={{ marginTop: "0px" }}>
              <button
                style={buttonStyle}
                className="action-button"
                data-action={Constants.actions.edit}
              >
                <FontAwesomeIcon icon={faPencil} />
              </button>
              <button
                style={buttonStyle}
                className="action-button"
                data-action={Constants.actions.delete}
              >
                <FontAwesomeIcon icon={faTrash} />
              </button>
            </div>
          );
        }
      },
      editable: false,
      colId: "action",
    },
  ];
  const defaultColDef = React.useMemo(() => {
    return {
      flex: 1,
      minWidth: 150,
      resizable: true,
      floatingFilter: true,
      sortable: true,
      filter: true,
      suppressMenu: true,
      unSortIcon: true,
      filterParams: {
        applyMiniFilterWhileTyping: true,
      },
      enableValue: true,
      editable: true,
    };
  }, []);
  return {
    columnDefs,
    defaultColDef
  };
};

export default DataFeedListGridDefs;
