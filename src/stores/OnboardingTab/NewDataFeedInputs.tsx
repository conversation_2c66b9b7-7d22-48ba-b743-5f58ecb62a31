import * as React from "react";
import {
  TextInput,
  SelectInput,
  required,
  useTranslate,
  FormDataConsumer,
  regex,
} from "react-admin";
import { Stack, Box, Typography } from "@mui/material";
import { Constants } from "../../utils/constants";
import { DatePicker, DatePickerProps } from "antd";
import dayjs from "dayjs";

export const NewDataFeedInputs = (props: any) => {
  const { data, effectiveDate, setEffectiveDate } = props;
  const translate = useTranslate();
  const onDateChange: DatePickerProps["onChange"] = (date, dateString) => {
    setEffectiveDate(date);
  };
  return (
    <Box flex="1" mt={-1} width={600}>
      <Typography variant="h6" sx={{ marginY: "0px", fontWeight: "bold" }}>
        Add New Data Feeds
      </Typography>
      <TextInput
        source="storeName"
        label={"Store name"}
        fullWidth
        validate={required()}
        disabled
        helperText={false}
      />
      <SelectInput
        label="DMS"
        fullWidth
        validate={required(" ")}
        source="Dms"
        choices={data}
        optionText="dms"
        optionValue="dms"
        helperText={false}
      />
      <FormDataConsumer<{ Dms: string }>>
        {({ formData, ...rest }) =>
          formData.Dms &&
          formData.Dms !== "Dealertrack" && (
            <TextInput
              source="DealerId"
              label="Dealer Id"
              fullWidth
              validate={[
                required(" "),
                regex(
                  formData.Dms === "Reynolds"
                    ? Constants.patterns.dealerId
                    : Constants.patterns.alphaNumericRegex,
                  formData.Dms === "Reynolds"
                    ? "Invalid dealer id format for Reynolds"
                    : translate("VALIDATION_MESSAGES.ALPHANUMERIC")
                ),
              ]}
              helperText={false}
            />
          )
        }
      </FormDataConsumer>
      <FormDataConsumer<{ Dms: string }>>
        {({ formData, ...rest }) =>
          formData.Dms === "Dealertrack" && (
            <>
              <Stack
                direction="row"
                display={"flex"}
                justifyContent={"space-between"}
                sx={{ width: "100%" }}
              >
                <TextInput
                  source="CompanyNumber"
                  label="Company Number"
                  sx={{ width: "49%" }}
                  validate={[
                    required(" "),
                    regex(
                      Constants.patterns.alphaNumericRegex,
                      translate("VALIDATION_MESSAGES.ALPHANUMERIC")
                    ),
                  ]}
                  helperText={false}
                />
                <TextInput
                  source="EnterpriseCode"
                  label="Enterprise Code"
                  sx={{ width: "49%" }}
                  validate={[
                    required(" "),
                    regex(
                      Constants.patterns.alphaNumericRegex,
                      translate("VALIDATION_MESSAGES.ALPHANUMERIC")
                    ),
                  ]}
                  helperText={false}
                />
              </Stack>
              <TextInput
                source="ServerName"
                label="Server Name"
                fullWidth
                validate={[
                  required(" "),
                  regex(
                    Constants.patterns.alphaNumericRegex,
                    translate("VALIDATION_MESSAGES.ALPHANUMERIC")
                  ),
                ]}
                helperText={false}
              />
            </>
          )
        }
      </FormDataConsumer>
      <p
        style={{
          fontSize: 12,
          marginLeft: "4px",
          marginBottom: 0,
          opacity: 0.8,
          color: "grey",
        }}
      >
        Effective date*
      </p>
      <DatePicker
        required
        popupStyle={{ zIndex: 9999 }}
        size={"large"}
        onChange={onDateChange}
        placeholder="Select Date"
        format={"MM/DD/YYYY"}
        value={effectiveDate}
        variant="filled"
        style={{
          width: "100%",
          border: "none",
          borderBottom: "1px solid #958a8a",
          borderRadius: 0,
          padding: "11px",
        }}
      ></DatePicker>
    </Box>
  );
};
