import { useRecordContext } from "react-admin";
import { useEffect } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Typography,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { AgGridReact } from "ag-grid-react";
import BulkLoadGridDefs from "./BulkLoadGridDefs";
import useOnboarding from "../../CustomHooks/useOnboarding";
import { Constants } from "../../utils/constants";

const BulkLoadStatusLog = (props: any) => {
  const { phase, phaseStatus } = props;
  const record = useRecordContext();
  const { getBulkLoadStatuslog, rowData } = useOnboarding({
    record: record,
    phase: phase,
    id: record.id,
  });
  const { columnDefs, defaultColDef, gridData } = BulkLoadGridDefs(rowData);

  if (phaseStatus === "Active" || phaseStatus === "Inactive") return null;
  return (
    <Accordion
      style={{
        marginBottom: 16,
        backgroundColor: "#eaf1f6",
        border: "solid 2px",
        borderColor: "#003d6b",
      }}
      onChange={(e: React.SyntheticEvent, expanded: boolean) => {
        if (expanded) {
          getBulkLoadStatuslog();
        } else {
        }
      }}
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        sx={{ display: "flex" }}
      >
        <Box sx={{ flex: 2 }}>
          <Typography
            sx={{
              fontSize: 14,
              color: "#003d6b",
              fontWeight: "bold",
            }}
          >
            {phase === "bulk_phase_1"
              ? "Phase 1"
              : phase === "bulk_phase_2"
              ? "Phase 2"
              : "Daily Load"}
          </Typography>
        </Box>
      </AccordionSummary>
      <AccordionDetails>
        <div
          className={Constants.ag_grid_theme}
          style={{ height: "85vh", width: "100%" }}
        >
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            rowData={gridData}
            defaultColDef={defaultColDef}
            rowSelection="single"
            animateRows={true}
            // @ts-ignore
            //  ref={gridRef}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            stopEditingWhenCellsLoseFocus={true}
          />
        </div>
      </AccordionDetails>
    </Accordion>
  );
};

export default BulkLoadStatusLog;
