import { ColDef } from "ag-grid-community";
import moment from "moment";
import { useMemo } from "react";

const BulkLoadGridDefs = (rowData: any) => {
  const FormatCellValueRate = (params: any) => {
    return moment(params.value).format("MM/DD/YYYY h:mma");
  };
  const statusFont = (params: any) => {
    return {
      color:
         params.value == "Failed"
          ? "red"
          : "green",
      fontWeight: "bold",
    };
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "Sl.No",
      field: "slno",
      flex: 1,
      cellStyle: { textAlign: "right" },
    },
    {
      headerName: "Description",
      field: "description",
      flex: 5,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: "Date & Time",
      field: "datetime",
      flex: 1.8,
      valueFormatter: FormatCellValueRate,
    },
    {
      headerName: "Database",
      field: "source",
      flex: 2.5,
    },
    {
      headerName: "Status",
      field: "status",
      flex: 1.1,
      cellStyle: statusFont,
    },
    {
      headerName: "Error Log",
      field: "error",
      flex: 7.8,
      wrapText: true,
      autoHeight: true,
    },
  ];

  const gridData: any[] = Array.from(
    { length: rowData.length },
    (_, index) => ({
      slno: index + 1,
      ...(rowData[index] as object),
    })
  );

  const defaultColDef = useMemo(() => {
    return {
      cellStyle: { fontSize: "14px" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
    };
  }, []);

  return {
    columnDefs,
    defaultColDef,
    gridData,
  };
};

export default BulkLoadGridDefs;