import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ir<PERSON>P<PERSON>ress,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import {
  Confirm,
  Form,
  SaveButton,
  TextInput,
  maxLength,
  required,
  useRecordContext,
} from "react-admin";
import useOnboarding from "../../CustomHooks/useOnboarding";
import { AdjustSharp, CheckCircle } from "@mui/icons-material";
import ErrorIcon from "@mui/icons-material/Error";
import BulkLoadStatusLog from "./BulkLoadStatusLog";
import { DatePicker, DatePickerProps, Space } from "antd";
import dayjs from "dayjs";
import useStoreCreate from "../../CustomHooks/useStoreCreate";
import SnackBarMessage from "../../components/SnackBarMessage";
import CancelIcon from "@mui/icons-material/Cancel";
import ForumIcon from "@mui/icons-material/Forum";
import moment from "moment";
import AddCommentIcon from "@mui/icons-material/AddComment";
import StoreMutations from "../../service/Mutations/storeMutations";
import DeleteIcon from "@mui/icons-material/Delete";
import MotionPhotosOffIcon from "@mui/icons-material/MotionPhotosOff";

const BulkLoading = (props?: any) => {
  const { getStoreQuery } = props;
  const record = useRecordContext();
  const [selectedBillingDate, setSelectedBillingDate] = useState<any>(null);
  const [selectedDate, setSelectedDate] = useState<any>(null);
  const [showCommentBox, setShowCommentBox] = useState<boolean>(false);
  const [openConfirmDelete, setOpenConfirmDelete] = useState<boolean>(false);
  const [deleteComment, setDeleteComment] = useState<any>({});
  const {
    handleSubmit,
    updatingBilling,
    setUpdatingBilling,
    showBillingSaveButton,
    setShowBillingSaveButton,
    setOpenSnackbar,
    openSnackbar,
    statusMessage,
    setStatusMessage,
  } = useStoreCreate({ action: "billingdate", getStoreQuery: getStoreQuery });
  const { handleBulkLoad, getQaComments, comments } = useOnboarding({
    id: record.id,
    record: record,
    bulkStoreQuery: getStoreQuery,
  });
  const { InsertComment } = StoreMutations;
  const boxRef = useRef<any>(null);

  useEffect(() => {
    if (boxRef.current) {
      boxRef.current.scrollTop = boxRef.current.scrollHeight;
    }
  }, [comments]);
  const onDateChange: DatePickerProps["onChange"] = (date, dateString) => {
    setSelectedBillingDate(date);
    setShowBillingSaveButton(true);
  };
  const updateEstimatedDate = () => {
    setUpdatingBilling(true);
    let submitdata = {
      ...record,
      billingDate: selectedBillingDate
        ? dayjs(selectedBillingDate).format("MM/DD/YYYY")
        : null,
    };
    handleSubmit(submitdata);
  };

  function StepIcon(props: any) {
    const { record } = props;
    return (
      <div>
        {record === "In Progress" ? (
          <AdjustSharp
            className="QontoStepIcon-activeIcon"
            style={{ color: "#f7b500" }}
            fontSize="large"
          />
        ) : record === "Completed" ? (
          <CheckCircle style={{ color: "#008000" }} fontSize="large" />
        ) : record === "Active" ? (
          <AdjustSharp
            className="QontoStepIcon-activeIcon"
            style={{ color: "#1976d2" }}
            fontSize="large"
          />
        ) : record === "Inactive" ? (
          <AdjustSharp
            className="QontoStepIcon-activeIcon"
            style={{ color: "#00000061" }}
            fontSize="large"
          />
        ) : record === "Failed" ? (
          <ErrorIcon style={{ color: "#d32f2f" }} fontSize="large" />
        ) : (
          <MotionPhotosOffIcon
            style={{ color: "#00000061" }}
            fontSize="large"
          />
        )}
      </div>
    );
  }

  useEffect(() => {
    record?.billingDate && setSelectedBillingDate(dayjs(record?.billingDate));
  }, [record]);

  const handleSubmitComment = (values: any, activity: string) => {
    InsertComment({ ...values, activity: activity }).then((res) => {
      getQaComments();
      setShowCommentBox(false);
      setDeleteComment({});
      setOpenConfirmDelete(false);
      setOpenSnackbar(true);
      setStatusMessage(res);
    });
  };
  useEffect(() => {
    getQaComments();
  }, []);
  const handleDialogClose = () => {
    setDeleteComment({});
    setOpenConfirmDelete(false);
  };
  const handleConfirm = () => {
    handleSubmitComment(deleteComment, "Delete");
  };
  return (
    <Box>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
      />
      <Box
        sx={{
          display: "flex",
          alignContent: "center",
          mt: 3,
          flexDirection: "column",
        }}
      >
        <Typography
          sx={{
            fontSize: 16,
            color: "#003d6b",
            fontWeight: "bold",
            // display: "flex",
            alignItems: "center",
            mb: "32px",
          }}
        >
          Bulk Data Load
        </Typography>
        <Stepper alternativeLabel sx={{ width: "100%" }} activeStep={2}>
          <Step completed={true}>
            <Tooltip title={record.bulkPhase1}>
              <StepLabel
                StepIconComponent={() => (
                  <StepIcon record={record.bulkPhase1} />
                )}
              >
                <Button
                  onClick={() => handleBulkLoad("bulk_phase_1", "In Progress")}
                  disabled={
                    record.bulkPhase1 !== "Active" &&
                    record.bulkPhase1 !== "Failed"
                  }
                  sx={{
                    "&:hover": {
                      backgroundColor: "#C0DCF5",
                    },
                  }}
                >
                  PHASE 1 {record.bulkPhase1 === "Failed" && "Retry"}
                </Button>
                {record.bulkPhase1StartDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    Start Date:{" "}
                    {moment(record.bulkPhase1StartDate).format("MM/DD/YY")}
                  </Typography>
                )}
                {record.bulkPhase1CompletedDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    End Date:{" "}
                    {moment(record.bulkPhase1CompletedDate).format("MM/DD/YY")}
                  </Typography>
                )}
              </StepLabel>
            </Tooltip>
          </Step>
          <Step>
            <Tooltip title={record.bulkPhase2}>
              <StepLabel
                StepIconComponent={() => (
                  <StepIcon record={record.bulkPhase2} />
                )}
              >
                <Button
                  onClick={() => handleBulkLoad("bulk_phase_2", "In Progress")}
                  disabled={
                    record.bulkPhase2 !== "Active" &&
                    record.bulkPhase2 !== "Failed"
                  }
                  sx={{
                    "&:hover": {
                      backgroundColor: "#C0DCF5",
                    },
                  }}
                >
                  PHASE 2 {record.bulkPhase2 === "Failed" && "Retry"}
                </Button>

                {record.bulkPhase2StartDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    Start Date:{" "}
                    {moment(record.bulkPhase2StartDate).format("MM/DD/YY")}
                  </Typography>
                )}
                {record.bulkPhase2CompletedDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    End Date:{" "}
                    {moment(record.bulkPhase2CompletedDate).format("MM/DD/YY")}
                  </Typography>
                )}
              </StepLabel>
            </Tooltip>
          </Step>
          <Step>
            <Tooltip title={record.dailyLoad}>
              <StepLabel
                StepIconComponent={() => <StepIcon record={record.dailyLoad} />}
              >
                <Button
                  disabled={
                    record.dailyLoad === "Disabled" ||
                    record.dailyLoad !== "Active"
                  }
                  onClick={() =>
                    record.dailyLoad === "Completed"
                      ? handleBulkLoad("daily_load", "Disabled")
                      : handleBulkLoad("daily_load", "In Progress")
                  }
                  sx={{
                    "&:hover": {
                      backgroundColor: "#C0DCF5",
                    },
                  }}
                >
                  {record.dailyLoad === "Completed" && "Disable"} Daily Load
                  Setup {record.dailyLoad === "Failed" && "Retry"}
                </Button>

                {record.dailyLoadStartDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    Start Date:{" "}
                    {moment(record.dailyLoadStartDate).format("MM/DD/YY")}
                  </Typography>
                )}
                {record.dailyLoadCompletedDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    End Date:{" "}
                    {moment(record.dailyLoadCompletedDate).format("MM/DD/YY")}
                  </Typography>
                )}
              </StepLabel>
            </Tooltip>
          </Step>
          <Step>
            <Tooltip title={record.nsQaValidation}>
              <StepLabel
                StepIconComponent={() => (
                  <StepIcon record={record.nsQaValidation} />
                )}
              >
                <Button
                  disabled={
                    record.nsQaValidation !== "Active" &&
                    record.nsQaValidation !== "In Progress"
                  }
                  onClick={() =>
                    record.nsQaValidation === "Active"
                      ? handleBulkLoad("ns_qa_validation", "In Progress")
                      : handleBulkLoad("ns_qa_validation", "Completed")
                  }
                  sx={{
                    "&:hover": {
                      backgroundColor: "#C0DCF5",
                    },
                  }}
                >
                  NS QA Validation
                </Button>
                {record.nsQaValidationStartDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    Start Date:{" "}
                    {moment(record.nsQaValidationStartDate).format("MM/DD/YY")}
                  </Typography>
                )}
                {record.nsQaValidationCompletedDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    End Date:{" "}
                    {moment(record.nsQaValidationCompletedDate).format(
                      "MM/DD/YY"
                    )}
                  </Typography>
                )}
              </StepLabel>
            </Tooltip>
          </Step>
          <Step>
            <Tooltip title={record.review}>
              <StepLabel
                StepIconComponent={() => <StepIcon record={record.review} />}
              >
                <Button
                  disabled={
                    record.review !== "Active" &&
                    record.review !== "In Progress"
                  }
                  onClick={() =>
                    record.review === "Active"
                      ? handleBulkLoad("review", "In Progress")
                      : handleBulkLoad("review", "Completed")
                  }
                  sx={{
                    "&:hover": {
                      backgroundColor: "#C0DCF5",
                    },
                  }}
                >
                  Review
                </Button>
                {record.reviewStartDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    Start Date:{" "}
                    {moment(record.reviewStartDate).format("MM/DD/YY")}
                  </Typography>
                )}
                {record.reviewCompletedDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    End Date:{" "}
                    {moment(record.reviewCompletedDate).format("MM/DD/YY")}
                  </Typography>
                )}
              </StepLabel>
            </Tooltip>
          </Step>
          <Step>
            <Tooltip title={record.storeLaunched}>
              <StepLabel
                StepIconComponent={() => (
                  <StepIcon record={record.storeLaunched} />
                )}
              >
                <Button
                  disabled={record.storeLaunched !== "Active"}
                  onClick={() => handleBulkLoad("store_launched", "Completed")}
                  sx={{
                    "&:hover": {
                      backgroundColor: "#C0DCF5",
                    },
                  }}
                >
                  Launch Store
                </Button>
              </StepLabel>
            </Tooltip>
          </Step>
        </Stepper>
        <Box
          sx={{
            display: "flex",
            alignItems: "flex-end",
            marginBottom: "10px",
            height: "fit-content",
          }}
        >
          <Space direction="vertical" style={{ justifyContent: "flex-end" }}>
            <Box sx={{ display: "flex", flexDirection: "column" }}>
              <Typography
                sx={{
                  color: record.dailyLoad === "Completed" ? "#1976D2" : "grey",
                  fontSize: "12px",
                }}
              >
                Billing Date
              </Typography>
              <DatePicker
                disabled={record.dailyLoad !== "Completed"}
                onChange={onDateChange}
                placeholder="Select Date"
                format={"MM/DD/YYYY"}
                value={selectedBillingDate}
                color="primary"
              />
            </Box>
          </Space>
          <Button
            disabled={!showBillingSaveButton}
            onClick={updateEstimatedDate}
            variant="contained"
            size="small"
            sx={{
              marginLeft: "16px",
              height: "fit-content",
            }}
          >
            {updatingBilling ? <CircularProgress size={24} /> : "Submit"}
          </Button>
        </Box>
      </Box>
      {/* {record.bulkPhase1 !== "Active" && ( */}
      <Box sx={{ mt: 3, width: "100%" }}>
        <Typography
          mb={2}
          sx={{ fontSize: 16, color: "#003d6b", fontWeight: "bold" }}
        >
          Comments ({comments.length})
        </Typography>
        <Box
          ref={boxRef}
          sx={{ maxHeight: "30vh", overflow: "auto", padding: "0 12px" }}
        >
          {comments.length !== 0 &&
            comments.map((comment: any) => {
              return (
                <Box
                  key={comment.createdAt}
                  sx={{ display: "flex", mb: 2, width: "100%" }}
                >
                  <Box sx={{ mr: 1 }}>
                    <ForumIcon sx={{ fontSize: 14, color: "grey" }} />
                  </Box>
                  <Box sx={{ width: "100%" }}>
                    <Box
                      sx={{ display: "flex", justifyContent: "space-between" }}
                    >
                      <Typography sx={{ fontSize: 14, fontWeight: "bold" }}>
                        {comment.createdBy}
                      </Typography>
                      {/* <Box sx={{ display: "flex"}}> */}
                      <Typography
                        sx={{
                          fontSize: 12,
                          color: "grey",
                        }}
                      >
                        {" "}
                        {moment(comment.createdAt).format("MM/DD/YYYY, h:mm a")}
                      </Typography>

                      {/* </Box> */}
                    </Box>
                    <Typography
                      sx={{
                        fontSize: 14,
                        textAlign: "justify",
                        wordBreak: "break-all",
                      }}
                    >
                      {comment.commentText}
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "flex-start",
                      padding: 0,
                      ml: 2,
                    }}
                  >
                    <Tooltip title="Delete">
                      <IconButton
                        aria-label="delete"
                        onClick={() => {
                          setDeleteComment({
                            comment: comment.commentText,
                            inCreatedBy: comment.createdBy,
                            storeId: comment.storeId,
                            tenantId: comment.tenantId,
                          });
                          setOpenConfirmDelete(true);
                        }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              );
            })}
        </Box>
        <Confirm
          isOpen={openConfirmDelete}
          //  loading={isLoading}
          title={`Delete comment`}
          content="Do you want to delete this comment?"
          onConfirm={handleConfirm}
          onClose={handleDialogClose}
        />
        {showCommentBox ? (
          <Box>
            <Form
              onSubmit={(values: any) => {
                handleSubmitComment(values, "Insert");
              }}
            >
              <TextInput
                source="comment"
                validate={[required(), maxLength(400)]}
                multiline
                rows={5}
                fullWidth
              />
              <SaveButton label="Save" />
              <Button
                color="primary"
                variant="contained"
                startIcon={<CancelIcon />}
                sx={{ m: 2 }}
                onClick={() => setShowCommentBox(false)}
              >
                Cancel
              </Button>
            </Form>
          </Box>
        ) : (
          <Button
            color="primary"
            variant="contained"
            sx={{ m: "8px 0px", padding: "4px 10px" }}
            startIcon={<AddCommentIcon />}
            onClick={() => setShowCommentBox(true)}
          >
            Add
          </Button>
        )}
      </Box>
      {/*  )} */}
      {record.bulkPhase1 !== "Active" && (
        <Box sx={{ mt: 3 }}>
          <Typography
            mb={2}
            sx={{ fontSize: 16, color: "#003d6b", fontWeight: "bold" }}
          >
            Status Log
          </Typography>
          <BulkLoadStatusLog
            phase="bulk_phase_1"
            phaseStatus={record.bulkPhase1}
          />{" "}
          <BulkLoadStatusLog
            phase="bulk_phase_2"
            phaseStatus={record.bulkPhase2}
          />{" "}
          <BulkLoadStatusLog
            phase="daily_load"
            phaseStatus={record.dailyLoad}
          />{" "}
        </Box>
      )}
    </Box>
  );
};

export default BulkLoading;
