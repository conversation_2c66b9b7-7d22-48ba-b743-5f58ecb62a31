import React from "react";
import { Box, Button, FormControlLabel, Radio, RadioGroup, Tooltip, useTheme } from "@mui/material";
import { AgGridReact } from "ag-grid-react";
import StoreGridDef from "./StoreGridDef";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { useNavigate } from "react-router-dom";
import { Constants } from "../utils/constants";
import useAllStoreList from "../CustomHooks/useAllStoreList";
const StoreList = () => {
  const { columnDefs, defaultColDef,  onBtnExport } =
    StoreGridDef();
    const {selectedStore, handleRadioChange, onGridReady, allStoresList} = useAllStoreList();
  const theme = useTheme();
  const navigate = useNavigate();
  const realmRole = JSON.parse(localStorage.getItem("role") || "");
  return (
    <Box sx={{ paddingX: "10px", mt: "15px", width: "100%" }}>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
      <RadioGroup
        sx={{ fontWeight: "bold" }}
        row
        aria-labelledby="demo-row-radio-buttons-group-label"
        name="row-radio-buttons-group"
        value={selectedStore}
        onChange={handleRadioChange}
      >
        <FormControlLabel
          value={"onboarding"}
          control={
            <Radio
              size="small"
              sx={{
                color: "#003d6b",
                "&.Mui-checked": {
                  color: "#003d6b",
                },
              }}
            />
          }
          label={"Currently Onboarding"}
        />
        <FormControlLabel
          value="launched"
          control={
            <Radio
              size="small"
              sx={{
                color: "#003d6b",
                "&.Mui-checked": {
                  color: "#003d6b",
                },
              }}
            />
          }
          label="Launched"
          sx={{fontSize: "12px"}}
        />
      </RadioGroup>

        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center",
          }}
        >
          {/* {realmRole === "superadmin" && */}
          <Button
            sx={{ textTransform: "none", mr: 2 }}
            onClick={() =>
              navigate("/statelessServiceBzoStoreDetails/AllDetails")
            }
          >
            All module details
          </Button>
          {/* } */}
          <Tooltip title="Export To Excel">
            <div>
              <FileDownloadIcon
                onClick={onBtnExport}
                style={{ color: theme.palette.primary.main }}
              />
            </div>
          </Tooltip>
        </div>
      </Box>

      <Box sx={{ width: "100%", marginTop: "15px" }}>
        <div
          className={Constants.ag_grid_theme}
          style={{ height: "85vh",marginTop: 2, maxWidth: '100vw' }}
        >
          <AgGridReact
            columnDefs={columnDefs}
            rowData={allStoresList}
            defaultColDef={defaultColDef}
            onGridReady={onGridReady}
            pagination={true}
          />
        </div>
      </Box>
    </Box>
  );
};

export default StoreList;
