import React, { useState, useEffect } from "react";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import { ListItemText, IconButton, Tooltip, DialogTitle } from "@mui/material";
import { makeStyles } from "@material-ui/styles";
import { getMenuPopUp } from "../../service/dataFetchQueries";
import { useRecordContext } from "react-admin";
import HighlightOffIcon from "@material-ui/icons/HighlightOff";

const useStyles = makeStyles((theme) => ({
  smallRadioButton: {
    "& svg": {
      width: "0.7em",
      height: "0.7em",
    },
    paddingLeft: 0,
    paddingRight: 0,
    height: 18,
    marginLeft: -2,
    backgroundColor: "transparent !important",
    width: 19,
    "@media (max-width: 1440px)": {
      width: "16px !important",
      marginLeft: -2,
    },
  },
  listItemText: {
    color: "rgb(0, 61, 107)",
    fontSize: 14,
    fontFamily: [ "Montserrat", "Roboto", "Helvetica", "Arial", "sans - serif"].join(","),
    padding: "7px 10px !important",
    marginBottom: -8,
    fontWeight: 500,
    maxWidth: 220,
  },
  loaderStore: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    paddingTop: "20% !important",
  },
}));

function MenuDialog(props: any) {
  const record: any = useRecordContext();
  const { openPopup, handlePopupClose, menuName, userEmail } = props;
  const classes = useStyles();
  const [openDialog, setOpenDialog] = useState(openPopup);
  const [loading, setLoading] = useState(true);
  const [menuPopUp, setMenuPopUp] = useState<any>([]);
  useEffect(() => {
    setOpenDialog(openPopup);
    if (menuName) {
      setMenuPopUp([]);
      setLoading(true);

      GetMenuPopUp(menuName);
    }
  }, [openPopup, menuName]);
  const handleClose = () => {
    setOpenDialog(false);
    handlePopupClose();
  };

  const GetMenuPopUp = (menuName: any) => {
    const input = {
      storeid: record.storeId,
      username: localStorage.getItem("userEmail"),
      inMenuname: menuName,
    };
    getMenuPopUp(input, record.realmName)
      .then((result: any) => {
        if (result) {
          setMenuPopUp(result);
          setLoading(false);
        } else {
          setLoading(false);
          setMenuPopUp([]);
        }
      })
      .catch((error: any) => {
        console.log(error);
      });
  };

  const NumberFormat = (number: any) => {
    var nf = new Intl.NumberFormat();
    var value = nf.format(number);
    return value;
  };

  return (
    <div>
      <Dialog
        open={openDialog}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          style: {
            width: "430px",
            marginLeft: "20px",
            marginTop: "28px",
            height: 270,
          },
        }}
      >
        <DialogTitle>
          <span
            style={{
              fontSize: 16,
              fontWeight: "bold",
              paddingLeft: "10px",
            }}
          >
            Menu Details
          </span>
          <Tooltip
            title="Close"
            style={{ position: "absolute", top: 0, right: 0 }}
          >
            <IconButton onClick={handleClose}>
              <HighlightOffIcon style={{ fontSize: 30 }} />
            </IconButton>
          </Tooltip>
        </DialogTitle>

        <DialogContent style={{ display: "flex", flexDirection: "row" }}>
          {menuPopUp.map((item: any, index: any) => {
            return (
              <>
                <div style={{ flex: 1 }}>
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={"Menu Name"}
                    style={{ fontWeight: 500 }}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={"Mileage Interval"}
                    style={{ fontWeight: 500 }}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={"Miles Before"}
                    style={{ fontWeight: 500 }}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={"Miles After"}
                    style={{ fontWeight: 500 }}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={"Maximum Miles"}
                    style={{ fontWeight: 500 }}
                  />
                </div>
                <div style={{ flex: 1 }}>
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={`:  ` + item.menuName}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={`:   ` + NumberFormat(item.milegaeInterval)}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={`:   ` + NumberFormat(item.beforeMiles)}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={`:   ` + NumberFormat(item.afterMiles)}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={`:   ` + NumberFormat(item.maxMiles)}
                  />
                </div>
              </>
            );
          })}
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default MenuDialog;
