import {
  Card,
  CardContent,
  Grid,
  Paper,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  fetchMenuNames,
  getFilteredOpcode,
  getMenuServiceType,
  loadMenuNames,
} from "../../service/dataFetchQueries";
import { useRecordContext, useTranslate } from "react-admin";
import { makeStyles } from "@material-ui/styles";
import MenuData from "./MenuData";
import clsx from "clsx";
import MenuDetails from "./MenuDetails";
import CreateMenu from "./CreateMenu";
import { addMenu, insertMenuDetails } from "../../service/mutations";
import OpenInNewOutlinedIcon from "@mui/icons-material/OpenInNewOutlined";
import MenuDialog from "./MenuDialog";
import SnackBarMessage from "../../components/SnackBarMessage";
import { Constants } from "../../utils/constants";

const useStyles = makeStyles((theme) => ({
  textContainerGrid: {
    // gap: 0,
    paddingLeft: 23,
  },
  mainLabel: {
    display: "flex",
    color: "rgb(0, 61, 107)",
    width: "77%",
    marginLeft: 58,
  },
  card: {
    marginLeft: "20px",
    height: "450px",
  },
  disabled: {
    opacity: 0.5,
    pointerEvents: "none",
    cursor: "not-allowed",
  },
  cards: {
    marginLeft: "20px",
    marginTop: "-5px",
    height: "500px",
  },
  label: {
    color: "rgb(0, 61, 107)",
    fontSize: 13,
    marginTop: "6px",
    width: "176px",
    paddingRight: 68,
    marginLeft: -19,
  },
  txt: {
    width: "200px",
    marginLeft: 40,
  },
  btnDiv: {
    width: "100px",
    marginLeft: 60,
  },
  btnDivMenu: {
    width: "150px",
    marginRight: 75,
    float: "inline-end",
    marginTop: 12,
    display: "flex",
  },
  labelPos: {
    textAlign: "right",
  },
  star: {
    color: "red",
    padding: "5px",
  },
  contentcred: {
    width: "100%",
    margin: "auto",
  },
  contentcredMenuData: {
    // width: '90%',
    margin: "auto",
    overflow: "auto",
    height: 450,
  },
  dialog: {
    position: "absolute",
    top: 50,
  },
  error: {
    margin: 0,
    fontSize: "90%",
    color: "tomato",
    marginTop: "20px",
    marginLeft: "15px",
  },
}));

export default function AvailableMenu() {
  const record: any = useRecordContext();
  const classes = useStyles();
  const [allMenu, setAllMenu] = useState([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [showMilege, setShowMilege] = useState(false);
  const [menuCreated, setMenuCreated] = useState(false);
  const [mSeries, setMSeries] = useState<any>([]);
  const [menuNames, setMenuNames] = useState<any>([]);
  const [filteredOpcode, setFilteredOpcode] = useState<any>([]);
  const [leftOpcode, setLeftOpcode] = useState<any>([]);
  const [rightOpcode, setRightOpcode] = useState<any>([]);
  const [menuServiceType, setMenuServiceType] = useState<any>([]);
  const [menuPopUp, setMenuPopUp] = useState<any>([]);
  const [category, setCategory] = React.useState([]);
  const [values, setValues] = React.useState([]);
  const [mfrh, setMfrh] = useState("");
  const [mintervalData, setMintervalData] = useState("");
  const [mitems, setMitems] = useState("");
  const [mname, setMname] = useState("");
  const [mprice, setMprice] = useState("");
  const [catArr, setCatArr] = useState([]);
  const [mServicetype, setMServicetype] = useState("");
  const [mOpcodes, setMOpcodes] = useState([]);
  const [milegeData, setMilegeData] = useState("");
  const [milegeDataInterval, setMilegeDataInterval] = useState([]);
  const [minteval, setmInteval] = useState([]);
  const [isDisabledMenu, setIsDisabledMenu] = useState(false);
  const [edit, setEdit] = useState(false);
  const [checkedInterval, setCheckedInterval] = useState<any>([]);
  const [selectedOpcode, setSelectedOpcode] = useState([]);
  const [assignedStatusValues, setAssignedStatusValues] = useState<any>([]);
  const [disableStatusValues, setDisableStatusValues] = useState<any>([]);
  const [sales, setSales] = React.useState(mprice ? mprice : "");
  const [frh, setFrh] = React.useState(mfrh ? mfrh : "");
  const [itemValue, setItemValue] = React.useState(mitems ? mitems : "");
  const [salesCheck, setSalesCheck] = React.useState(false);
  const [frhCheck, setFrhCheck] = React.useState(false);
  const [itemCheck, setItemCheck] = React.useState(false);
  const [intervalCheck, setIntervalCheck] = React.useState(false);
  const [categoryCheck, setCategoryCheck] = React.useState(false);
  const [checkOpcode, setCheckOpcode] = React.useState(false);
  const [openStore, setOpenStore] = React.useState(false);
  const [selectedMenu, setSelectedMenu] = React.useState("");
  const [openSnackbar, setOpenSnackbar] = React.useState(false);
  const [statusMessage, setStatusMessage] = React.useState("");
  const translate = useTranslate();
  const [statusMessageType, setStatusMessageType] = useState<any>("");
  const [errorSale, setErrorSale] = React.useState("");
  const [errorFrh, setErrorFrh] = React.useState("");
  const [errorItem, setErrorItem] = React.useState("");
  const [disabled, setDisabled] = React.useState(true);
  const [isAccordionCollapsed, setAccordionCollapsed] = React.useState(false); // State variable to track the collapse state of the menu interval accordion and control the visibility of the create interval form
  useEffect(() => {
    GetMenuNames();
  }, []);

  const GetMenuNames = async () => {
    try {
      setIsLoading(true);
      const res = await loadMenuNames(record.realmName, record.storeId);
      if (res) {
        var resultArr = res.sort(customSort);
        setAllMenu(resultArr);
      }
    } catch (error) {
      console.log(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  function customSort(a: any, b: any) {
    // Extract numeric part of menuName, default to Infinity if no numeric part
    let aNum = parseInt(a.menuName) || Infinity;
    let bNum = parseInt(b.menuName) || Infinity;

    // Compare numeric parts first
    if (aNum !== bNum) {
      return aNum - bNum;
    }

    // If numeric parts are equal, compare the full menuName strings
    return a.menuName.localeCompare(b.menuName);
  }

  const clickDetail = async (menuName: any, serviceType?: any) => {
    setShowMilege(true);
    try {
      const promises = [
        FetchMenuNames(menuName, serviceType),
        FtechFilteredOpcode(menuName),
        FetchMenuServiceType(menuName),
      ];
      await Promise.all(promises);
      console.log("All fetch operations completed.");
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const AddMenu = (formData: any) => {
    const input = {
      storeid: record.storeId,
      username: localStorage.getItem("userEmail"),
      inMaxMiles: formData.maximumMiles,
      inMenuname: formData.menuName,
      inMenuInterval: formData.mileageInterval,
      inMilesAfter: formData.milesAfter,
      inMilesBefore: formData.milesBefore,
      isdefault: formData.defaultMenu == true ? 1 : 0,
    };
    addMenu(input, record.realmName)
      .then((result: any) => {
        if (result) {
          setMenuCreated(true);
          GetMenuNames();
          const statusMessage =
            formData.menuName + `  Menu Created Successfully!`;
          setStatusMessage(statusMessage);
          setStatusMessageType("success");
          setOpenSnackbar(true);
        }
      })
      .catch((error: any) => {
        const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
        setStatusMessageType(Constants.statusType.error);
        setStatusMessage(statusMessage);
        setStatusMessageType("error");
        setOpenSnackbar(true);
        console.log(error);
      });
  };

  const FetchMenuNames = (menuName: any, serviceType: any) => {
    setIsDisabledMenu(true);
    setShowMilege(false);
    setMServicetype(serviceType);
    var serviceType: any = serviceType ? serviceType : 0;
    setEdit(serviceType == 0 ? false : true);
    const input = {
      storeid: record.storeId,
      username: localStorage.getItem("userEmail"),
      inMenuname: menuName,
      inServicetype: serviceType,
    };
    fetchMenuNames(input, record.realmName)
      .then((result: any) => {
        if (result[0]) {
          setMenuNames(result[0]);
          setCatArr(result[0].categoryList ? result[0].categoryList : []);
          setMfrh(result[0].mFrh == 0 ? "" : result[0].mFrh);
          setMintervalData(result[0].mInterval);
          setmInteval(result[0].mInterval);
          setMitems(result[0].mItems == 0 ? "" : result[0].mItems);
          setMname(result[0].mName);
          setMprice(result[0].mPrice == 0 ? "" : result[0].mPrice);
          setMServicetype(result[0].mServicetype);
          if (result[0].mOpcodes) {
            setMOpcodes(
              result[0].mOpcodes ? JSON.parse(result[0].mOpcodes) : []
            );
          } else {
            setMOpcodes([]);
          }
          setMSeries(JSON.parse(result[0].mSeries));
          setShowMilege(true);
        }
      })
      .catch((error: any) => {
        console.log(error);
      });
  };

  const FtechFilteredOpcode = (menuName: any) => {
    const input = {
      storeid: record.storeId,
      username: localStorage.getItem("userEmail"),
      inMenuname: menuName,
    };
    getFilteredOpcode(input, record.realmName)
      .then((result: any) => {
        if (result) {
          var roData = JSON.parse(result[0].opcodeList);
          setFilteredOpcode(roData);
          setLeftOpcode(roData);
        }
      })
      .catch((error: any) => {
        console.log(error);
      });
  };

  const FetchMenuServiceType = (menuName: any) => {
    const input = {
      storeid: record.storeId,
      username: localStorage.getItem("userEmail"),
    };
    getMenuServiceType(input, record.realmName)
      .then((result: any) => {
        if (result) {
          setMenuServiceType(result);
          const categoryValue = result.map((item: any) => {
            return item.id;
          });
          setValues(categoryValue);
          setCategory(result);
        }
      })
      .catch((error: any) => {
        console.log(error);
      });
  };

  const handleChangeSales = (e: any) => {
    if (isNaN(e.target.value)) {
      setErrorSale("Invalid float value. Please enter a valid number.");
      setSales("");
    } else {
      setDisabled(false);
      setErrorSale("");
      setSales(e.target.value);
    }
  };

  const handleChangeFrh = (e: any) => {
    if (isNaN(e.target.value)) {
      if (e.target.value == ".") {
        setErrorFrh("Invalid float value. Please enter a valid number.");
        setFrh("0.0");
      } else {
        setErrorFrh("Invalid float value. Please enter a valid number.");
        setFrh("");
      }
    } else {
      setDisabled(false);
      setErrorFrh("");
      setFrh(e.target.value);
    }
  };

  const handleChangeItem = (e: any) => {
    if (isNaN(e.target.value)) {
      setErrorItem("Invalid float value. Please enter a valid number.");
      setItemValue("");
    } else {
      setDisabled(false);
      setErrorItem("");
      setItemValue(e.target.value);
    }
  };

  const CreateInterval = () => {
    const userName = localStorage.getItem("userEmail");
    const storeIds = record.storeId;
    const numericPrice = parseFloat(mprice);
    const numericItems = parseFloat(itemValue);
    const generatedJSON: any = {};
    generatedJSON.menu_name = mname;
    generatedJSON.m_sales = parseFloat(sales);
    generatedJSON.m_frh = parseFloat(frh);
    generatedJSON.m_items = numericItems;
    generatedJSON.m_storeid = storeIds;
    generatedJSON.m_category = values;
    var opcodeObj = {};
    var opcodeArr: any = [];
    rightOpcode &&
      rightOpcode.map((item: any) => {
        if (item.opcode) {
          var opcodeObj = {
            opcode: item.opcode,
            category: values,
          };
          opcodeArr.push(opcodeObj);
        } else {
          var opcodeObj = {
            opcode: item,
            category: values,
          };
          opcodeArr.push(opcodeObj);
        }
      });
    var seriesObj = {};
    var seriesValueArr: any = [];
    var menuDataArr = [];
    if (checkedInterval == null || checkedInterval.length == 0) {
      var arrCheckd = assignedStatusValues.filter(
        (obj: any) => !disableStatusValues.includes(obj)
      );
      arrCheckd &&
        arrCheckd.map((item: any) => {
          var seriesObj = {
            series_freq: item,
            category: values,
          };
          seriesValueArr.push(seriesObj);
        });
    } else {
      checkedInterval &&
        checkedInterval.map((item: any) => {
          var seriesObj = {
            series_freq: item,
            category: values,
          };
          seriesValueArr.push(seriesObj);
        });
    }
    generatedJSON.opcodes = opcodeArr;
    generatedJSON.m_series = seriesValueArr;
    if (parseFloat(sales) > 0) {
    } else {
      setErrorSale("Please enter the sales value.");
      setSalesCheck(true);
    }
    if (parseFloat(frh) > 0) {
    } else {
      setErrorFrh("FRH value must be greater than 0");
      setFrhCheck(true);
    }
    if (numericItems > 0) {
    } else {
      setErrorItem("Items value must be greater than 0");
      setItemCheck(true);
    }
    if (parseFloat(sales) > 0 && parseFloat(frh) > 0 && numericItems > 0) {
      setSalesCheck(false);
      setFrhCheck(false);
      setItemCheck(false);
    }
    if (seriesValueArr.length > 0) {
      setIntervalCheck(false);
    } else {
      setIntervalCheck(true);
    }
    if (values) {
      setCategoryCheck(false);
    } else {
      setCategoryCheck(true);
    }

    if (opcodeArr.length > 0) {
      setCheckOpcode(false);
    } else {
      setCheckOpcode(true);
    }
    if (
      seriesValueArr.length > 0 &&
      opcodeArr.length > 0 &&
      parseFloat(sales) > 0 &&
      parseFloat(frh) > 0 &&
      numericItems > 0 &&
      values
    ) {
      const input = {
        menudata: JSON.stringify(generatedJSON),
        username: userName,
        storeid: storeIds,
      };
      insertMenuDetails(input, record.realmName)
        .then((result: any) => {
          if (result) {
            setMenuCreated(true);
            clickCancelMenu(true);
            GetMenuNames();
            const statusMessage = mname + `  Interval Saved Successfully!`;
            setStatusMessage(statusMessage);
            setStatusMessageType("success");
            setOpenSnackbar(true);
          }
        })
        .catch((error: any) => {
          const statusMessage = translate(
            "ERROR_MESSAGES.SOMETHING_WENT_WRONG"
          );
          setStatusMessageType(Constants.statusType.error);
          setStatusMessage(statusMessage);
          setStatusMessageType("error");
          setOpenSnackbar(true);
          console.log(error);
        });
    }
  };

  const saveInterval = (status: any) => {
    if (status == true) {
      setIsDisabledMenu(false);
    }
    GetMenuNames();
    setShowMilege(false);
  };

  const clickCancelMenu = (menuName: any) => {
    setCheckedInterval([]);
    setIsDisabledMenu(false);
    setShowMilege(false);
    setIntervalCheck(false);
    setCategoryCheck(false);
    setCheckOpcode(false);
    setErrorSale("");
    setErrorFrh("");
    setItemValue("");
    setErrorItem("");
  };

  const clickDeleteMenu = () => {
    setShowMilege(false);
    GetMenuNames();
  };

  const showMenuModal = (data: any, event?: any) => {
    event?.stopPropagation();
    setOpenStore(true);
    setSelectedMenu(data);
  };

  const handleCloseStore = () => {
    setOpenStore(false);
  };
  useEffect(() => {
    clickCancelMenu(true);
  }, [isAccordionCollapsed]);

  return (
    <Grid xs={12} className={classes.textContainerGrid} container>
      <Grid xs={7} sm={7} className={classes.textContainerGrid}>
        <Paper style={{ height: "100%", padding: 20, boxShadow: "none" }}>
          <Card
            sx={{ margin: "20px 0 0 0", height: "100%" }}
            className={classes.cards}
          >
            <Grid item xs={12}>
              <span style={{ display: "flex", width: "100%" }}>
                <Typography
                  style={{
                    fontWeight: "bold",
                    marginLeft: "44%",
                    marginTop: 5,
                  }}
                >
                  Available Menus
                </Typography>
              </span>
            </Grid>
            <CardContent
              className={`${showMilege ? classes.disabled : ""} ${
                classes.contentcredMenuData
              }`}
            >
              <MenuData
                allMenu={allMenu}
                clickDetail={clickDetail}
                isLoading={isLoading}
                clickDeleteMenu={clickDeleteMenu}
                showMenuModal={showMenuModal}
                openSnackbar={openSnackbar}
                setOpenSnackbar={setOpenSnackbar}
                statusMessage={statusMessage}
                setStatusMessage={setStatusMessage}
                statusMessageType={statusMessageType}
                setStatusMessageType={setStatusMessageType}
                isAccordionCollapsed={isAccordionCollapsed}
                GetMenuNames={GetMenuNames}
              />
            </CardContent>
          </Card>
        </Paper>
      </Grid>
      <Grid xs={4} sm={4} className={classes.textContainerGrid}>
        <Paper style={{ height: "100%", padding: 20, boxShadow: "none" }}>
          <Card
            sx={{ margin: "20px 0 0 0", height: "100%" }}
            className={classes.cards}
          >
            <CreateMenu
              AddMenu={AddMenu}
              allMenu={allMenu}
              setAccordionCollapsed={setAccordionCollapsed}
              GetMenuNames={GetMenuNames}
            />
          </Card>
        </Paper>
      </Grid>
      {showMilege && (
        <Grid xs={11} sm={11}>
          <Card
            sx={{
              width: "94.7%",
              marginLeft: "65px",
              marginTop: "0px",
            }}
          >
            <span
              style={{ display: "flex", marginTop: "15px", marginLeft: "15px" }}
            >
              <Typography
                style={{
                  padding: 8,
                  display: "flex",
                  color: "rgb(0, 61, 107)",
                  fontSize: "14px",
                }}
              >
                Mileage Interval - {menuNames?.mName}
                <div>
                  <Tooltip title="View Menu Details">
                    <OpenInNewOutlinedIcon
                      htmlColor="rgb(0, 61, 107)"
                      onClick={() =>
                        showMenuModal(menuNames?.mName ? menuNames?.mName : "")
                      }
                      style={{ fontSize: "14px" }}
                    />
                  </Tooltip>
                </div>
              </Typography>
            </span>
            <CardContent
              style={{
                width: "100%",
                margin: "auto",
              }}
            >
              <MenuDetails
                filteredOpcode={filteredOpcode}
                menuServiceType={menuServiceType}
                leftOpcode={leftOpcode}
                rightOpcode={rightOpcode}
                setRightOpcode={setRightOpcode}
                setLeftOpcode={setLeftOpcode}
                category={category}
                values={values}
                setValues={setValues}
                mSeries={mSeries}
                CreateInterval={CreateInterval}
                milegeData={milegeData}
                milegeDataInterval={milegeDataInterval}
                clickDetail={clickDetail}
                saveInterval={saveInterval}
                mInterval={minteval}
                mfrh={mfrh}
                mintervalData={mintervalData}
                mitems={mitems}
                mname={mname}
                mprice={mprice}
                mServicetype={mServicetype}
                mOpcodes={mOpcodes}
                isLoading={isLoading}
                catArr={catArr}
                edit={edit}
                clickCancelMenu={clickCancelMenu}
                checkedInterval={checkedInterval}
                setCheckedInterval={setCheckedInterval}
                assignedStatusValues={assignedStatusValues}
                setAssignedStatusValues={setAssignedStatusValues}
                disableStatusValues={disableStatusValues}
                setDisableStatusValues={setDisableStatusValues}
                checkOpcode={checkOpcode}
                setCheckOpcode={setCheckOpcode}
                frh={frh}
                setFrh={setFrh}
                sales={sales}
                setSales={setSales}
                itemValue={itemValue}
                setItemValue={setItemValue}
                salesCheck={salesCheck} // Pass salesCheck as a prop
                frhCheck={frhCheck} // Pass frhCheck as a prop
                itemCheck={itemCheck} // Pass itemCheck as a prop
                intervalCheck={intervalCheck} // Pass intervalCheck as a prop
                categoryCheck={categoryCheck} // Pass categoryCheck as a prop
                setSelectedOpcode={setSelectedOpcode}
                handleChangeItem={handleChangeItem}
                handleChangeFrh={handleChangeFrh}
                handleChangeSales={handleChangeSales}
                errorSale={errorSale}
                errorFrh={errorFrh}
                errorItem={errorItem}
              />
            </CardContent>
          </Card>
        </Grid>
      )}
      <MenuDialog
        openPopup={openStore}
        handlePopupClose={handleCloseStore}
        menuName={selectedMenu}
      ></MenuDialog>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={statusMessageType}
      />
    </Grid>
  );
}
