import React from "react";
import { makeStyles } from "@material-ui/core/styles";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Button,
  Card,
  CardContent,
  Checkbox,
  CircularProgress,
  Grid,
  Paper,
  Tooltip,
  Typography,
} from "@mui/material";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import DeleteIcon from "@material-ui/icons/DeleteOutline";
import EditIcon from "@mui/icons-material/Edit";
import OpenInNewOutlinedIcon from "@mui/icons-material/OpenInNewOutlined";

import clsx from "clsx";
import { loadMenuDetails } from "../../service/dataFetchQueries";
import { useRecordContext, useTranslate } from "react-admin";
import { deleteInterval, deleteMenu, editMenu } from "../../service/mutations";
import MenuDialog from "./MenuDialog";

import { useRef, useState, useEffect } from "react";
import Box from "@mui/material/Box";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import { DialogTitle } from "@mui/material";
import { Constants } from "../../utils/constants";

export default function MenuData(props: any) {
  const useStyles = makeStyles((theme) => ({
    checked: {
      "&.Material-ui-checked": {
        backgroundColor: "red",
      },
    },
    root: {
      width: "100%",
    },
    noData: {
      display: "flex",
      color: "rgb(0, 61, 107)",
      justifyContent: "center",
      fontSize: 13,
    },
    loaderGrid: {
      height: 300,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
    },
    loaderGridInner: {
      height: 50,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
    },
    headingSub: {
      fontSize: theme.typography.pxToRem(13),
      flexBasis: "19.33%",
      flexShrink: 0,
      color: "rgb(0, 61, 107)",
      width: "17.8%",
      fontWeight: 550,
    },
    headingSubData: {
      fontSize: theme.typography.pxToRem(13),
      flexBasis: "19.33%",
      flexShrink: 0,
      // color: 'rgb(0, 61, 107)',
      width: "17.8%",
    },
    headingSub1: {
      fontSize: theme.typography.pxToRem(15),
      flexBasis: "99.33%",
      flexShrink: 0,
      color: "rgb(0, 61, 107)",
    },
    headingSub2: {
      fontSize: theme.typography.pxToRem(15),
      flexBasis: "99.33%",
      flexShrink: 0,
      color: "rgb(0, 61, 107)",
    },
    heading: {
      fontSize: theme.typography.pxToRem(13),
      flexBasis: "61.33%",
      flexShrink: 0,
      color: "rgb(0, 61, 107)",
      fontWeight: 550,
    },
    secondaryHeading: {
      fontSize: theme.typography.pxToRem(15),
      color: theme.palette.text.secondary,
    },
    frhValue: {
      fontSize: theme.typography.pxToRem(15),
      flexBasis: "20.33%",
      flexShrink: 0,
      marginLeft: 11,
    },
    category: {
      fontSize: theme.typography.pxToRem(15),
      color: theme.palette.text.secondary,
      marginLeft: -16,
    },
    accordDet: {
      marginTop: 5,
    },
    accordDetail: {
      marginTop: 5,
      width: "100%",
    },
    btnDivMenu: {
      fontSize: 12,
    },
  }));

  const classes = useStyles();
  const record: any = useRecordContext();
  const [expanded, setExpanded] = React.useState(false);
  const [jsonGeneralData, setJsonGeneralData] = React.useState([]);
  const [selectedTab, setSelectedTab] = React.useState<any>();
  const [intervalList, setIntervalList] = React.useState([]);
  const [jsonGeneral, setJsonGeneral] = React.useState([]);
  const [jsonOpcodeList, setJsonOpcodeList] = React.useState([]);
  const [isLoading, setIsLoading] = React.useState(true);
  const [innerLoading, setInnerLoading] = React.useState(false);
  const [menu, setMenu] = React.useState("");
  const [openDelete, setOpenDelete] = React.useState(false);
  const [openDelInt, setOpenDelInt] = React.useState(false);
  const [menuIntServTyp, setMenuIntServTyp] = React.useState("");
  const [menuIntName, setMenuIntName] = React.useState("");
  const [selectedMenu, setSelectedMenu] = React.useState("");
  const translate = useTranslate();

  const [openStore, setOpenStore] = React.useState(false);

  const handleChange = (panel: any) => (event: any, isExpanded: any) => {
    setJsonGeneralData([]);
    setIsLoading(false);
    // setFrh('');
    // setItems('');
    // setPrice('');
    // setMenu_name('');
    // setService_type_id('');
    // setMilege_interval('');
    setJsonOpcodeList([]);
    setIntervalList([]);
    setExpanded(isExpanded ? panel : false);
    if (panel.menuName) {
      GetMenuDetails(panel);
    }
  };

  const GetMenuDetails = async (panel: any) => {
    setInnerLoading(true);
    try {
      const res = await loadMenuDetails(
        panel.menuName,
        record.realmName,
        record.storeId
      );
      if (res) {
        if (res[0]) {
          let jsonGeneralData = JSON.parse(
            res[0]?.jsonGeneral ? res[0].jsonGeneral : []
          );
          if (res[0]?.intervalList) {
            setIntervalList(JSON.parse(res[0].intervalList));
          }
          setIntervalList(
            res[0]?.intervalList ? JSON.parse(res[0].intervalList) : []
          );
          setJsonGeneral(
            res[0]?.jsonGeneral ? JSON.parse(res[0].jsonGeneral) : []
          );
          setJsonOpcodeList(
            res[0]?.jsonOpcodeList ? JSON.parse(res[0].jsonOpcodeList) : []
          );
          setJsonGeneralData(jsonGeneralData ? jsonGeneralData : []);
          // setIsLoading(true);
          // if (jsonGeneralData[0]) {
          //   setFrh(jsonGeneralData[0].frh);
          //   setItems(jsonGeneralData[0].items);
          //   setPrice(jsonGeneralData[0].price);
          //   setMenu_name(jsonGeneralData[0].menu_name);
          //   setService_type_id(jsonGeneralData[0].service_type_id);
          //   setMilege_interval(jsonGeneralData[0].milegae_interval);
          // }
        }
      }
    } catch (error) {
      console.log(error);
      throw error;
    } finally {
      setInnerLoading(false); // Stop loading indicator
    }
  };

  const DeleteMenu = (
    menuNmae: any,
    menuIsdefault: any,
    menuLength: any,
    event: any
  ) => {
    event.stopPropagation();
    setExpanded(expanded);
    if (menuIsdefault == "1" && menuLength != 1) {
      const statusMessage = "You cannot delete the default menu.";
      props.setStatusMessage(statusMessage);
      props.setStatusMessageType("warning");
      props.setOpenSnackbar(true);
    } else {
      setMenu(menuNmae);
      setOpenDelete(true);
      setSelectedTab("");
    }
  };

  const handleCheckboxChange = (event: any, menuIsdefault: any) => {
    setExpanded(expanded);
    event.stopPropagation();
    EditMenu(event, menuIsdefault);
  };

  const handleCreateIntervalClick = (event: any, menuName: any) => {
    event.stopPropagation();
    props.clickDetail(menuName);
  };

  const DeleteInterval = (
    menuNmae: any,
    serviceType: any,
    milegae_interval: any,
    event: any
  ) => {
    setOpenDelete(true);
    setOpenDelInt(true);
    setSelectedTab("inner");

    setOpenDelInt(true);
    setMenuIntName(menuNmae);
    setMenuIntServTyp(serviceType);
  };

  const confirmDelete = () => {
    handleDeleteMenu();
  };

  const handleDeleteMenu = () => {
    setOpenDelete(false);
    setOpenDelInt(false);
    const input = {
      storeid: record.storeId,
      username: localStorage.getItem("userEmail"),
      inMenuname: menu,
    };

    deleteMenu(input, record.realmName)
      .then((result: any) => {
        if (result) {
          props.clickDeleteMenu();
          const statusMessage = menu + ` Menu Deleted`;
          props.setStatusMessage(statusMessage);
          props.setStatusMessageType("success");
          props.setOpenSnackbar(true);
        }
      })
      .catch((error: any) => {
        const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
        props.setStatusMessageType(Constants.statusType.error);
        props.setStatusMessage(statusMessage);
        props.setStatusMessageType("error");
        props.setOpenSnackbar(true);
        console.log(error);
      });
  };

  const confirmDeleteInt = () => {
    handleDeleteMenuInt();
  };

  const handleDeleteMenuInt = () => {
    setOpenDelInt(false);
    const input = {
      storeid: record.storeId,
      username: localStorage.getItem("userEmail"),
      inMenuname: menuIntName,
      serviceType: menuIntServTyp,
    };

    deleteInterval(input, record.realmName)
      .then((result: any) => {
        if (result) {
          props.clickDeleteMenu();
          const statusMessage = menu + ` Menu Interval Deleted`;
          props.setStatusMessage(statusMessage);
          props.setStatusMessageType("success");
          props.setOpenSnackbar(true);
        }
      })
      .catch((error: any) => {
        const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
        props.setStatusMessageType(Constants.statusType.error);
        props.setStatusMessage(statusMessage);
        props.setStatusMessageType("error");
        props.setOpenSnackbar(true);
        console.log(error);
      });
  };

  const handleCloseDelete = () => {
    setOpenDelete(false);
    setOpenDelInt(false);
  };

  const clickEdit = (menuNmae: any, serviceType: any) => {
    // setExpanded(true);
    props.clickDetail(menuNmae, serviceType);
  };

  const showMenuModal = (data: any, event: any) => {
    props.showMenuModal(data, event);
  };

  const EditMenu = (event: any, menuIsdefault: any) => {
    let menuName = event.target.id;
    let isdefault = event.target.value == false ? 1 : 0;
    if (menuIsdefault == 0) {
      const input = {
        storeid: record.storeId,
        username: localStorage.getItem("userEmail"),
        inMenuname: menuName,
        isdefault: isdefault,
      };
      editMenu(input, record.realmName)
        .then((result: any) => {
          if (result) {
            props.clickDeleteMenu();
            const statusMessage = "Default Menu Updated";
            props.setStatusMessage(statusMessage);
            props.setStatusMessageType("success");
            props.setOpenSnackbar(true);
          }
        })
        .catch((error: any) => {
          const statusMessage = translate(
            "ERROR_MESSAGES.SOMETHING_WENT_WRONG"
          );
          props.setStatusMessageType(Constants.statusType.error);
          props.setStatusMessage(statusMessage);
          props.setStatusMessageType("error");
          props.setOpenSnackbar(true);
          console.log(error);
        });
    }
  };

  useEffect(() => {
    setExpanded(false);
  }, [props.isAccordionCollapsed]);

  return (
    <div className={classes.root}>
      {props.isLoading == true ? (
        <Grid
          className={classes.loaderGrid}
          style={{ justifyContent: "center" }}
        >
          <CircularProgress size={60} />
        </Grid>
      ) : props.allMenu.length > 0 && props.isLoading == false ? (
        props.allMenu.map((item: any, index: any) => (
          <Accordion expanded={expanded === item} onChange={handleChange(item)}>
            <AccordionSummary
              aria-controls="panel4bh-content"
              id="panel4bh-header"
              style={{
                display: "flex",
                alignItems: "center",
                position: "relative",
              }}
            >
              <ExpandMoreIcon />
              <Typography className={classes.heading} >
                {item.menuName}
                <Tooltip title="View Menu Details">
                  <OpenInNewOutlinedIcon
                    htmlColor="rgb(0, 61, 107)"
                    style={{
                      borderRadius: "3px",
                      width: "17px",
                      height: "17px",
                      border: 0,
                      fontSize: "12px",
                      cursor: "pointer",
                      lineHeight: "13px",
                      marginLeft: 5,
                      paddingTop: '4px'
                     
                    }}
                    onClick={(event: any) =>
                      showMenuModal(item.menuName ? item.menuName : "", event)
                    }
                  />
                </Tooltip>
              </Typography>
              <Tooltip title="Set as Default Menu">
                <Checkbox
                  onChange={(event) =>
                    handleCheckboxChange(event, item.menuIsdefault)
                  }
                  checked={item.menuIsdefault == "1" ? true : false}
                  value={item.menuIsdefault == "1" ? true : false}
                  id={item.menuName}
                  style={{ cursor: "pointer", height: 20, marginRight: 8 }}
                  size="small"
                />
              </Tooltip>
              <Button
                variant="contained"
                className={clsx("reset-btn", classes.btnDivMenu)}
                color="primary"
                value={item.menuName}
                disabled={item.menuEnable == 0 ? true : false}
                style={{
                  height: 24,
                  fontSize: 12,
                  marginLeft: 4,
                  textTransform: "none",
                }}
                onClick={(e: any) =>
                  handleCreateIntervalClick(e, item.menuName)
                }
              >
                Create Interval
              </Button>

              <Tooltip title="Delete Menu">
                <DeleteIcon
                  //  value={item.menuName}
                  onClick={(e: any) =>
                    DeleteMenu(
                      item.menuName,
                      item.menuIsdefault,
                      props.allMenu.length,
                      e
                    )
                  }
                  style={{
                    background: "#384163",
                    color: "#fff",
                    borderRadius: "3px",
                    width: "30px",
                    height: "24px",
                    border: 0,
                    fontSize: "12px",
                    cursor: "pointer",
                    lineHeight: "13px",
                    marginLeft: 6,
                  }}
                />
              </Tooltip>
            </AccordionSummary>

            <AccordionDetails className={classes.accordDetail}>
              <Accordion
                style={{ width: "100%", height: "auto", marginTop: 5 }}
              >
                <AccordionSummary
                  aria-controls="panel4bh-content"
                  id="panel4bh-header2"
                >
                  <div style={{ width: "100%", marginTop: 30 }}>
                    {innerLoading == true ? (
                      <Grid
                        className={classes.loaderGridInner}
                        style={{ justifyContent: "center" }}
                      >
                        <CircularProgress size={40} />
                      </Grid>
                    ) : jsonGeneralData.length > 0 && innerLoading == false ? (
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <Typography
                          variant="subtitle1"
                          className={classes.headingSub}
                        >
                          <span>Miles</span>
                        </Typography>
                        <Typography
                          variant="subtitle1"
                          className={classes.headingSub}
                        >
                          Category
                        </Typography>
                        <Typography
                          variant="subtitle1"
                          className={classes.headingSub}
                        >
                          $ Sales
                        </Typography>
                        <Typography
                          variant="subtitle1"
                          className={classes.headingSub}
                        >
                          Flat Rate Hours
                        </Typography>
                        <Typography
                          variant="subtitle1"
                          className={classes.headingSub}
                        >
                          Items
                        </Typography>
                      </div>
                    ) : (
                      <Typography
                        // variant="subtitle1"
                        style={{ textAlign: "center", fontSize: 14 }}
                      >
                        No Intervals Created
                      </Typography>
                    )}
                    {jsonGeneralData.map((item: any, index) => (
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <Typography
                          className={classes.headingSubData}
                          style={{ marginBottom: "30px" }}
                        >
                          {item.milegae_interval}
                        </Typography>
                        <Typography
                          className={classes.headingSubData}
                          style={{ marginBottom: "30px" }}
                        >
                          {item.service_type}
                        </Typography>
                        <Typography
                          className={classes.headingSubData}
                          style={{ marginBottom: "30px" }}
                        >
                          ${item.price}
                        </Typography>
                        <Typography
                          className={classes.headingSubData}
                          style={{ marginBottom: "30px" }}
                        >
                          {item.frh}
                        </Typography>
                        <Typography
                          className={classes.headingSubData}
                          style={{ marginBottom: "30px" }}
                        >
                          {item.items}
                        </Typography>

                        <Tooltip title="Edit">
                          <EditIcon
                            style={{
                              background: "#384163",
                              color: "#fff",
                              borderRadius: "3px",
                              width: "24px", // Adjusted width
                              height: "20px", // Adjusted height
                              border: 0,
                              fontSize: "10px", // Adjusted font size
                              cursor: "pointer",
                              lineHeight: "11px", // Adjusted line height
                              marginLeft: -25, // Adjusted left margin
                              marginBottom: "30px", // Vertical spacing
                              marginRight: "10px", // Added right margin
                            }}
                            onClick={() =>
                              clickEdit(item.menu_name, item.service_type_id)
                            }
                          />
                        </Tooltip>

                        <Tooltip title="Delete Interval">
                          <DeleteIcon
                            style={{
                              background: "#384163",
                              color: "#fff",
                              borderRadius: "3px",
                              width: "24px", // Adjusted width
                              height: "20px", // Adjusted height
                              border: 0,
                              fontSize: "10px", // Adjusted font size
                              cursor: "pointer",
                              lineHeight: "11px", // Adjusted line height
                              marginLeft: -4, // Adjusted left margin
                              marginBottom: "30px", // Vertical spacing
                              marginRight: "10px", // Added right margin
                            }}
                            onClick={(e: any) =>
                              DeleteInterval(
                                item.menu_name,
                                item.service_type_id,
                                item.milegae_interval,
                                e
                              )
                            }
                          />
                        </Tooltip>
                      </div>
                    ))}
                  </div>
                </AccordionSummary>
              </Accordion>
            </AccordionDetails>
          </Accordion>
        ))
      ) : (
        <Typography className={clsx(classes.noData)}>
          No Menus Available
        </Typography>
      )}

      <DeleteDialog
        selectedTab={selectedTab}
        openDelInt={openDelInt}
        openPopup={openDelete}
        menuName={menu}
        handlePopupClose={handleCloseDelete}
        handleDeleteMenu={confirmDelete}
        confirmDeleteInt={confirmDeleteInt}
      />
    </div>
  );
}

const DeleteDialog = (props: any) => {
  const {
    openDelInt,
    openPopup,
    menuName,
    handlePopupClose,
    handleDeleteMenu,
    confirmDeleteInt,
    selectedTab,
  } = props;
  const [openDialog, setOpenDialog] = useState(openPopup);
  const [openInDialog, setOpenInDialog] = useState(openDelInt);
  const [selected, setSelected] = useState("Both");
  var displaymsg: string;

  useEffect(() => {
    setOpenDialog(openPopup);
    setOpenInDialog(openDelInt);
  }, [openPopup, openDelInt]);

  const handleClose = () => {
    setOpenDialog(false);
    setOpenInDialog(false);
    handlePopupClose();
  };

  if (menuName && selectedTab != "inner") {
    displaymsg =
      "Are you sure you want to permanently delete the selected menu and its corresponding model mapping?";
  } else {
    displaymsg =
      "Are you sure, you want to permanently delete the selected Interval?";
  }

  const handleDelete = () => {
    // handleSelected(selected);
    if (selectedTab == "inner") {
      setOpenInDialog(false);
      confirmDeleteInt();
    } else {
      setOpenDialog(false);
      handleDeleteMenu();
    }
  };

  return (
    <div>
      <Dialog
        open={selectedTab == "inner" ? openInDialog : openDialog}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {selectedTab == "inner" ? "Remove Interval?" : "Remove Menu?"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            {displaymsg}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleDelete} autoFocus>
            Ok
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};
