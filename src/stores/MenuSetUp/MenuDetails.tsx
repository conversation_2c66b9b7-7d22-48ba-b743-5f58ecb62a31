import React, { useEffect, useState } from "react";
import { makeStyles } from "@material-ui/styles";
import OpenInNewOutlinedIcon from "@mui/icons-material/OpenInNewOutlined";
import clsx from "clsx";
import {
  TextField,
  Button,
  Grid,
  Card,
  CardContent,
  Typography,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  InputLabel,
  Paper,
  Checkbox,
  Dialog,
  Tooltip,
  Input,
} from "@mui/material";
import MenuOpcode from "./MenuOpcode";
const useStyles = makeStyles((theme) => ({
  textContainerGrid: {
    // display: 'flex',
    gap: 10,
    marginBottom: 5,
    paddingBottom: 5,
  },
  mainLabel: {
    display: "flex",
    color: "rgb(0, 61, 107)",
  },

  card: {
    width: "91.2%",
    marginLeft: "11px",
    marginTop: "28px",
    height: "850px",
  },
  label: {
    color: "rgb(0, 61, 107)",
    fontSize: 13,
    marginTop: "16px",
    width: "100px",
  },
  txt: {
    width: "200px",
    // marginLeft: 15
    marginLeft: 30,
  },
  btnDiv: {
    width: "100px",
    marginLeft: 1,
    marginTop: 20,
  },
  labelPos: {
    textAlign: "right",
  },
  star: {
    color: "red",
    padding: "5px",
  },
  contentcred: {
    width: "100%",
    margin: "auto",
  },
  dialog: {
    position: "absolute",
    top: 50,
  },
  error: {
    margin: 0,
    fontSize: "90%",
    color: "tomato",
    marginTop: "20px",
    marginLeft: "15px",
  },
  rolelabel: {
    padding: "8px 0",
    color: "#001837",
    fontWeight: 500,
    // color: 'rgb(0, 61, 107)'
  },
  TextField: {
    marginTop: 12,
    "& label": {
      color: "#212121 !important",
    },
    "& .MuiOutlinedInput-root": {
      "& fieldset": {
        borderColor: "#7575753d",
        borderRadius: 0,
      },
      "&:hover fieldset": {
        borderColor: "#7575753d",
        borderRadius: 0,
      },
      "&.Mui-focused fieldset": {
        borderColor: "#7575753d",
        borderRadius: 0,
      },
    },
  },
}));
export default function MenuDetails(props: any) {
  const classes = useStyles();
  const {
    // menuNames,
    filteredOpcode,
    menuServiceType,
    // menuPopUp,
    leftOpcode,
    rightOpcode,
    setRightOpcode,
    setLeftOpcode,
    category,
    values,
    setValues,
    CreateInterval,

    // new Props

    milegeData,
    milegeDataInterval,
    clickDetail,
    saveInterval,
    mInterval,
    mfrh,
    mintervalData,
    mitems,
    mname,
    mprice,
    mServicetype,
    mOpcodes,
    isLoading,
    catArr,
    edit,
    clickCancelMenu,
    checkedInterval,
    setCheckedInterval,
    assignedStatusValues,
    setAssignedStatusValues,
    disableStatusValues,
    setDisableStatusValues,
    checkOpcode,
    setCheckOpcode,
    frh,
    setFrh,
    sales,
    setSales,
    itemValue,
    setItemValue,
    salesCheck,
    frhCheck,
    itemCheck,
    intervalCheck,
    categoryCheck,
    // setOpcodeData,
    setSelectedOpcode,
    errorSale,
    errorFrh,
    errorItem,
    handleChangeSales,
    handleChangeFrh,
    handleChangeItem,
  } = props;

  const [seriesArr, setSeriesArr] = useState([]);
  const [checkedItems, setCheckedItems] = useState([]);

  const handleChange = (event: any) => {
    let check: any = parseInt(event.target.value);
    setValues(check);
  };

  useEffect(() => {
    if (props.mSeries && props.mSeries.length > 0) {
      const filteredAssignedStatusValues = props.mSeries
        .filter((item: any) => item.assigned_status === 1)
        .map((item: any) => item.ratings);
      setAssignedStatusValues(filteredAssignedStatusValues);
      const FilterDisables = props.mSeries
        .filter(
          (item: any) =>
            item.assigned_status === 1 &&
            props.mServicetype !== item.service_type
        )
        .map((item: any) => item.ratings);
      setDisableStatusValues(FilterDisables);
      setAssignedStatusValues(filteredAssignedStatusValues);
    }
  }, [props.mSeries]);

  useEffect(() => {
    let seriesArr = [];
    if (props.mSeries) {
      seriesArr = props.mSeries.filter((item: any) => item.ratings != null);
      setSeriesArr(seriesArr);
    }
  }, [props.mSeries]);

  useEffect(() => {
    window.scrollBy(0, 850);
  }, []);

  useEffect(() => {
    if (props.catArr && props.catArr.length > 0) {
      let i = 0;
      category.map((cat: any, catIndex: any) => {
        if (props.edit == false) {
          if (props.catArr.includes(cat.id)) {
          } else {
            if (i == 0) {
              i++;
              return setValues(cat.id);
            }
          }
        }
      });
    }
  }, [props.catArr, category]);

  useEffect(() => {
    setSales(props.mprice);
    setFrh(props.mfrh);
    setItemValue(props.mitems);
    setValues(parseInt(props.mServicetype));
    let seriesArr = [];
    let selOpcode = [];
    if (props.mSeries) {
      seriesArr = props.mSeries.filter((item: any) => item.ratings != null);
      setSeriesArr(seriesArr);
    }
    if (props.mSeries) {
      seriesArr = props.mSeries.filter((item: any) => item.ratings != null);
      setSeriesArr(seriesArr);
    }

    if (props.mOpcodes) {
      const correctedArray = props.mOpcodes.map((item: any) => ({
        opcode: item.menu_opcode,
        opcodedescription: item.opcodedescription,
        service_type: item.service_type,
      }));
      setRightOpcode(correctedArray);
    } else {
      setRightOpcode([]);
    }
  }, [props.mprice, props.mSeries]);

  const handleCheckboxChange = (colIndex: any, column: any) => {
    var assignStatus = assignedStatusValues;
    var arr = assignedStatusValues;
    if (
      typeof assignStatus != "undefined" &&
      assignStatus.some(function (el: any) {
        return el == column.ratings;
      })
    ) {
      arr = assignStatus.filter(function (obj: any) {
        return obj != column.ratings;
      });
    } else {
      arr.push(column.ratings);
    }
    var arrCheck = [];
    arrCheck = arr.filter((obj: any) => !disableStatusValues.includes(obj));
    setAssignedStatusValues(arr);
    setCheckedInterval(arrCheck);
    const newCheckedItems: any = [...checkedItems];
    newCheckedItems[colIndex] = !newCheckedItems[colIndex];
    setCheckedItems(newCheckedItems);
  };

  const CancelMenu = (menuName: any) => {
    props.clickCancelMenu(true);
  };
  return (
    <Grid container spacing={2}>
      <Grid item xs={6}>
        <Paper
          style={{
            height: "100%",
            padding: "0px 0px 0px 20px",
            boxShadow: "none",
          }}
        >
          <Typography
            style={{
              textDecoration: "underline",
              fontSize: "14px",
              marginLeft: "-17px",
              color: "#001837",
              fontWeight: 500,
            }}
          >
            Miles
          </Typography>
          <Typography
            style={{
              fontSize: "14px",
              marginLeft: "-17px",
              color: "#001837",
              fontWeight: 500,
            }}
          >
            {mInterval}
          </Typography>
          <Grid
            container
            spacing={2}
            style={{ padding: "12px", marginLeft: "-38px" }}
          >
            <Grid
              item
              style={{
                width: "90px",
                paddingTop: "6px",
                paddingLeft: "5px",
              }}
            >
              <InputLabel shrink={false} htmlFor={"sales"}>
                <Typography className={classes.label}>
                  $ Sales<span style={{ color: "red" }}>*</span>
                </Typography>
              </InputLabel>{" "}
            </Grid>
            <Grid
              item
              style={{
                width: "125px",
                paddingTop: "6px",
                paddingLeft: "25px",
              }}
            >
              <InputLabel shrink={false} htmlFor={"frh"}>
                <Typography className={classes.label}>
                  Flat Rate Hours
                  <span style={{ color: "red" }}>*</span>
                </Typography>
              </InputLabel>{" "}
            </Grid>
            <Grid
              item
              style={{
                width: "90px",
                paddingTop: "6px",
                paddingLeft: "10px",
              }}
            >
              <InputLabel shrink={false} htmlFor={"item"}>
                <Typography className={classes.label}>
                  Items<span style={{ color: "red" }}>*</span>
                </Typography>
              </InputLabel>
            </Grid>
          </Grid>

          <Grid
            container
            spacing={2}
            style={{
              padding: "12px",
              marginTop: "-40px",
              marginLeft: "-39px",
            }}
          >
            <Grid
              item
              style={{
                width: "78px",
                paddingTop: "6px",
                paddingLeft: "6px",
              }}
            >
              <TextField
                required
                variant="outlined"
                size="small"
                id="sales"
                margin="normal"
                InputLabelProps={{
                  shrink: true,
                }}
                InputProps={{
                  inputProps: {
                    maxLength: 9, // 6 digits + 1 decimal point + 2 digits
                    pattern: "\\d{1,6}(\\.\\d{0,2})?", // regex pattern for validation
                  },
                }}
                name="sales"
                autoComplete="sales"
                classes={{ root: classes.TextField }}
                value={sales}
                onChange={handleChangeSales}
                style={{
                  width: "110px",
                }}
              />
            </Grid>
            <Grid
              item
              style={{
                width: "78px",
                paddingTop: "6px",
                paddingLeft: "37px",
              }}
            >
              <TextField
                id="frh"
                margin="normal"
                InputLabelProps={{
                  shrink: true,
                }}
                classes={{ root: classes.TextField }}
                InputProps={{
                  inputProps: {
                    maxLength: 9, // 6 digits + 1 decimal point + 2 digits
                    pattern: "\\d{1,6}(\\.\\d{0,2})?", // regex pattern for validation
                  },
                }}
                name="frh"
                autoComplete="frh"
                value={frh}
                variant="outlined"
                onChange={handleChangeFrh}
                style={{ width: "110px", height: "40px" }}
                required
                size="small"
              />
            </Grid>
            <Grid
              item
              style={{
                width: "78px",
                paddingTop: "6px",
                paddingLeft: "68px",
              }}
            >
              <TextField
                id="item"
                margin="normal"
                InputLabelProps={{
                  shrink: true,
                }}
                classes={{ root: classes.TextField }}
                InputProps={{
                  inputProps: {
                    maxLength: 9, // 6 digits + 1 decimal point + 2 digits
                    pattern: "\\d{1,6}(\\.\\d{0,2})?", // regex pattern for validation
                  },
                }}
                name="item"
                autoComplete="item"
                value={itemValue}
                variant="outlined"
                onChange={handleChangeItem}
                style={{ width: "110px", height: "40px" }}
                required
                size="small"
              />
            </Grid>
          </Grid>
          <Grid
            item
            style={{
              marginLeft: "-22px",
              marginTop: "-20px",
            }}
          >
            <InputLabel shrink={false} htmlFor={"item"}>
              <Typography
                style={{
                  color: "red",
                  fontSize: 11,
                }}
              >
                {errorSale
                  ? errorSale
                  : errorFrh
                  ? errorFrh
                  : errorItem
                  ? errorItem
                  : ""}
              </Typography>
            </InputLabel>
          </Grid>
          <Grid
            container
            spacing={2}
            style={{
              padding: "12px",
              marginLeft: "-34px",
            }}
          >
            <FormControl component="fieldset">
              <FormLabel
                style={{
                  color: "#001837",
                  fontWeight: 500,
                  fontSize: "14px",
                }}
              >
                Select a Category <span style={{color:'red'}}>*</span>
                {categoryCheck == true && (
            <span
              style={{
                color: "red",
                fontSize: 11,
              }}
            >
              Please select a category
            </span>
          )}
              </FormLabel>
              
              <RadioGroup
                aria-label="category"
                name="category1"
                value={values}
                onChange={handleChange}
                style={{ paddingLeft: 10 }}
              >
                {category.map((cat: any, catIndex: any) => (
                  <FormControlLabel
                    value={cat.id}
                    control={
                      <Radio size="small" style={{ fontSize: "14px" }} />
                    }
                    label={
                      <Typography style={{ fontSize: "14px" }}>
                        {cat.serviceType}
                      </Typography>
                    }
                    key={catIndex}
                    disabled={
                      props.edit == true
                        ? true
                        : props.catArr && props.catArr.includes(cat.id)
                        ? true
                        : false
                    }
                  />
                ))}
              </RadioGroup>
            </FormControl>
          </Grid>
        </Paper>
      </Grid>
      <Grid item xs={6} style={{ padding: "0px" }}>
        <Paper style={{ height: "100%", paddingTop: 20, boxShadow: "none" }}>
          <MenuOpcode
            filteredOpcode={filteredOpcode}
            menuNmae={mname ? mname : ""}
            leftOpcode={leftOpcode}
            rightOpcode={rightOpcode}
            setRightOpcode={setRightOpcode}
            setLeftOpcode={setLeftOpcode}
            checkOpcode={checkOpcode}
            setCheckOpcode={setCheckOpcode}
          />
        </Paper>
      </Grid>
      <Grid item xs={12}>
        <Typography style={{ fontSize: 14, fontWeight: 500, color: "#001837" }}>
          <span>Select Intervals this Service Applies to</span>
          <span style={{ color: "red" }}>*</span>{" "}
          {intervalCheck == true && (
            <span
              style={{
                color: "red",
                fontSize: 11,
              }}
            >
              Please pick a time interval
            </span>
          )}
        </Typography>

        <Paper
          style={{
            height: "150px",
            padding: 20,
            overflowY: "auto",
          }}
        >
          <Grid container spacing={2}>
            {seriesArr.length > 0 &&
              seriesArr.map((column: any, colIndex: any) => (
                <Grid item xs={1} key={column.ratings || colIndex}>
                  <div>
                    {column.ratings !== null &&
                      column.ratings !== undefined && (
                        <FormControlLabel
                          style={{ height: 25, width: 50 }}
                          control={
                            <Checkbox
                              style={{ transform: "scale(.6)" }}
                              checked={assignedStatusValues.includes(
                                column.ratings
                              )}
                              onChange={() =>
                                handleCheckboxChange(colIndex, column)
                              }
                              disabled={
                                props.mServicetype !== column.service_type &&
                                column.assigned_status !== 0
                              }
                            />
                          }
                          label={
                            <Typography
                              style={{
                                fontSize: "12px",
                                backgroundColor:
                                  column.assigned_status == 0 ? "" : "#bad49f",
                              }}
                            >
                              {column.ratings.toLocaleString()}
                            </Typography>
                          }
                        />
                      )}
                  </div>
                </Grid>
              ))}
          </Grid>
        </Paper>
        <div style={{ float: "right" }}>
          <Button
            variant="contained"
            color="primary"
            fullWidth
            style={{
              marginRight: "5px",
              width: "100px",
              marginLeft: 1,
              marginTop: 20,
            }}
            onClick={CreateInterval}
            size="small"
          >
            Save
          </Button>
          <Button
            variant="contained"
            color="primary"
            fullWidth
            style={{
              marginRight: "5px",
              width: "100px",
              marginLeft: 1,
              marginTop: 20,
            }}
            onClick={CancelMenu}
            size="small"
          >
            Cancel
          </Button>
        </div>
      </Grid>
    </Grid>
  );
}
