import React, { useEffect, useState } from "react";
import { makeStyles, withStyles } from "@material-ui/styles";
import RevenueStatementGrid from "./RevenueStatementGrid";
import FormControlLabel from "@mui/material/FormControlLabel";
import Drawer from "@mui/material/Drawer";
import Chip from "@mui/material/Chip";
import {
  Grid,
  Paper,
  Tabs,
  Tab,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Tooltip,
} from "@mui/material";

var Dealer: string = process.env.REACT_APP_DEALER!;

const StyledTableRow = withStyles<any>((theme: any) => ({
  root: {
    "&:nth-of-type(odd)": {
      //color: '#ccc !important'
    },
  },
}))(TableRow);

const StyledTableCellSubHeader = withStyles<any>((theme: any) => ({
  head: {
    border: "1px solid #100101 !important",
    lineHeight: "1.0rem",
    backgroundColor: "rgb(209, 209, 209)",
    fontWeight: "bold",
    padding: 10,
  },
  body: {
    fontSize: 12,
    padding: 10,
    backgroundColor: "rgb(209, 209, 209)",
    fontWeight: "bold",
  },
}))(TableCell);

const StyledTableCellHeader = withStyles<any>((theme: any) => ({
  head: {
    color: "#fff !important",
    border: "1px solid #100101 !important",
    lineHeight: "1.0rem",
    backgroundColor:
      Dealer == "Armatus" ? "#003d6b !important" : "#c2185b !important",
    padding: 10,
    fontSize: 12,
  },
  body: {
    fontSize: 12,
    fontWeight: "bold",
    padding: 10,
    color: "#fff !important",
    border: "1px solid #100101 !important",
    lineHeight: "1.0rem",
    backgroundColor:
      Dealer == "Armatus" ? "#003d6b !important" : "#c2185b !important",
  },
}))(TableCell);

const useStyles = makeStyles((theme) => ({
  //   optBgSelected: {
  //     background: Dealer == 'Armatus' ? '#003d6b': '#c2185b',
  //     textAlign: 'center',
  //     border: Dealer == 'Armatus' ? '1px solid #003d6b':'1px solid #c2185b',
  //     borderRadius: '10px',
  //     margin: '2px'
  //   },

  optTagSelected: {
    textDecoration: "none",
    color: "#ffffff",
  },

  optBg: {
    margin: "2px",
    textAlign: "center",
    border: Dealer == "Armatus" ? "1px solid #003d6b" : "1px solid #c2185b",
    borderRadius: "10px",
  },

  optTag: {
    textDecoration: "none",
    color: "#000",
    "&:hover, &:focus": {
      background: Dealer == "Armatus" ? "#003d6b" : "#c2185b",
      border: Dealer == "Armatus" ? "1px solid #003d6b" : "1px solid #c2185b",
      borderRadius: "10px",
      cursor: "pointer",
      color: "#fff",
    },
  },

  optBgSelected: {
    "&:hover, &:focus": {
      background: "#ffffff",
      border: Dealer == "Armatus" ? "1px solid #003d6b" : "1px solid #c2185b",
      borderRadius: "10px",
      cursor: "pointer",
      color: "#fff",
    },

    backgroundColor: Dealer == "Armatus" ? "#003d6b" : "#c2185b",
    textAlign: "center",
    border: Dealer == "Armatus" ? "1px solid #003d6b" : "1px solid #c2185b",
    borderRadius: "10px",
    margin: "2px",
    color: "#fff !important",
  },

  //   optTagSelected: {
  //     color: '#646464',
  //     textDecoration: 'none',
  //     '&:hover, &:focus': {
  //       color: '#646464',
  //       textDecoration: 'none'
  //     }
  //   }
}));

const SummaryDataHeader: any = (props:any) => {
  const classes = useStyles();
  const [componentType, setComponentType] = useState<string>("all");
  const changeComponentType = (params: string) => {
    setComponentType(params);
    // props.changeRevenueComponentType(params);
  };

  return props.type == "all" ||
    props.type == "laborRevenueByComponent" ||
    props.type == "PartsRevenueByComponent" ? (
    <StyledTableRow className="headerTop">
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        RO Count
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Sold Hours
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Actual Hours
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Cost
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Sales
      </StyledTableCellHeader>
      {props.type == "all" ? (
        <StyledTableCellHeader
          className="title"
          style={{ minWidth: "50px" }}
        ></StyledTableCellHeader>
      ) : props.type == "laborRevenueByComponent" ? (
        <StyledTableCellSubHeader
          className="title"
          style={{ minWidth: "50px" }}
        >
          Customer Labor Revenue By Components
          <span>
            <a
              className="viewDetail"
              id="laborparts"
              style={{ cursor: "pointer" }}
            >
              <Tooltip title="Drill Down">
                <img
                  style={{
                    marginLeft: "5px",
                    width: "17px",
                    height: "17px",
                    float: "right",
                  }}
                  src="/images/Icons/external.png"
                />
              </Tooltip>
            </a>
          </span>
          <Grid direction="row" spacing={1} style={{ display: "inline-flex" }}>
            {/* <Chip
              label="All"
              className={
                classes.optBg +
                " " +
                classes.optTag +
                " " +
                (componentType == "all" ? classes.optBgSelected : "")
              }
              onClick={() => changeComponentType("all")}
              style={{ marginRight: 5 }}
              size="small"
            /> */}
            {/* <Chip
              label="With Parts"
              style={{ marginRight: 5 }}
              className={
                classes.optBg +
                " " +
                classes.optTag +
                " " +
                (componentType == "labor with parts"
                  ? classes.optBgSelected
                  : "")
              }
              onClick={() => changeComponentType("labor with parts")}
              size="small"
            /> */}
            {/* <Chip
              label="Without Parts"
              style={{ marginRight: 5 }}
              className={
                classes.optBg +
                " " +
                classes.optTag +
                " " +
                (componentType == "labor without parts"
                  ? classes.optBgSelected
                  : "")
              }
              size="small"
              onClick={() => changeComponentType("labor without parts")}
            /> */}
          </Grid>
        </StyledTableCellSubHeader>
      ) : (
        <StyledTableCellSubHeader
          className="title"
          style={{ minWidth: "50px" }}
        >
          Customer Parts Revenue By Components
          <span>
            <a
              className="viewDetail"
              id="laborparts"
              style={{ cursor: "pointer" }}
            >
              <Tooltip title="Drill Down">
                <img
                  style={{
                    marginLeft: "5px",
                    width: "17px",
                    height: "17px",
                    float: "right",
                  }}
                  src="/images/Icons/external.png"
                />
              </Tooltip>
            </a>
          </span>
        </StyledTableCellSubHeader>
      )}

      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Sales
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Cost
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Actual Hours
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Sold Hours
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        RO Count
      </StyledTableCellHeader>
    </StyledTableRow>
  ) : props.type == "laborRevenueByCategory" ? (
    <StyledTableRow>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        ELR Rev.
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        GP% Rev.
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Labor Rev.
      </StyledTableCellHeader>
      <StyledTableCellSubHeader className="title" style={{ minWidth: "50px" }}>
        {" "}
        Customer Labor Revenue By Category
        <span>
          <a
            className="viewDetail"
            id="laborparts"
            style={{ cursor: "pointer" }}
          >
            <Tooltip title="Drill Down">
              <img
                style={{
                  marginLeft: "5px",
                  width: "17px",
                  height: "17px",
                  float: "right",
                }}
                src="/images/Icons/external.png"
              />
            </Tooltip>
          </a>
        </span>
      </StyledTableCellSubHeader>

      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Labor Rev.
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        GP% Rev.
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        ELR Rev.
      </StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
    </StyledTableRow>
  ) : props.type == "partsRevenueByCategory" ? (
    <StyledTableRow>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Cost
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Sale
      </StyledTableCellHeader>
      <StyledTableCellSubHeader className="title" style={{ minWidth: "50px" }}>
        {" "}
        Customer Parts Revenue By Category
        <span>
          <a
            className="viewDetail"
            id="laborparts"
            style={{ cursor: "pointer" }}
          >
            <Tooltip title="Drill Down">
              <img
                style={{
                  marginLeft: "5px",
                  width: "17px",
                  height: "17px",
                  float: "right",
                }}
                src="/images/Icons/external.png"
              />
            </Tooltip>
          </a>
        </span>
      </StyledTableCellSubHeader>

      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Sale
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Cost
      </StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
    </StyledTableRow>
  ) : props.type == "jobLevelBrkDown" ? (
    <StyledTableRow>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        RO Count
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Parts
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Labor
      </StyledTableCellHeader>
      <StyledTableCellSubHeader className="title" style={{ minWidth: "50px" }}>
        {" "}
        Job Level Break Down
        <span>
          <a
            className="viewDetail"
            id="laborparts"
            style={{ cursor: "pointer" }}
          >
            <Tooltip title="Drill Down">
              <img
                style={{
                  marginLeft: "5px",
                  width: "17px",
                  height: "17px",
                  float: "right",
                }}
                src="/images/Icons/external.png"
              />
            </Tooltip>
          </a>
        </span>
      </StyledTableCellSubHeader>

      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Labor
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Parts
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        RO Count
      </StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
    </StyledTableRow>
  ) : props.type == "jobLevelBrkDownPerc" ? (
    <StyledTableRow>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Labor
      </StyledTableCellHeader>
      <StyledTableCellSubHeader className="title" style={{ minWidth: "50px" }}>
        {" "}
        Job Level Break Down
        <span>
          <a
            className="viewDetail"
            id="laborparts"
            style={{ cursor: "pointer" }}
          >
            <Tooltip title="Drill Down">
              <img
                style={{
                  marginLeft: "5px",
                  width: "17px",
                  height: "17px",
                  float: "right",
                }}
                src="/images/Icons/external.png"
              />
            </Tooltip>
          </a>
        </span>
      </StyledTableCellSubHeader>

      <StyledTableCellHeader className="title" style={{ minWidth: "50px" }}>
        Labor
      </StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: "50px" }}
      ></StyledTableCellHeader>
    </StyledTableRow>
  ) : (
    ""
  );
};

export default SummaryDataHeader;
