import React, { useEffect, useState } from "react";
import RevenueStatementGrid from "./RevenueStatementGrid";
import { loadMonthYears } from "../../service/dataFetchQueries";
const RevenueStatement: React.FC<any> = (props) => {
  const [queryMonth, setQueryMonth] = useState<string>("");
  const [optQueryMonth, setOptQueryMonth] = useState<string>("");
  const [endQueryMonth, setEndQueryMonth] = useState<string>("");
  let params = "";

  useEffect(() => {
    getMonthYears();
  }, []);

  const getMonthYears = async () => {
    try {
      const res = await loadMonthYears();
      if (res) {
        const monthYear = res;
        setQueryMonth(monthYear[0].monthYear);
        setOptQueryMonth(monthYear[1].monthYear);
        setEndQueryMonth(monthYear[monthYear.length - 1].monthYear);
        localStorage.setItem("queryMonth", monthYear[1].monthYear);
      }
    } catch (error) {
      console.error("Error fetching menu model data:", error);
      throw error;
    }
  };
  if (queryMonth !== "" && optQueryMonth !== "" && endQueryMonth !== "") {
    return (
      <>
        <RevenueStatementGrid
          queryMonth={queryMonth}
          optQueryMonthYear={optQueryMonth}
          endMonth={endQueryMonth}
          params={params.trim() === "" ? null : params}
        />
      </>
    );
  } else {
    return <>Error</>;
  }
};

export default RevenueStatement;
