import React, { useEffect, useState } from "react";
import { makeStyles, withStyles } from "@material-ui/styles";
import RevenueStatementGrid from "./RevenueStatementGrid";
import { TableBody, Table, TableHead } from "@mui/material";
import TableCell from "@mui/material/TableCell";
import TableRow from "@mui/material/TableRow";
import SummaryDataGrid from "./SummaryDataGrid";
import SummaryDataHeader from "./SummaryDataHeader";
import { formatCellValue, formatCellValueCount } from "../../utils/Utils";
import {
  getRevenueSummaryWarrantyVolumesLabor,
  getRevenueSummaryWarrantyVolumesParts,
} from "../../service/dataFetchQueries";

const StyledTableRow = withStyles((theme) => ({
  root: {
    "&:nth-of-type(odd)": {
      //color: '#ccc !important'
    },
    // padding: 10
  },
}))(TableRow);

const StyledTableCellHeader = withStyles((theme) => ({
  head: {
    border: "1px solid #100101 !important",
    lineHeight: "1.0rem",
    fontSize: 10,
    maxWidth: 4,
    paddingLeft: "1px",
  },
  body: {
    fontSize: 10,
    border: "1px solid #100101 !important",
    lineHeight: "1.0rem",
    maxWidth: 4,
    paddingLeft: "7px",
  },
}))(TableCell);

const StyledTableCellSubHeader = withStyles((theme) => ({
  head: {
    border: "1px solid #100101 !important",
    lineHeight: "1.0rem",

    fontWeight: "bold",
    padding: 10,
    maxWidth: 4,
  },
  body: {
    fontSize: 10,
    padding: 10,

    border: "1px solid #100101 !important",
    fontWeight: "bold",
    maxWidth: 4,
  },
}))(TableCell);

const StyledTableCellHeaderMain = withStyles((theme) => ({
  head: {
    border: "1px solid #100101 !important",
    lineHeight: "1.0rem",
    fontSize: 12,
    maxWidth: 5,
    backgroundColor: "#ddeaf4",
  },
  body: {
    fontSize: 12,
    border: "1px solid #100101 !important",
    lineHeight: "1.0rem",
    maxWidth: 5,
  },
}))(TableCell);

const StyledTableCell = withStyles((theme) => ({
  head: {
    color: "#100101 !important",
    border: "1px solid #ccc !important",
    lineHeight: "0.2rem",
    padding: 10,
  },
  body: {
    fontSize: 12,
    padding: 10,
    border: "1px solid #ccc !important",
  },
}))(TableCell);

const WarrantyVolumesGrid: React.FC<any> = (props) => {
  const [summaryData, setSummaryData] = useState<any[]>([]);
  const [summaryDataParts, setSummaryDataParts] = useState<any[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      let result: any[];

      if (props.type === "labor") {
        result = await getRevenueSummaryWarrantyVolumesLabor();
      } else {
        result = await getRevenueSummaryWarrantyVolumesParts();
      }

      if (result) {
        const sorted = result.sort((a, b) => {
          if (a.heading < b.heading) return -1;
          if (a.heading > b.heading) return 1;
          return 0;
        });

        setSummaryData(sorted);
      }
    };

    fetchData();
  }, [props.type]);

  return (
    <Table style={{ marginBottom: 10, marginTop: 10 }} aria-label="purchases">
      <TableHead>
        <StyledTableRow className="headerTop">
          <StyledTableCellHeaderMain className="title" colSpan={1}>
            RO Count
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title">
            Sold Hours
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title">
            Actual Hours
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title" colSpan={1}>
            Cost
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title">
            Sales
          </StyledTableCellHeaderMain>
          {props.type == "labor" ? (
            <StyledTableCellHeaderMain className="title" colSpan={1}>
              Rate
            </StyledTableCellHeaderMain>
          ) : (
            <StyledTableCellHeaderMain className="title" colSpan={1}>
              Markup
            </StyledTableCellHeaderMain>
          )}
          <StyledTableCellSubHeader
            className="title"
            colSpan={4}
            style={{ width: "20%", backgroundColor: "#ddeaf4" }}
          >
            {props.type == "labor" ? "Labor" : "Parts"}
          </StyledTableCellSubHeader>
          {props.type == "labor" ? (
            <StyledTableCellHeaderMain className="title" colSpan={1}>
              Rate
            </StyledTableCellHeaderMain>
          ) : (
            <StyledTableCellHeaderMain className="title" colSpan={1}>
              Markup
            </StyledTableCellHeaderMain>
          )}
          <StyledTableCellHeaderMain className="title">
            Sales
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title" colSpan={1}>
            Cost
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title">
            Actual Hours
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title">
            Sold Hours
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title">
            RO Count
          </StyledTableCellHeaderMain>
        </StyledTableRow>
      </TableHead>
      <TableBody>
        {summaryData.length > 0 &&
          summaryData.map((summary, index) => (
            <StyledTableRow className="headerTop">
              <StyledTableCellHeader className="title" colSpan={1}>
                {formatCellValueCount(summary.selJobcount)}
              </StyledTableCellHeader>
              <StyledTableCellHeader className="title">
                {formatCellValueCount(summary.selHours)}
              </StyledTableCellHeader>
              <StyledTableCellHeader className="title">
                {formatCellValueCount(summary.actualHours)}
              </StyledTableCellHeader>
              <StyledTableCellHeader className="title" colSpan={1}>
                {formatCellValue(summary.selCost)}
              </StyledTableCellHeader>
              <StyledTableCellHeader className="title">
                {formatCellValue(summary.selSale)}
              </StyledTableCellHeader>
              {props.type == "labor" ? (
                <StyledTableCellHeader className="title">
                  {formatCellValue(summary.elr)}
                </StyledTableCellHeader>
              ) : (
                <StyledTableCellHeader className="title" colSpan={1}>
                  {formatCellValue(summary.markup)}
                </StyledTableCellHeader>
              )}

              <StyledTableCellSubHeader
                className="title"
                colSpan={4}
                style={{ width: "20%", backgroundColor: "#d1d1d1" }}
              >
                {summary.heading}
              </StyledTableCellSubHeader>
              {props.type == "labor" ? (
                <StyledTableCellHeader className="title">
                  {formatCellValue(summary.optElr)}
                </StyledTableCellHeader>
              ) : (
                <StyledTableCellHeader className="title" colSpan={1}>
                  {formatCellValue(summary.optMarkup)}
                </StyledTableCellHeader>
              )}
              <StyledTableCellHeader className="title" colSpan={1}>
                {formatCellValue(summary.selCost)}
              </StyledTableCellHeader>
              <StyledTableCellHeader className="title" colSpan={1}>
                {formatCellValue(summary.optSelCost)}
              </StyledTableCellHeader>

              <StyledTableCellHeader className="title">
                {formatCellValueCount(summary.optActualHours)}
              </StyledTableCellHeader>
              <StyledTableCellHeader className="title">
                {formatCellValueCount(summary.optSelHours)}
              </StyledTableCellHeader>

              <StyledTableCellHeader className="title">
                {formatCellValueCount(summary.optSelJobCount)}
              </StyledTableCellHeader>
            </StyledTableRow>
          ))}
      </TableBody>
    </Table>
  );
};

export default WarrantyVolumesGrid;
