import React, { useEffect, useState } from "react";
import { makeStyles, withStyles } from "@material-ui/styles";
import RevenueStatementGrid from "./RevenueStatementGrid";
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Toolbar,
  Button,
  TableBody,
} from "@mui/material"
import FormControlLabel from "@mui/material/FormControlLabel";
import Table from "@mui/material/Table";
import Drawer from "@mui/material/Drawer";
import TableCell from "@mui/material/TableCell";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Tooltip from "@mui/material/Tooltip";
import IconButton from "@mui/material/IconButton";
import Collapse from "@mui/material/Collapse";
import KeyboardArrowDownIcon from "@material-ui/icons/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@material-ui/icons/KeyboardArrowUp";
import {
  formatCellValue,
  formatCellValueCount,
  formatCellValuePerc,
} from "../../utils/Utils";
import WarrantyVolumesGrid from "./WarrantyVolumesGrid";


const StyledTableRow = withStyles((theme) => ({
  root: {
    "&:nth-of-type(odd)": {
      //color: '#ccc !important'
    },
  },
}))(TableRow);

const StyledTableCellSubHeader = withStyles((theme) => ({
  head: {
    border: "1px solid #100101 !important",
    lineHeight: "1.0rem",
    backgroundColor: "rgb(209, 209, 209)",
    fontWeight: "bold",
    padding: 10,
  },
  body: {
    fontSize: 12,
    padding: 10,
    backgroundColor: "rgb(209, 209, 209)",
    //fontWeight: 'bold'
  },
}))(TableCell);

const StyledTableCellSubHeaderRevenue = withStyles((theme) => ({
  head: {
    border: "1px solid #100101 !important",
    lineHeight: "1.0rem",
    backgroundColor: "rgb(209, 209, 209)",
    fontWeight: "bold",
    padding: 10,
  },
  body: {
    fontSize: 12,
    padding: 10,
    backgroundColor: "rgb(209, 209, 209)",
    fontWeight: "bold",
  },
}))(TableCell);

const StyledTableCell = withStyles((theme) => ({
  head: {
    color: "#100101 !important",
    border: "1px solid #ccc !important",
    lineHeight: "0.2rem",
    padding: 10,
  },
  body: {
    fontSize: 12,
    textAlign: "right",
    padding: 10,
    border: "1px solid #ccc !important",
  },
}))(TableCell);

const SummaryDataGrid: React.FC<any> = (props) => {
  const { summaryData } = props;
  const [isExpanded, setIsExpanded] = useState(false);
  const [open, setOpen] = React.useState(false);
  const changeRevenueComponentType = (params: any) => {
    setIsExpanded(true);
  };
  const formatToUpperCase = (str: string) =>
    str.replace(/^(.)|\s+(.)/g, (c) => c.toUpperCase());

  const formatHeading = (heading: any) => {
    if (heading == "extended warranty") {
      return "+++Extended";
    } else if (heading.startsWith("-")) {
      // return '- ' + heading.charAt(1).toUpperCase() + heading.slice(2);
      return "- " + formatToUpperCase(heading.slice("1"));
    } else if (heading == "Sale No Cost") {
      return "Sale, No Cost";
    } else if (heading == "Cost No Sale") {
      return "Cost, No Sale";
    } else if (heading == "Sale Equals Cost Core") {
      return "Sale = Cost; Core Related";
    } else if (heading == "Sale Equals Cost Not Core") {
      return "Sale = Cost; Not Core Related";
    } else if (heading == "sale costGT50 markup GT3") {
      return "Sale & Cost; Mark-Up > 3.00 & Cost > $50";
    } else if (heading == "sale cost markup LT1.03") {
      return "Sale & Cost; Mark-Up < 1.03";
    } else if (heading == "sale cost markup GT1.03") {
      return "<b>Charts: Sale & Cost; Mark-Up >= 1.03</b>";
    } else if (heading == "extended warranty - labor") {
      return " - labor";
    } else if (heading == "extended warranty - parts") {
      return " - parts";
    } else if (heading == "revenue with cost and hours") {
      // return '<b>(A)</b> Revenue With Cost And Hours';
      return "Revenue With Cost And Hours";
    } else if (heading == "revenue with cost but no hours") {
      // return '<b>(B)</b> Revenue With Cost But No Hours';
      return "Revenue With Cost But No Hours";
    } else if (heading == "revenue no cost but hours") {
      // return '<b>(C)</b> Revenue No Cost But Hours';
      return "Revenue No Cost But Hours";
    } else if (heading == "revenue no cost no hours") {
      // return '<b>(D)</b> Revenue No Cost No Hours';
      return "Revenue No Cost No Hours";
    } else if (heading == "revenue - no filters - labor and parts") {
      return "<b>Revenue - No Filters (L & P)</b>";
    } else {
      //return heading.charAt(0).toUpperCase() + heading.slice(1);
      return formatToUpperCase(heading);
    }
  };

  return props.type == "all" ? (
    <>
      {summaryData.map((data: any) => (
        <>
          <StyledTableRow>
            <StyledTableCell id="currTotalJobCount">
              {formatCellValueCount(data.selJobcount)}
            </StyledTableCell>
            <StyledTableCell id="currTotalHours">
              {formatCellValueCount(data.selHours)}
            </StyledTableCell>
            <StyledTableCell id="currActualHours">
              {formatCellValueCount(data.actualHours)}
            </StyledTableCell>
            <StyledTableCell id="currTotalCost">
              {formatCellValue(data.selCost)}
            </StyledTableCell>
            <StyledTableCell id="currTotalSales">
              {formatCellValue(data.selSale)}
            </StyledTableCell>
            <StyledTableCellSubHeader id="currTotalSales">
              {/* <label> {ReactHtmlParser(formatHeading(data.heading))} </label> */}
              <label> {formatHeading(data.heading)} </label>
              {props.isChartSubTotal ? (
                <div></div>
              ) : (
                props.department == "service" &&
                data.heading.toLowerCase() == "warranty" && (
                  <Button
                    style={{ float: "right", fontSize: 10 }}
                    onClick={() => setOpen(!open)}
                  >
                    Rates & Markups
                    <IconButton
                      aria-label="expand row"
                      size="small"
                      onClick={() => setOpen(!open)}
                    >
                      {open ? (
                        <KeyboardArrowUpIcon />
                      ) : (
                        <KeyboardArrowDownIcon />
                      )}
                    </IconButton>
                  </Button>
                )
              )}
            </StyledTableCellSubHeader>

            <StyledTableCell id="optTotalSales">
              {formatCellValue(data.optSelSale)}
            </StyledTableCell>
            <StyledTableCell id="optTotalCost">
              {formatCellValue(data.optSelCost)}
            </StyledTableCell>
            <StyledTableCell id="optActualHours">
              {formatCellValueCount(data.optActualHours)}
            </StyledTableCell>
            <StyledTableCell id="optTotalHours">
              {formatCellValueCount(data.optSelHours)}
            </StyledTableCell>
            <StyledTableCell id="optTotalJobCount">
              {formatCellValueCount(data.optSelJobCount)}
            </StyledTableCell>
          </StyledTableRow>
          {props.department == "service" &&
            data.heading.toLowerCase() == "warranty" && (
              <TableRow>
                <TableCell
                  style={{ paddingBottom: 0, paddingTop: 0, padding: 0 }}
                  colSpan={12}
                >
                  <Collapse in={open} timeout="auto" unmountOnExit>
                    <Box sx={{ margin: 1 }}>
                      <WarrantyVolumesGrid
                        type="labor"
                        currMonth={props.currMonth}
                        optMonth={props.optMonth}
                      />
                      <WarrantyVolumesGrid
                        type="parts"
                        currMonth={props.currMonth}
                        optMonth={props.optMonth}
                      />
                    </Box>
                  </Collapse>
                </TableCell>
              </TableRow>
            )}
        </>
      ))}
    </>
  ) : props.type == "laborRevenueByCategory" ? (
    summaryData.map((data: any) => (
      <StyledTableRow>
        <StyledTableCell id="currTotalJobCount"></StyledTableCell>
        <StyledTableCell id="currTotalHours"></StyledTableCell>
        <StyledTableCell id="currActualHours">
          {formatCellValueCount(data.elrRev)}
        </StyledTableCell>
        <StyledTableCell id="currTotalCost">
          {formatCellValuePerc("-" + props.type, data.gpPercLbrRev)}
        </StyledTableCell>
        <StyledTableCell id="currTotalSales">
          {formatCellValue(data.lbrRevenue)}
        </StyledTableCell>
        <StyledTableCellSubHeader id="currTotalSales">
          {formatHeading(data.heading)}
        </StyledTableCellSubHeader>
        <StyledTableCell id="optTotalSales">
          {formatCellValue(data.optLbrRevenue)}
        </StyledTableCell>
        <StyledTableCell id="optTotalCost">
          {formatCellValuePerc("-" + props.type, data.gpPercLbrRev)}
        </StyledTableCell>
        <StyledTableCell id="optActualHours">
          {formatCellValueCount(data.optElrRev)}
        </StyledTableCell>
        <StyledTableCell id="optTotalHours"></StyledTableCell>
        <StyledTableCell id="optTotalJobCount"></StyledTableCell>
      </StyledTableRow>
    ))
  ) : props.type == "partsRevenueByCategory" ? (
    summaryData.map((data: any) => (
      <StyledTableRow>
        <StyledTableCell id="currTotalJobCount"></StyledTableCell>
        <StyledTableCell id="currTotalHours"></StyledTableCell>
        <StyledTableCell id="currActualHours"></StyledTableCell>
        <StyledTableCell id="currTotalCost">
          {formatCellValue(data.selCost)}
        </StyledTableCell>
        <StyledTableCell id="currTotalSales">
          {formatCellValue(data.selSale)}
        </StyledTableCell>
        <StyledTableCellSubHeader id="currTotalSales">
          {formatHeading(data.heading)}
        </StyledTableCellSubHeader>
        <StyledTableCell id="optTotalSales">
          {formatCellValue(data.optSelSale)}
        </StyledTableCell>
        <StyledTableCell id="optTotalCost">
          {formatCellValue(data.optSelCost)}
        </StyledTableCell>
        <StyledTableCell id="optActualHours"></StyledTableCell>
        <StyledTableCell id="optTotalHours"></StyledTableCell>
        <StyledTableCell id="optTotalJobCount"></StyledTableCell>
      </StyledTableRow>
    ))
  ) : props.type == "jobLevelBrkDownPerc" ? (
    summaryData.map((data: any) => (
      <StyledTableRow>
        <StyledTableCell id="currTotalJobCount"></StyledTableCell>
        <StyledTableCell id="currTotalHours"></StyledTableCell>
        <StyledTableCell id="currActualHours"></StyledTableCell>
        <StyledTableCell id="currTotalCost"></StyledTableCell>
        <StyledTableCell id="currTotalSales">
          {formatCellValuePerc(data.heading, data.labor)}
        </StyledTableCell>
        <StyledTableCellSubHeader id="currTotalSales">
          {formatHeading(data.heading)}
        </StyledTableCellSubHeader>
        <StyledTableCell id="optTotalSales">
          {formatCellValuePerc(data.heading, data.optLabor)}
        </StyledTableCell>
        <StyledTableCell id="optTotalCost"></StyledTableCell>
        <StyledTableCell id="optActualHours"></StyledTableCell>
        <StyledTableCell id="optTotalHours"></StyledTableCell>
        <StyledTableCell id="optTotalJobCount"></StyledTableCell>
      </StyledTableRow>
    ))
  ) : (
    props.type == "jobLevelBrkDown" &&
    summaryData.map((data: any) => (
      <StyledTableRow>
        <StyledTableCell id="currTotalJobCount"></StyledTableCell>
        <StyledTableCell id="currTotalHours"></StyledTableCell>
        <StyledTableCell id="currActualHours">{data.rocount}</StyledTableCell>
        <StyledTableCell id="currTotalCost">
          {formatCellValue(data.parts)}
        </StyledTableCell>
        <StyledTableCell id="currTotalSales">
          {formatCellValue(data.labor)}
        </StyledTableCell>
        <StyledTableCellSubHeader id="currTotalSales">
          {formatHeading(data.heading)}
        </StyledTableCellSubHeader>
        <StyledTableCell id="optTotalSales">
          {formatCellValue(data.optLabor)}
        </StyledTableCell>
        <StyledTableCell id="optTotalCost">
          {formatCellValue(data.optParts)}
        </StyledTableCell>
        <StyledTableCell id="optActualHours">{data.optRocount}</StyledTableCell>
        <StyledTableCell id="optTotalHours"></StyledTableCell>
        <StyledTableCell id="optTotalJobCount" />
      </StyledTableRow>
    ))
  );
};

export default SummaryDataGrid;
