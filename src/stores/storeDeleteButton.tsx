import React, { useState } from "react";
import { Confirm, useRecordContext, useTranslate } from "react-admin";
import { useNavigate } from "react-router-dom";
import { DeleteSingleStore } from "../service/mutations";
import { PageRoutes } from "../utils/pageRoutes";
import { Box, Button, Typography } from "@mui/material";
import dayjs from "dayjs";
import type { DatePickerProps } from "antd";
import { DatePicker, Space } from "antd";

const DeleteStore = (props?: any) => {
  const { page, setIsStoreDeleted } = props;
  const record = useRecordContext();
  const navigate = useNavigate();
  const tenantTableId = localStorage.getItem("tenant-id");
  const translate = useTranslate();
  const [open, setOpen] = useState(false);
  const handleClick = () => setOpen(true);
  const [selectedDate, setSelectedDate] = useState<any>("");

  const handleConfirm = async () => {
    DeleteSingleStore(record.storeId, record.tenantId, selectedDate
      ? dayjs(selectedDate).format("MM/DD/YYYY")
      : null).then(
      (res) => {
        if (res === "Success") {
          page === "tenantPage"
            ? setIsStoreDeleted(true)
            : navigate(PageRoutes.tenantShowPageRoute(tenantTableId));
          setOpen(false);
        }
      }
    );
  };

  const onDateChange: DatePickerProps["onChange"] = (date, dateString) => {
    setSelectedDate(date);
  };
  return (
    <div style={{ marginRight: "16px" }}>
      <Button
        variant="contained"
        color={"error"}
        onClick={handleClick}
        size="small"
      >
        Delete
      </Button>
      <Confirm
        isOpen={open}
        title={`Remove ${record.storeName}`}
        // content={translate("DIALOG_BOX.DELETE_STORE")}
        content={
          <Box>
            <Typography variant="body1">
              {translate("DIALOG_BOX.DELETE_STORE")}
            </Typography>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                mt: 2,
                width: "200px",
              }}
            >
              <Typography
                sx={{
                  color: "#1976D2",
                  fontSize: "12px",
                }}
              >
                Deleted Date
              </Typography>
              <DatePicker
                popupStyle={{ zIndex: 9999 }}
                onChange={onDateChange}
                format={"MM/DD/YYYY"}
                value={selectedDate}
              />
            </Box>
          </Box>
        }
        onConfirm={handleConfirm}
        onClose={() => setOpen(false)}
      />
    </div>
  );
};

export default DeleteStore;
