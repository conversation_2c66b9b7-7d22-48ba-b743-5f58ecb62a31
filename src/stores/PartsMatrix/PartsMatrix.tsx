import {
  Box,
  Button,
  Grid,
  Paper,
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
  CircularProgress,
  IconButton,
  Tooltip,
} from "@mui/material";
import * as React from "react";
import { AgGridReact } from "ag-grid-react";
import { useRecordContext, useTranslate } from "react-admin";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import EditIcon from "@mui/icons-material/Edit";
import "react-datepicker/dist/react-datepicker.css";
import "react-datepicker/dist/react-datepicker-cssmodules.css";
import { useEffect, useRef } from "react";
import NewGridOrMatrix from "../../components/NewGridOrMatrix";
import moment from "moment";
import SnackBarMessage from "../../components/SnackBarMessage";
import Loading from "../../components/Loading";
import CustomLinearProgress from "../../components/CustomLinearProgress";
import dayjs from "dayjs";
import DeletePartsButton from "./DeletePartsButton";
import GridDefinitions from "./GridDefinitions";
import usePartsMatrix from "../../CustomHooks/usePartsMatrix";
import NewMatrixForm from "./NewMatrixForm";
import { Constants } from "../../utils/constants";
import MatrixNameModal from "./MatrixNameModal";
const PartsMatrix = (props: any) => {
  const record: any = useRecordContext();
  const gridRef: any = useRef();
  const { getStoreQuery } = props;
  const {
    setMatrixValidationMessage,
    matrixValidationMessage,
    saveClickedFlag,
    setInitialRowData,
    rowData,
    onBtStartEditing,
    initialRowData,
    setEnableSave,
    enableSave,
    setListLoading,
    fetchPartsMatrixRowData,
    setOpenSnackbar,
    openSnackbar,
    statusMessage,
    isSuccessful,
    rowDataList,
    handleSubmit,
    submitLoading,
    addMatrix,
    handleRadioChange,
    handleSubmitPartsMatrix,
    setAddMatrix,
    addEmptyRowForCreate,
    handleRemoveLastRowForCreate,
    matrixSource,
    matrixType,
    sourceLoading,
    installDateValue,
    setInstallDateValue,
    updateDate,
    setUpdateDate,
    listLoading,
    expandedAccordionIndex,
    setIsAccordionExpanded,
    setRowData,
    setExpandedAccordionIndex,
    setEdit,
    setSaveClickedFlag,
    fetchPartsMatrixRowDetails,
    setSelectedSources,
    setInstallDate,
    findMatrixOrder,
    handleEditButton,
    createOrUpdatePartsMatrix,
    cancel,
    edit,
    addEmptyRowForEdit,
    handleRemoveLastRowForEdit,
    setOpenMatrixNameModal,
    openMatrixNameModal,
    matrixNameChanged,
    setMatrixNameChanged,
    getMatrixTypesList,
    setStatusMessage,
    statusMessageType, setStatusMessageType
  } = usePartsMatrix(record, gridRef, getStoreQuery);
  const translate = useTranslate();
  const { columnDefs, defaultColDef, formatCellValueDescription } =
    GridDefinitions(saveClickedFlag);
  const onGridReady = (params: any) => {
    gridRef.current = params && params;
    const newEmptyRow = {
      id: 0,
      priceStartRange: "0.01",
      priceEndRange: "",
      addPercentage: "",
    };
    setInitialRowData([newEmptyRow]);
    !rowData &&
      setTimeout(() => {
        onBtStartEditing();
      }, 100);
  };

  const getGridWrapper = (action?: string, RowData?: any) => {
    return (
      <>
        <div
          className={Constants.ag_grid_theme}
          style={{ height: "30vh", width: "85%" }}
        >
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            onGridReady={(params) => onGridReady(params)}
            rowData={action === "add" ? initialRowData : rowData}
            defaultColDef={defaultColDef}
            // rowHeight={45}
            rowSelection="single"
            animateRows={true}
            ref={gridRef}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            // onRowEditingStopped={onRowEditingStopped}
            stopEditingWhenCellsLoseFocus={true}
            onRowEditingStarted={() => setEnableSave(true)}
            suppressClickEdit={enableSave ? false : true}
          />
        </div>
      </>
    );
  };
  useEffect(() => {
    setListLoading(true);
    fetchPartsMatrixRowData();
  }, []);

  return (
    <Box sx={{ margin: "8px" }}>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={translate(statusMessage, {
          entityName: "Matrix",
        })}
        type={statusMessageType}
      />
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          padding: "0 18px",
        }}
      >
        <Button
          size="small"
          variant="outlined"
          color="primary"
          sx={{
            textTransform: "none",
          }}
          onClick={() => {
            setOpenMatrixNameModal(true);
          }}
        >
          All Source Matrix Names
        </Button>
        <MatrixNameModal
          setOpenMatrixNameModal={setOpenMatrixNameModal}
          openMatrixNameModal={openMatrixNameModal}
          matrixNameChanged={matrixNameChanged}
          setMatrixNameChanged={setMatrixNameChanged}
          matrixType={matrixType}
          record={record}
          getMatrixTypesList={getMatrixTypesList}
          setStatusMessage={setStatusMessage}
          setOpenSnackbar={setOpenSnackbar}
          setStatusMessageType={setStatusMessageType}
          fetchPartsMatrixRowData={fetchPartsMatrixRowData}
        />
        {!record.isPartsMatrixSubmitted && (
          <Button
            disabled={rowDataList.length === 0}
            size="small"
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            sx={{ ml: 1 }}
          >
            {submitLoading ? <CircularProgress size={20} /> : "Submit"}
          </Button>
        )}
      </Box>
      <NewGridOrMatrix
        addGrid={addMatrix}
        handleRadioChange={handleRadioChange}
        component="partsMatrix"
        getGridWrapper={getGridWrapper}
        handleSubmitPartsMatrix={handleSubmitPartsMatrix}
        setAddGrid={setAddMatrix}
        addEmptyRow={addEmptyRowForCreate}
        handleRemoveLastRow={handleRemoveLastRowForCreate}
        initialRowData={initialRowData}
        matrixSourceList={matrixSource}
        matrixTypeList={matrixType}
        incompleteError={matrixValidationMessage}
        fetchPartsMatrixRowData={fetchPartsMatrixRowData}
        listLoading={sourceLoading}
        installDateValue={installDateValue}
        setInstallDateValue={setInstallDateValue}
        updateDate={updateDate}
        setUpdateDate={setUpdateDate}
        setStatusMessage={setStatusMessage}
        setStatusMessageType={setStatusMessageType}
        setOpenSnackbar={setOpenSnackbar}
      />
      {listLoading ? (
        <Loading size="small" />
      ) : rowDataList.length === 0 ? (
        <Box
          sx={{ display: "flex", margin: "30px 0px", justifyContent: "center" }}
        >
          <Typography
            sx={{
              fontSize: 16,
              color: "#003d6b",
            }}
          >
            No data to show
          </Typography>
        </Box>
      ) : (
        <Paper sx={{ margin: "16px" }}>
          {rowDataList &&
            rowDataList.map((RowData: any, index: any) => (
              <Accordion
                style={{ marginBottom: 16 }}
                key={index}
                expanded={index === expandedAccordionIndex}
                onChange={(event: React.SyntheticEvent, expanded: boolean) => {
                  setUpdateDate(false);
                  setInstallDateValue("");
                  setIsAccordionExpanded(expanded);
                  setAddMatrix("");
                  setRowData(null);
                  setExpandedAccordionIndex(expanded ? index : null);
                  setEnableSave(false);
                  setEdit(false);
                  setMatrixValidationMessage("");
                  if (expanded) {
                    setInstallDateValue(dayjs(RowData.storeInstallDate));
                    setSaveClickedFlag(false);
                    fetchPartsMatrixRowDetails(RowData);
                    fetchPartsMatrixRowData("prtsource_list");
                    setSelectedSources(RowData.prtsource);
                    setInstallDate(new Date(RowData.storeInstallDate));
                  } else {
                    setExpandedAccordionIndex(null);
                    setInstallDateValue("");
                  }
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{ display: "flex", flexDirection: "row" }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "row",
                      width: "100%",
                      justifyContent: "space-between",
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        width: "100%",
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "row",
                          width: "100%",
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: "column",
                            flex: 1,
                          }}
                        >
                          {/* Matrix type box */}
                          <Box sx={{ display: "flex", flexDirection: "row" }}>
                            <Typography
                              sx={{
                                width: "6rem",
                                color: "#003d6b",
                                fontWeight: "bold",
                                fontSize: 14,
                              }}
                            >
                              Matrix Type
                            </Typography>
                            <Tooltip
                              title={
                                RowData.partsFor === "opcode"
                                  ? "Opcode"
                                  : "Source Matrix"
                              }
                            >
                              <Typography
                                sx={{
                                  fontSize: 14,
                                  color: "#003d6b",
                                  fontWeight: "bold",
                                }}
                              >
                                :{" "}
                                {RowData.partsFor === "opcode"
                                  ? "Opcode"
                                  : "Source Matrix"}
                              </Typography>
                            </Tooltip>
                          </Box>
                          {/* Matrix name box */}
                          <Box sx={{ display: "flex", flexDirection: "row" }}>
                            <Typography
                              sx={{
                                width: "6rem",
                                color: "#003d6b",
                                fontWeight: "bold",
                                fontSize: 14,
                              }}
                            >
                              Matrix Name
                            </Typography>
                            <Tooltip title={RowData.matrixType}>
                              <Typography
                                sx={{
                                  fontSize: 14,
                                  color: "#003d6b",
                                  fontWeight: "bold",
                                }}
                              >
                                :{" "}
                                {formatCellValueDescription(RowData.matrixType)}
                              </Typography>
                            </Tooltip>
                          </Box>
                        </Box>
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: "column",
                            flex: 1,
                          }}
                        >
                          {/* Installed Dates box */}
                          <Box sx={{ display: "flex", flexDirection: "row" }}>
                            {" "}
                            <Typography
                              sx={{
                                width: "13rem",
                                fontSize: 14,
                                color: "#003d6b",
                                fontWeight: "bold",
                              }}
                            >
                              Store Install Date
                            </Typography>
                            <Typography
                              sx={{
                                fontSize: 14,
                                color: "#003d6b",
                                fontWeight: "bold",
                              }}
                            >
                              {": "}
                              {moment(RowData.storeInstallDate).format(
                                "MM/DD/YYYY"
                              )}
                            </Typography>
                          </Box>
                          {/* Calculated Dates box */}
                          <Box sx={{ display: "flex", flexDirection: "row" }}>
                            <Typography
                              sx={{
                                width: "13rem",
                                color: "#003d6b",
                                fontWeight: "bold",
                                fontSize: 14,
                              }}
                            >
                              FOPC Calculated Date From
                            </Typography>
                            <Typography
                              sx={{
                                fontSize: 14,
                                color: "#003d6b",
                                fontWeight: "bold",
                              }}
                            >
                              :{" "}
                              {moment(RowData.createdDate).format("MM/DD/YYYY")}{" "}
                            </Typography>
                          </Box>
                        </Box>
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: "column",
                            flex: 1,
                          }}
                        >
                          <Box sx={{ display: "flex", flexDirection: "row" }}>
                            {" "}
                            <Typography
                              sx={{
                                width: "6rem",
                                fontSize: 14,
                                color: "#003d6b",
                                fontWeight: "bold",
                              }}
                            >
                              Matrix Period
                            </Typography>
                            <Typography
                              sx={{
                                fontSize: 14,
                                color: "#003d6b",
                                fontWeight: "bold",
                              }}
                            >
                              : {findMatrixOrder(RowData?.matrixOrder)}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                      {record.dms !== "Reynolds" &&  record.dms !== "Tekion" && (
                        <Box sx={{ display: "flex", flexDirection: "row" }}>
                          <Typography
                            sx={{
                              width: "6rem",
                              color: "#003d6b",
                              fontWeight: "bold",
                              fontSize: 14,
                            }}
                          >
                            Source(s)
                          </Typography>
                          <Tooltip
                            title={
                              RowData.prtsource.join(", ")
                                ? RowData.prtsource.join(", ")
                                : "NA"
                            }
                          >
                            <Typography
                              sx={{
                                fontSize: 14,
                                color: "#003d6b",
                                fontWeight: "bold",
                              }}
                            >
                              :{" "}
                              {RowData.prtsource
                                ? RowData.prtsource.join(", ")
                                : "NA"}
                            </Typography>
                          </Tooltip>
                        </Box>
                      )}
                    </Box>
                    <Box sx={{ display: "flex" }}>
                      {index === expandedAccordionIndex && (
                        <Tooltip title="Edit">
                          <IconButton
                            aria-label="edit"
                            disabled={enableSave}
                            onClick={(params: any) => {
                              handleEditButton(params);
                              params.preventDefault();
                              params.stopPropagation();
                            }}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                      <DeletePartsButton
                        createOrUpdatePartsMatrix={createOrUpdatePartsMatrix}
                        RowData={RowData}
                        fetchPartsMatrixRowDetails={fetchPartsMatrixRowDetails}
                      />
                    </Box>
                  </Box>
                </AccordionSummary>
                {rowData && index === expandedAccordionIndex ? (
                  <AccordionDetails>
                    <Grid container>
                      <Grid item xs={6}>
                        <Box sx={{ flex: 1 }}>
                          <Box sx={{ display: "flex", flexDirection: "row" }}>
                            {enableSave && (
                              <>
                                <Button
                                  style={{
                                    textTransform: "none",
                                    fontSize: "12px",
                                  }}
                                  onClick={() => {
                                    addEmptyRowForEdit();
                                  }}
                                >
                                  Add row
                                </Button>
                                <DeletePartsButton
                                  removeLastRow={handleRemoveLastRowForEdit}
                                  RowData={rowData}
                                  removeRow={true}
                                />
                              </>
                            )}
                          </Box>
                        </Box>
                        {matrixValidationMessage && (
                          <Typography
                            fontSize={"small"}
                            alignContent={"left"}
                            marginBottom={"5px"}
                            color={"red"}
                          >
                            {matrixValidationMessage}
                          </Typography>
                        )}
                        <Box>{getGridWrapper("view", RowData.gridData)}</Box>
                      </Grid>
                      {edit && index === expandedAccordionIndex && (
                        <Grid
                          item
                          xs={6}
                          style={{ marginTop: "90px", float: "right" }}
                        >
                          <NewMatrixForm
                            action={"edit"}
                            RowData={RowData}
                            rowData={rowData}
                            handleSubmitPartsMatrix={(values: any) =>
                              handleSubmitPartsMatrix(values, "edit")
                            }
                            setAddGrid={setAddMatrix}
                            component={"partsMatrix"}
                            addGrid={"matrix"}
                            matrixSourceList={matrixSource}
                            matrixTypeList={matrixType}
                            fetchPartsMatrixRowData={fetchPartsMatrixRowData}
                            enableSave={enableSave}
                            cancel={cancel}
                            setInstallDateValue={setInstallDateValue}
                            installDateValue={installDateValue}
                            updateDate={updateDate}
                            setUpdateDate={setUpdateDate}
                            setStatusMessage={setStatusMessage}
                            setOpenSnackbar={setOpenSnackbar}
                            setStatusMessageType={setStatusMessageType}
                          />
                        </Grid>
                      )}
                    </Grid>
                  </AccordionDetails>
                ) : !rowData && index === expandedAccordionIndex ? (
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                    }}
                  >
                    <CustomLinearProgress />
                  </Box>
                ) : (
                  <></>
                )}
              </Accordion>
            ))}
        </Paper>
      )}
    </Box>
  );
};

export default PartsMatrix;
