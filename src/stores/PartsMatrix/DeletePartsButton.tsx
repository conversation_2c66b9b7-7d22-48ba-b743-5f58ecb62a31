import { But<PERSON>, CircularProgress, I<PERSON><PERSON><PERSON>on, Tooltip } from "@mui/material";
import { useState } from "react";
import { Confirm, useRecordContext } from "react-admin";
import DeleteIcon from "@mui/icons-material/Delete";
const DeletePartsButton: React.FC<any> = (props) => {
  const {
    createOrUpdatePartsMatrix,
    RowData,
    removeLastRow,
    removeRow,
    fetchPartsMatrixRowDetails,
  } = props;

  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const record: any = useRecordContext();

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setOpen(true);
    if(!removeRow){
      fetchPartsMatrixRowDetails(RowData);
    }
  };

  const handleDialogClose = () => setOpen(false);

  const handleConfirm = () => {
    setLoading(true);
    const values = {
      installation_date: RowData.storeInstallDate,
      pCreatedDate: RowData.createdDate,
      matrixtype: RowData.matrixType,
      source: RowData.prtsource,
      storeId: record.storeId,
      partsFor: RowData.partsFor,
    };
    const newMatrix = {
      tableData: RowData,
      formData: { ...values },
    };
    createOrUpdatePartsMatrix(newMatrix, "delete").finally(() => {
      setLoading(false);
      setOpen(false);
    });
  };
  const handleremoveRowConfirm = () => {
    removeLastRow();
    setOpen(false);
  };
  return (
    <>
      {!removeRow ? (
        <Tooltip title="Delete">
          <IconButton aria-label="delete" onClick={handleClick}>
            {loading ? <CircularProgress size={24} /> : <DeleteIcon />}
          </IconButton>
        </Tooltip>
      ) : (
        <Button
          style={{
            textTransform: "none",
            fontSize: "12px",
          }}
          onClick={handleClick}
          disabled={RowData?.length <= 1}
        >
          Remove last row
        </Button>
      )}
      <Confirm
        isOpen={open}
        loading={loading}
        title={removeRow ? `Remove Last Row` : `Delete Matrix`}
        content={
          removeRow ? "DIALOG_BOX.ROW_DELETE" : "DIALOG_BOX.MATRIX_DELETE"
        }
        onConfirm={removeRow ? handleremoveRowConfirm : handleConfirm}
        onClose={handleDialogClose}
      />
    </>
  );
};
export default DeletePartsButton;
