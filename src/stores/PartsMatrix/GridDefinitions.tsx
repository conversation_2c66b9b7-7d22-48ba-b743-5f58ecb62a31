import { ColDef } from "ag-grid-community";
import { CellClassRules } from "ag-grid-community";
import { useMemo } from "react";
import { Constants } from "../../utils/constants";

const GridDefinitions = (saveClickedFlag: boolean) => {
  const formatCellValueRate = (params: any) => {
    if (
      params.value &&
      params.value !== "" &&
      Constants.patterns.numericalRegex.test(params.value)
    ) {
      return (
        "$" +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
      );
    } else {
      return params.value && "$" + params.value;
    }
  };
  const formatCellValue = (params: any) => {
    if (params.value && params.value != "") {
      return params.value + "%";
    } else {
      return "";
    }
  };

  const SetCellStyle = () => {
    return { textAlign: "right", border: " 0px white" };
  };

  const formatCellValueDescription = (data: any) => {
    return data.length > 15 ? data.substring(0, 15) + `...` : data;
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "From",
      cellEditor: "NumericEditor",
      editable: false,
      field: "priceStartRange",
      width: 40,
      suppressMenu: true,
      unSortIcon: true,
      valueFormatter: formatCellValueRate,
      cellClass: "twoDecimalPlacesWith$",
      cellClassRules: {
        "rag-red-outer": (params: any) => {
          return saveClickedFlag && params.data.priceStartRange == "";
        },
      },
      comparator: function (valueA: any, valueB: any) {
        return valueA - valueB;
      },
      cellStyle: SetCellStyle,
    },
    {
      headerName: "To",
      field: "priceEndRange",
      cellEditor: "agTextCellEditor",
      width: 40,
      valueFormatter: formatCellValueRate,
      suppressMenu: true,
      unSortIcon: true,
      cellClass: "twoDecimalPlacesWith$",
      cellClassRules: {
        "rag-red-outer": (params: any) => {
          return (
            (params.data.priceEndRange != "" &&
              Number(params.data.priceStartRange) >
                Number(params.data.priceEndRange)) ||
            (saveClickedFlag &&
              !Constants.patterns.numericalRegex.test(
                params.data.priceEndRange
              )) ||
            (saveClickedFlag && params.data.priceEndRange == "")
          );
        },
      },
      comparator: function (valueA: any, valueB: any) {
        return valueA - valueB;
      },
      cellStyle: SetCellStyle,
    },
    {
      headerName: "Base",
      cellEditor: "agSelectCellEditor",
      cellClassRules: {
        "rag-red-outer": (params: any) => {
          return saveClickedFlag && params.data.calcBase == "";
        },
      },
      cellStyle: SetCellStyle,
      cellEditorParams: {
        values: ["LIST-", "COST-", "LIST+", "COST+", "LIST%", "COST%"],
      },
      field: "calcBase",
      width: 180,
      suppressMenu: true,
      unSortIcon: true,
      comparator: function (valueA: any, valueB: any) {
        return valueA - valueB;
      },

      editable: true,
    },
    {
      headerName: "BreakField",
      cellEditor: "agSelectCellEditor",
      cellClassRules: {
        "rag-red-outer": (params: any) => {
          return saveClickedFlag && params.data.breakField == "";
        },
      },
      cellStyle: SetCellStyle,
      cellEditorParams: {
        values: ["LIST", "COST"],
      },
      field: "breakField",
      width: 180,
      suppressMenu: true,
      unSortIcon: true,
      comparator: function (valueA: any, valueB: any) {
        return valueA - valueB;
      },
      editable: true,
    },
    {
      headerName: "Percentage",
      cellEditor: "agTextCellEditor",
      field: "addPercentage",
      valueFormatter: formatCellValue,
      cellClass: "addPercentage",
      width: 180,
      suppressMenu: true,
      unSortIcon: true,
      cellClassRules: {
        "rag-red-outer": (params: any) => {
          return (
            (saveClickedFlag && params.data.addPercentage == "") ||
            (saveClickedFlag &&
              !Constants.patterns.numericalRegex.test(
                params.data.addPercentage
              ))
          );
        },
      },
      comparator: function (valueA: any, valueB: any) {
        return valueA - valueB;
      },
      cellStyle: SetCellStyle,
      editable: true,
    },
  ];

  const commonDefaultProps = {
    flex: 1,
    minWidth: 60,
    resizable: true,

    suppressMenu: true,

    enableValue: true,
  };
  const defaultColDef = useMemo(() => {
    const lastRowEditable = function (params: any) {
      return params.node.rowIndex === params.api.getDisplayedRowCount() - 1;
    };
    return {
      ...commonDefaultProps,
      editable: lastRowEditable,
    };
  }, []);

  return {
    columnDefs,
    defaultColDef,
    formatCellValueDescription,
    commonDefaultProps,
    formatCellValueRate,
    formatCellValue,
    SetCellStyle,
  };
};

export default GridDefinitions;
