import { useState, ChangeEvent } from "react";
import { Box, Tab, Tabs } from "@mui/material";
import { TabPanel } from "../Layout/TabPanel";
import { useTranslate } from "react-admin";

export const PostOnboard = () => {
  const [tabValue, setTabValue] = useState(0);
  const translate = useTranslate();
  const handleTabChange = (event: ChangeEvent<{}>, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box>
      <Box>
        <Tabs
          value={tabValue}
          indicatorColor="primary"
          textColor="primary"
          onChange={handleTabChange}
        >
          <Tab label={translate('TABS.DATA_REFRESH')} />
          <Tab label={translate('TABS.DAILY_LOAD_SETUP')} />
        </Tabs>
      </Box>
      <TabPanel value={tabValue} index={0}>
       
      </TabPanel>
      <TabPanel value={tabValue} index={1}>
      
      </TabPanel>
    </Box>
  );
};
