import React, { useState, useEffect } from "react";
import Grid from "@material-ui/core/Grid";
import { AgGridReact } from "ag-grid-react";
import MenuDefs from "./MenuDefs";
import SnackBarMessage from "../../../components/SnackBarMessage";
import { Constants } from "../../../utils/constants";

const MenuMappingGrid = (mappingType: any) => {
  const {
    leftRowData,
    defaultColDefLeft,
    leftColumnDefs,
    rightRowData,
    rightColumnDefs,
    defaultColDefRight,
    autoGroupColumnDef,
    autoGroupColumnDefright,
    gridRef,
    openSnackbar,
    statusMessage,
    statusMessageType,
    onGridReady,
    handleSelectionChanged,
    setOpenSnackbar,
  } = MenuDefs();

  const getGridWrapper = (id: number) => (
    <div className={Constants.ag_grid_theme} style={{ height: "65vh", width: "400px" }}>
      <AgGridReact
        groupAllowUnbalanced
        className={Constants.ag_grid_theme}
        defaultColDef={id === 0 ? defaultColDefLeft : defaultColDefRight}
        rowData={id === 0 ? leftRowData : rightRowData}
        columnDefs={id === 0 ? leftColumnDefs : rightColumnDefs}
        autoGroupColumnDef={
          id == 0 ? autoGroupColumnDef : autoGroupColumnDefright
        }
        rowSelection={id === 0 ? "multiple" : undefined}
        rowDragMultiRow={true}
        suppressRowClickSelection={id === 0}
        suppressMoveWhenRowDragging={id === 0}
        rowDragEntireRow={true}
        groupSelectsChildren={true}
        onGridReady={(params) => onGridReady(params, id)}
        rowDragText={(params: any, dragItemCount: any) => {
          if (dragItemCount > 1) {
            return dragItemCount + "models";
          }
          return params?.rowNode?.data?.modelName;
        }}
        ref={gridRef}
        onSelectionChanged={(params) => handleSelectionChanged(params, id)}
        // rowHeight={35}
      />
    </div>
  );
  return (
    <>
      <Grid container spacing={2} style={{ padding: 8 }}>
        <Grid item>{getGridWrapper(0)}</Grid>
        <Grid item>{getGridWrapper(1)}</Grid>
      </Grid>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={statusMessageType}
      />
    </>
  );
};

export default MenuMappingGrid;
