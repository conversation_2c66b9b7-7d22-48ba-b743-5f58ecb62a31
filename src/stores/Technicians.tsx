import "@ag-grid-community/all-modules/dist/styles/ag-grid.css";
import "@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-balham.css";
import "ag-grid-community/styles/ag-theme-material.css";
import { useRef, useState } from "react";
import "../style.css";
import { updateTechnicianStatus } from "../service/mutations";
import { useRecordContext, useTranslate } from "react-admin";
import { Constants } from "../utils/constants";
import { Box } from "@mui/material";
import TechGridDefs from "./Technicians/TechGridDefs";
import useServiceTechnicians from "../CustomHooks/useServiceTechnicians";
import SnackBarMessage from "../components/SnackBarMessage";

function Technicians() {
  const record = useRecordContext();
  const gridApiRef: any = useRef();
  let gridRef = useRef<any>();
  const gridColumnApiRef: any = useRef();
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [originalRowData, setOriginalRowData] = useState<any>(null);
  const [saveNewValue, setsaveNewValue] = useState(false);
  const { columnDefs, defaultColDef } = TechGridDefs(record.storeLaunched);
  const translate = useTranslate();
  const { fetchTechnicians, rowData, getRowStyle, onRowEditingStarted } =
    useServiceTechnicians(record);
  const updateTechnician = (updatedData: any) => {
    var nickName = updatedData.nickname;
    nickName = nickName ? nickName.trim() : "";
    var techStatus = updatedData.active;
    const pValData: any = {
      techno: updatedData.lbrtechno,
      pCategorized: techStatus == null ? 0 : 1,
      statusval: updatedData.active,
      nickName: nickName ? nickName.trim() : null,
    };
    updateTechnicianStatus(
      pValData,
      record.storeId,
      record.realmName,
    )
      .then((result: any) => {
        if (result) {
          fetchTechnicians();
          setOpenSnackbar(true);
        }
      })
      .catch((error: any) => {
        console.log(error);
        // ToDo: Need show Error Message
      });
  };

  const onGridReady = (params: any) => {
    gridApiRef.current = params.api;
    gridRef.current = params.api;
    gridColumnApiRef.current = params.columnApi;
    fetchTechnicians();
  };

  const onCellClicked = (params: any) => {
    const target = params.event.target;
    let action = target.closest("[data-action]")?.dataset.action;
    // Handle click event for action cells
    if (params.column.colId === "action" && action) {
      if (action === Constants.actions.edit) {
        const node = params.node;
        const data = node.data;
        params.api.startEditingCell({
          rowIndex: params.node.rowIndex,
          // gets the first columnKey
          colKey:
            gridColumnApiRef?.current?.getDisplayedCenterColumns()[0].colId,
        });
        setOriginalRowData({ ...data });
      }
      if (action === Constants.actions.update) {
        setsaveNewValue(true);
        params.api.stopEditing(false);
      }
      if (action === Constants.actions.cancel) {
        params.api.stopEditing(true);
      }
    }
  };

  const onRowEditingStopped = (params: any) => {
    const { api, node } = params;
    const editedData = node.data;
    const originalData: any = originalRowData;
    if (originalData) {
      if (!saveNewValue) {
        node.setData(originalData);
      } else {
        const updatedData = { ...originalData, ...editedData };
        updatedData.nickname = updatedData?.nickname?.replace(/\s+/g, ' ');
        node.setData(updatedData);
        (editedData.nickname != originalData.nickname ||
          updatedData.active != originalData.active) &&
          updateTechnician(updatedData);
      }
    }
    setsaveNewValue(false);
  };

  return (
    <Box sx={{ marginTop: 5 }}>
      <div className={Constants.ag_grid_theme} style={{ height: "70vh", width: "50%" }}>
        <AgGridReact
          onRowEditingStopped={onRowEditingStopped}
          onRowEditingStarted={onRowEditingStarted}
          onCellClicked={onCellClicked}
          editType="fullRow"
          suppressClickEdit={true}
          suppressColumnVirtualisation={true}
          columnDefs={columnDefs}
          suppressChangeDetection={true}
          onGridReady={onGridReady}
          rowData={rowData}
          suppressCellSelection={true}
          suppressRowClickSelection={true}
          defaultColDef={defaultColDef}
          tooltipShowDelay={0}
          getRowStyle={getRowStyle}
        />
        <SnackBarMessage
          onClose={() => setOpenSnackbar(false)}
          open={openSnackbar}
          message={translate("SUCCESS_MESSAGES.UPDATE_MESSAGE", {
            entityName: "Technicians details",
          })}
        />
      </div>
    </Box>
  );
}

export default Technicians;
