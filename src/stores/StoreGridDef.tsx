import { Link } from "@mui/material";
import { ColDef } from "ag-grid-community";
import React, { useMemo, useState } from "react";
import { ICellRendererParams } from "ag-grid-community";
import CancelIcon from "@mui/icons-material/Cancel";
import StoreQueries from "../service/DataFetchQueries/storeQueries";
import { PageRoutes } from "../utils/pageRoutes";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { Link as RouterLink } from "react-router-dom";
import moment from "moment";
import { exportMultipleSheetsAsExcel } from "ag-grid-enterprise";

const StoreGridDef = () => {
  const [api, setApi] = useState<any>(null);

  const DateRenderer = (params: ICellRendererParams) => {
    return (
      <span>
        {params.value ? moment(params.value).format("MM/DD/YYYY") : ""}
      </span>
    );
  };

  const StatusRender = (params: ICellRendererParams) => {
    return (
      <div title={params.data.remarks}>
        <span>
          {params.data.isDeleted ? (
            <CancelIcon style={{ color: "red", fontSize: "1.3rem" }} />
          ) : (
            <CheckCircleIcon sx={{ color: "green", fontSize: "1.3rem" }} />
          )}
        </span>
      </div>
    );
  };
  const SetCellStyle = (params: any) => {
    return {
      color: "green",
      textAlign: "center",
    };
  };
  const statusFont = (params: any) => {
    return {
      color: params.value === "Failed" ? "red" : "green",
      fontWeight: "bold",
    };
  };
  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        field: "storeName",
        headerName: "Store",
        width: 350,
        cellRenderer: StoreRenderer,
        headerStyle: {
          backgroundColor: "#2196f3",
          color: "white",
          fontWeight: "bold",
        },
      },
      { field: "tenantName", headerName: "Tenant", width: 350 },
      { field: "dms", headerName: "DMS", width: 150 },
      {
        field: "agreementDate",
        headerName: "Contract Signed Date",
        width: 150,
        valueFormatter: (params: any) =>
          params.value ? moment(params.value).format("MM/DD/YYYY") : "---",
      },
      {
        field: "bulkPhase1",
        headerName: "Bulk Phase 1",
        width: 150,
      },
      { field: "bulkPhase2", headerName: "Bulk Phase 2", width: 150 },
      {
        field: "dailyLoad",
        headerName: "Daily Load Setup",
        width: 150,
      },
      {
        field: "noofLaborGrids",
        headerName: "Labor Grids Count",
        width: 100,
      },
      {
        field: "noofPartsMatrix",
        headerName: "Parts Matrix Count",
        width: 100,
      },
      { field: "nsQaValidation", headerName: "Q/A Validation", width: 150 },
      { field: "review", headerName: "Review", width: 150 },
    ],
    []
  );

  const defaultColDef = useMemo<ColDef>(
    () => ({
      cellStyle: { textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      width: 180,
      suppressSizeToFit: true,
      filterParams: {
        applyMiniFilterWhileTyping: true,
      },
    }),
    []
  );

  const handleExport = (api: any, fileName: string, sheetName: string) => {
    const spreadsheets: any[] = [];
    spreadsheets.push(api.getSheetDataForExcel({ sheetName }));

    exportMultipleSheetsAsExcel({
      data: spreadsheets,
      fileName,
    });
  };

  const onBtnExport = () => {
    handleExport(api!, "Store List.xlsx", "Store List");
  };

  return {
    columnDefs,
    defaultColDef,
    onBtnExport,
  };
};

const StoreRenderer = (params: ICellRendererParams) => {
  const url = PageRoutes.getStoreDetailsRoute(params.data.id);
  return (
    <Link component={RouterLink} to={url} underline="hover">
      {params.value}
    </Link>
  );
};
export default StoreGridDef;
