import { ColDef } from "ag-grid-community";
import { CellClassRules } from "ag-grid-community";
import { useMemo } from "react";
import { Constants } from "../../utils/constants";

const GridDefinitions = (saveClickedFlag: boolean, noOfRows: number) => {
  const FormatCellValueRate = (params: any) => {
    const { colDef, data, value } = params;
    const formattedValue = (num: number) =>
      `$${num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;

    if (colDef.field === "col0" && data.hours === 0) {
      return formattedValue(value ? parseFloat(value) : 0);
    }

    if (value && Constants.patterns.numericalRegex.test(value)) {
      return formattedValue(parseFloat(value));
    }

    return value === "" ? "$" : value;
  };

  const SetCellStyle = () => {
    return { textAlign: "right", border: " 0px white" };
  };

  const SetCellBorderStyle: CellClassRules = {
    "rag-red-outer": (params: any) => {
      const regex = /^\d{1,5}(\.\d{1,2})?$/;
      return (
        saveClickedFlag &&
        (params.data[params.colDef.field] === "" ||
          !Constants.patterns.numericalRegex.test(params.value) ||
          !regex.test(params.value))
      );
    },
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "Hours Sold",
      minWidth: 100,
      width: 100,
      field: "hours",
      editable: false,
    },
    {
      headerName: "0",
      width: 80,
      field: "col0",
      cellEditor: "agTextCellEditor",
      valueFormatter: FormatCellValueRate,
      cellClassRules: SetCellBorderStyle,
      editable: (params: any) => params.data.hours !== 0,
    },
    {
      headerName: "0.1",
      width: 80,
      field: "col1",
      cellEditor: "agTextCellEditor",
      valueFormatter: FormatCellValueRate,
      cellClassRules: SetCellBorderStyle,
    },
    {
      headerName: "0.2",
      width: 80,
      field: "col2",
      cellEditor: "agTextCellEditor",
      valueFormatter: FormatCellValueRate,
      cellClassRules: SetCellBorderStyle,
    },
    {
      headerName: "0.3",
      width: 80,
      field: "col3",
      cellEditor: "agTextCellEditor",
      valueFormatter: FormatCellValueRate,
      cellClassRules: SetCellBorderStyle,
    },
    {
      headerName: "0.4",
      width: 80,
      field: "col4",
      cellEditor: "agTextCellEditor",
      valueFormatter: FormatCellValueRate,
      cellClassRules: SetCellBorderStyle,
    },
    {
      headerName: "0.5",
      width: 80,
      field: "col5",
      cellEditor: "agTextCellEditor",
      valueFormatter: FormatCellValueRate,
      cellClassRules: SetCellBorderStyle,
    },
    {
      headerName: "0.6",
      width: 80,
      field: "col6",
      cellEditor: "agTextCellEditor",
      valueFormatter: FormatCellValueRate,
      cellClassRules: SetCellBorderStyle,
    },
    {
      headerName: "0.7",
      width: 80,
      field: "col7",
      cellEditor: "agTextCellEditor",
      valueFormatter: FormatCellValueRate,
      cellClassRules: SetCellBorderStyle,
    },
    {
      headerName: "0.8",
      width: 80,
      field: "col8",
      cellEditor: "agTextCellEditor",
      valueFormatter: FormatCellValueRate,
      cellClassRules: SetCellBorderStyle,
    },
    {
      headerName: "0.9",
      width: 80,
      field: "col9",
      cellEditor: "agTextCellEditor",
      valueFormatter: FormatCellValueRate,
      cellClassRules: SetCellBorderStyle,
    },
  ];

  const defaultColDef = useMemo(() => {
    return {
      flex: 1,
      minWidth: 80,
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      editable: true,
      cellStyle: SetCellStyle,
      suppressMovable: true,
    };
  }, []);

  const LaborGridCols = {
    col0: "",
    col1: "",
    col2: "",
    col3: "",
    col4: "",
    col5: "",
    col6: "",
    col7: "",
    col8: "",
    col9: "",
  };

  const gridData: any[] = Array.from({ length: 16 }, (_, index) => ({
    hours: index,
    ...LaborGridCols,
  }));

  return {
    columnDefs,
    defaultColDef,
    gridData,
    LaborGridCols,
  };
};

export default GridDefinitions;
