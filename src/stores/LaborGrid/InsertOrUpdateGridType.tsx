import React, { useEffect } from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import {
  Backdrop,
  Box,
  Checkbox,
  CircularProgress,
  FormControlLabel,
  IconButton,
  Tooltip,
  Typography,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
// import { updateMatrixType } from "../service/mutations";
import {
  Confirm,
  Form,
  TextInput,
  useRecordContext,
  useTranslate,
} from "react-admin";
import { Constants } from "../../utils/constants";
import LaborGridMutations from "../../service/LaborGrid/LaborGridMutations";
import EditIcon from "@mui/icons-material/Edit";
import { Delete } from "@mui/icons-material";

export default function InsertOrUpdateGridType(props: any) {
  const translate = useTranslate();
  const record: any = useRecordContext();
  const {
    getGridTypesList,
    hasDefaultType,
    isGridModal,
    setStatusMessage,
    setOpenSnackbar,
    setStatusMessageType,
    isAdd,
    editGridName,
    gridCount,
    isDefaultGridType,
    getLaborGridList,
  } = props;
  const { InsertUpdateGridType } = LaborGridMutations;
  const [open, setOpen] = React.useState(false);
  const [errorMsg, setErrorMsg] = React.useState("");
  const [loading, setLoading] = React.useState(false);

  const [iconAction, setIconAction] = React.useState("");
  const [checked, setChecked] = React.useState(false);
  const [openDelete, setOpenDelete] = React.useState(false);
  const [defaultConfirmed, setDefaultConfirmed] = React.useState(false);
  const [openDeleteConfirm, setOpenDeleteConfirm] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setErrorMsg("");
    setOpen(false);
    setChecked(false);
  };

  const submitGridType = async (gridType?: any) => {
    const input = {
      inOldGridName: iconAction === "insert" ? gridType : editGridName,
      inNewGridName: iconAction === "delete" ? editGridName : gridType,
      inStoreId: record.storeId,
      inTenantId: record.tenantId,
      Realm: record.realmName,
      inIsDefaultGridName: isDefaultGridType ? isDefaultGridType : checked,
      inActivity: iconAction,
    };
    try {
      const result: any = await InsertUpdateGridType(input);
      if (result[0].status === 1) {
        const statusMessage = translate(result[0]?.msg);
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
        setStatusMessageType(Constants.statusType.success);

        setLoading(false);
        handleClose();
        setErrorMsg("");
        getGridTypesList(record);
        iconAction === "update" &&
          getLaborGridList({
            realm: record.realmName,
            storeId: record.storeId,
            callType: "Grid_Type",
          });
      } else {
        const statusMessage = translate(result[0]?.msg);
        if (iconAction === "update" || iconAction === "insert") {
          setErrorMsg(statusMessage);
        } else {
          setStatusMessage(statusMessage);
          setOpenSnackbar(true);
          setStatusMessageType(Constants.statusType.error);
          handleClose();
          setLoading(false);
        }
      }
    } catch (error) {
      const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
      setStatusMessageType(Constants.statusType.error);
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
      handleClose();
      setLoading(false);
    }
  };

  const submitForm = async (values: any) => {
    setLoading(true);
    if (iconAction === "delete") {
      submitGridType();
      return;
    }
    const gridType = values.gridType.trim();
    if (!gridType || /\s{2,}/.test(gridType)) {
      setErrorMsg("Invalid Grid Name");
    } else {
      submitGridType(gridType);
      // setErrorMsg("");
    }
  };

  const validateGridType = (value: any) => {
    if (!value) return "Please enter Grid Name";
    const trimmedValue = value.trim();
    if (value.length > 50) return "Grid name cannot be more than 50 characters";
    if (trimmedValue !== value)
      return "Grid Name cannot start or end with spaces";
    if (/\s{2,}/.test(trimmedValue))
      return "Grid Name cannot contain multiple consecutive spaces";
    if (!Constants.patterns.gridName.test(value))
      return "Grid Name cannot contain special characters";
    return undefined;
  };
  const handleChangeCheckbox = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChecked(event.target.checked);
  };
  const handleConfirm = () => {
    setLoading(true);
    if (openDeleteConfirm) {
      submitGridType(editGridName);
      setOpenDeleteConfirm(false);
    } else {
      setOpenDelete(false);
      setChecked(true);
      setDefaultConfirmed(true);
    }
  };

  const handleDialogClose = () => {
    setOpenDeleteConfirm(false);
    setOpenDelete(false);
    setDefaultConfirmed(true);
  };

  return (
    <>
      {/* UI code */}
      {isGridModal && isAdd ? (
        // Button inside
        <Button
          color="primary"
          variant="outlined"
          sx={{ textTransform: "none", mt: 1 }}
          onClick={() => {
            setIconAction("insert");
            handleClickOpen();
          }}
        >
          Add new grid name
        </Button>
      ) : isGridModal && !isAdd ? (
        <Box sx={{ display: "flex" }}>
          <Tooltip title="Edit">
            <IconButton
              onClick={() => {
                setIconAction("update");
                handleClickOpen();
              }}
              sx={{
                color: "#003d6b",
                padding: "0px 8px",
              }}
            >
              <EditIcon style={{ fontSize: 15, color: "#003d6b" }} />
            </IconButton>
          </Tooltip>
          <Tooltip title="Delete">
            <IconButton
              onClick={() => {
                setIconAction("delete");
                handleClickOpen();
              }}
              sx={{
                color: "#003d6b",
                padding: "0px 8px",
              }}
            >
              <Delete style={{ fontSize: 15, color: "#003d6b" }} />
            </IconButton>
          </Tooltip>
        </Box>
      ) : (
        <Tooltip title={"Add new grid name"}>
          <IconButton
            onClick={() => {
              setIconAction("insert");
              handleClickOpen();
            }}
            style={{ height: "fit-content", marginTop: "14px " }}
          >
            <AddIcon />
          </IconButton>
        </Tooltip>
      )}

      {/* Confirm modal for delete button of Grid Type*/}
      <Confirm
        isOpen={openDelete}
        title={"Delete Grid Name"}
        content={`Do you want to delete ${editGridName}?`}
        cancel={"Cancel"}
        confirm={"Confirm"}
        onConfirm={handleConfirm}
        onClose={handleDialogClose}
      />

      {/* Dialog box code */}
      <Dialog fullWidth open={open} onClose={handleClose}>
        {loading && (
          <Backdrop
            open={loading}
            style={{
              zIndex: 1301,
              color: "#fff",
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
            }}
          >
            <CircularProgress color="inherit" />
          </Backdrop>
        )}
        <DialogTitle
          variant="h6"
          color="#003d6b"
          sx={{
            fontSize: "16px",
            fontWeight: "bold",
            padding: "18px 18px 4px",
          }}
        >
          {iconAction === "insert"
            ? "Add Grid Name"
            : iconAction === "update"
            ? "Edit Grid Name"
            : "Delete Grid Name"}
        </DialogTitle>
        <Form onSubmit={submitForm}>
          <DialogContent sx={{ padding: "0px 20px" }}>
            {iconAction === "delete" ? (
              <Typography>Do you want to delete '{editGridName}'?</Typography>
            ) : (
              <>
                {" "}
                <TextInput
                  defaultValue={iconAction === "insert" ? "": editGridName}
                  autoFocus
                  margin="dense"
                  id="gridType"
                  name="gridType"
                  label="Grid Name"
                  type="text"
                  fullWidth
                  variant="standard"
                  source="gridType"
                  validate={validateGridType}
                  helperText={false}
                  onChange={() => {
                    setErrorMsg("");
                  }}
                />
                {errorMsg && (
                  <div style={{ color: "red", fontSize: 12 }}>{errorMsg}</div>
                )}
                {!isAdd && gridCount > 0 && !isDefaultGridType && (
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={checked}
                        onChange={handleChangeCheckbox}
                        sx={{ "& .MuiSvgIcon-root": { fontSize: 16 } }}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "14px" }}>
                        Set as default grid
                      </Typography>
                    }
                  />
                )}
              </>
            )}
          </DialogContent>
          <DialogActions>
            <Button
              color="primary"
              variant="outlined"
              type="submit"
              sx={{ textTransform: "none" }}
              disabled={loading}
            >
              {iconAction === "delete" ? "Confirm" : "Submit"}
            </Button>
            <Button
              color="primary"
              variant="contained"
              sx={{ m: 2, textTransform: "none" }}
              onClick={() => handleClose()}
            >
              Cancel
            </Button>
          </DialogActions>
        </Form>
      </Dialog>
    </>
  );
}
