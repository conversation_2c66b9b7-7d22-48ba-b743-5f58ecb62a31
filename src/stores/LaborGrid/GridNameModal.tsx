import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Modal,
  IconButton,
  FormControl,
  FormLabel,
  RadioGroup,
  Radio,
  FormControlLabel,
  Typography,
} from "@mui/material";
import HighlightOffIcon from "@material-ui/icons/HighlightOff";
import CircleIcon from "@mui/icons-material/Circle";
import { Constants } from "../../utils/constants";
import InsertOrUpdateGridType from "./InsertOrUpdateGridType";
import SnackBarMessage from "../../components/SnackBarMessage";

const GridNameModal = (props: any) => {
  const {
    openGridTypeModal,
    setOpenGridTypeModal,
    setGridTypeChanged,
    setUpdatedDefaultGridType,
    hasDefaultType,
    gridTypeChoice,
    updatedDefaultGridType,
    defaultGridType,
    getGridTypesList,
    setStatusMessage,
    setOpenSnackbar,
    setStatusMessageType,
    gridTypeChanged,
    saveDefaultGridType,
    record,
    getLaborGridList,
    openSnackbar,
    statusMessage,
    statusMessageType
  } = props;
  return (
    <Modal
      open={openGridTypeModal}
      onClose={() => setOpenGridTypeModal(false)}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <>
      <Box
        sx={{
          display: "flex",
          position: "absolute" as "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: "20%",
          bgcolor: "background.paper",
          border: "1px solid #6a6a6a",
          boxShadow: 24,
          p: "16px 24px",
          borderRadius: "10px",
        }}
      >
        <FormControl sx={{ width: "100%", m: 0 }}>
          <FormLabel id="demo-controlled-radio-buttons-group">
            <Box sx={{ display: "flex" }}>
              <Typography
                id="modal-modal-title"
                variant="h6"
                color="#003d6b"
                sx={{ fontSize: "14px", fontWeight: "bold" }}
              >
                All Model Grid Names
              </Typography>
              <IconButton
                onClick={() => {
                  setGridTypeChanged(false);
                  setUpdatedDefaultGridType("");
                  setOpenGridTypeModal(false);
                }}
                sx={{
                  position: "absolute",
                  right: "5px",
                  color: "#003d6b",
                  padding: 0,
                  opacity: 0.5,
                }}
              >
                <HighlightOffIcon style={{ fontSize: 20, color: "#003d6b" }} />
              </IconButton>
            </Box>
          </FormLabel>
          {gridTypeChoice.length > 0 ? (
            <>
              <RadioGroup
                aria-labelledby="demo-controlled-radio-buttons-group"
                name="controlled-radio-buttons-group"
                value={updatedDefaultGridType}
                onChange={(event) => {
                  setGridTypeChanged(
                    event.target.value === defaultGridType ? false : true
                  );
                  setUpdatedDefaultGridType(event.target.value);
                }}
              >
                {gridTypeChoice.map((gridType: any) => {
                  return hasDefaultType ? (
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        flexWrap: "wrap",
                        justifyContent: "space-between",
                      }}
                    >
                      <Typography
                        sx={{
                          mt: "8px",
                          display: "flex",
                          alignItems: "center",
                          fontSize: "12px",
                        }}
                      >
                        <CircleIcon
                          sx={{
                            fontSize: "6px !important",
                            color: "#003d6b",
                            mr: "8px",
                          }}
                        ></CircleIcon>{" "}
                        {gridType.name}&nbsp;
                        <span
                          style={{
                            fontStyle: "italic",
                            opacity: 0.7,
                            fontWeight: gridType.isDefaultGridType
                              ? "bold"
                              : "",
                          }}
                        >
                          {gridType.isDefaultGridType
                            ? "(Default)"
                            : `(Grid Count: ${gridType.count})`}
                        </span>
                      </Typography>
                      {/* edit and delete button when All Model Grid names is clicked and already has a default grid type */}
                      <InsertOrUpdateGridType
                        getGridTypesList={getGridTypesList}
                        hasDefaultType={hasDefaultType}
                        isGridModal={true}
                        isAdd={false}
                        editGridName={gridType.name}
                        setStatusMessage={setStatusMessage}
                        setOpenSnackbar={setOpenSnackbar}
                        setStatusMessageType={setStatusMessageType}
                        gridCount={gridType.count}
                        isDefaultGridType={gridType.isDefaultGridType}
                        getLaborGridList={getLaborGridList}
                      />
                    </Box>
                  ) : (
                    <>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                          padding: "6px",
                          ml: gridType.count == 0 ? "18px" : "",
                        }}
                      >
                        <FormControlLabel
                          key={gridType.id}
                          value={gridType.id}
                          control={
                            gridType.count != 0 ? (
                              <Radio
                                disabled={gridType.count == 0}
                                color="primary"
                                sx={{
                                  "& .MuiSvgIcon-root": {
                                    fontSize: 14,
                                  },
                                  p: 0.25,
                                }}
                              />
                            ) : (
                              <></>
                            )
                          }
                          label={
                            <Typography sx={{ fontSize: "12px", ml: 1 }}>
                              {gridType.name}&nbsp;
                              <span
                                style={{ fontStyle: "italic", opacity: 0.7 }}
                              >
                                {`(Grid Count: ${gridType.count})`}
                              </span>
                            </Typography>
                          }
                          labelPlacement="end"
                        />
                        <InsertOrUpdateGridType
                          getGridTypesList={getGridTypesList}
                          hasDefaultType={hasDefaultType}
                          isGridModal={true}
                          isAdd={false}
                          editGridName={gridType.name}
                          setStatusMessage={setStatusMessage}
                          setOpenSnackbar={setOpenSnackbar}
                          setStatusMessageType={setStatusMessageType}
                          gridCount={gridType.count}
                          isDefaultGridType={gridType.isDefaultGridType}
                          getLaborGridList={getLaborGridList}
                        />
                      </Box>
                    </>
                  );
                })}
              </RadioGroup>
              {/* save button when All Model Grid names is clicked, has grid types and has noooooooooo default grid type */}
              {!hasDefaultType && (
                <Button
                  color="primary"
                  variant="outlined"
                  sx={{ textTransform: "none", mt: 1 }}
                  disabled={!gridTypeChanged}
                  onClick={() => saveDefaultGridType(record)}
                >
                  Set as Default Grid
                </Button>
              )}
            </>
          ) : (
            // if there is no grid type created yet
            <>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  mt: 2,
                  height: "100px",
                  border: `1px solid ${Constants.colors.greyBorder}`,
                  borderRadius: "5px",
                  alignItems: "center",
                }}
              >
                <Typography
                  sx={{ color: Constants.colors.greyText, fontSize: "12px" }}
                >
                  No grid names to list.{" "}
                </Typography>
              </Box>
              <InsertOrUpdateGridType
                getGridTypesList={getGridTypesList}
                hasDefaultType={hasDefaultType}
                isGridModal={true}
                isAdd={true}
                setStatusMessage={setStatusMessage}
                setOpenSnackbar={setOpenSnackbar}
                setStatusMessageType={setStatusMessageType}
              />
            </>
          )}
        </FormControl>
      </Box>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={statusMessageType}
      />
       </>
    </Modal>
   
  );
};

export default GridNameModal;
