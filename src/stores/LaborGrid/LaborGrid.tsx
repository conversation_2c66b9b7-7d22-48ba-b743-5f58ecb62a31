import {
  Box,
  Button,
  Grid,
  Paper,
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
  Tooltip,
  CircularProgress,
  Backdrop,
} from "@mui/material";
import * as React from "react";
import { useState, useRef, useEffect } from "react";
import { AgGridReact } from "ag-grid-react";
import { useRecordContext, useTranslate } from "react-admin";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import EditIcon from "@mui/icons-material/Edit";
import SaveIcon from "@mui/icons-material/Save";
import GridDefinitions from "./GridDefinitions";
import useLaborGrid from "../../CustomHooks/useLaborGrid";
import NewGridOrMatrix from "../../components/NewGridOrMatrix";
import DeleteButton from "../../components/DeleteButton";
import CancelIcon from "@mui/icons-material/Cancel";
import DatePicker from "react-datepicker";
import CustomDatePickerInput from "../../components/CustomDatePickerInput";
import SnackBarMessage from "../../components/SnackBarMessage";
import CustomLinearProgress from "../../components/CustomLinearProgress";
import Loading from "../../components/Loading";
import moment from "moment";
import { UpdateSubmitStatus } from "../../service/mutations";
import { Constants } from "../../utils/constants";
import GridNameModal from "./GridNameModal";
import { log } from "console";
import MultipleSnackbars from "../../components/MultipleSnackbars";
const LaborGrid = (props: any) => {
  const { getStoreQuery } = props;
  const {
    onRowEditingStopped,
    setTempData,
    tempData,
    getLaborGridList,
    setOpenSnackbar,
    setEnableSave,
    setIncompleteError,
    setInvalidLength,
    handleSubmitLaborGrid,
    setAddGrid,
    setSaveClickedFlag,
    setInvalidError,
    setInstallDate,
    setRowData,
    setGridType,
    gridType,
    invalidError,
    invalidLength,
    statusMessage,
    openSnackbar,
    rowData,
    addGrid,
    gridTypeList,
    saveClickedFlag,
    enableSave,
    incompleteError,
    installDate,
    listLoading,
    installDateValue,
    setInstallDateValue,
    setStatusMessage,
    updateDate,
    setUpdateDate,
    statusMessageType,
    setStatusMessageType,
    getGridTypesList,
    defaultGridType,
    hasDefaultType,
    gridTypeChoice,
    saveDefaultGridType,
    setUpdatedDefaultGridType,
    updatedDefaultGridType,
    setOpenGridTypeModal,
    openGridTypeModal,
    setGridTypeChanged,
    gridTypeChanged,
    newDefaultGridType,
    setNewDefaultGridType,
    reassignGridType,
    openDelete,
    setOpenDelete,
    setReassignGridType,
    isSuccessful,
    noOfRows,
    setNoOfRows,
    snackbarMessages,
    setSnackbarMessages,
    submitLoader,
    setSubmitLoader,
  } = useLaborGrid();
  const { columnDefs, defaultColDef, gridData, LaborGridCols } =
    GridDefinitions(saveClickedFlag, noOfRows);
  const record: any = useRecordContext();
  const translate = useTranslate();
  const gridRef = useRef<any>();
  const [initialRowData, setInitialRowData] = useState<any>();
  const [expandedAccordionIndex, setExpandedAccordionIndex] = useState(null);
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const [gridTypeError, setGridTypeError] = useState<boolean>(false);
  const [gridHeight, setGridHeight] = useState<number>(405);
  useEffect(() => {
    getLaborGridList({
      realm: record.realmName,
      storeId: record.storeId,
      callType: "Grid_Type",
    });
  }, []);

  const onGridReady = (params: any) => {
    gridRef.current = params.api;
    setInitialRowData(gridData);
    setTempData(gridData);
    !rowData &&
      setTimeout(() => {
        let rowIndex = 0;
        gridRef.current.setFocusedCell(rowIndex, "col0");
        gridRef.current.startEditingCell({
          rowIndex,
          colKey: "col0",
        });
      }, 100);
    setGridHeight(noOfRows * 25 + 40);
  };

  const getGridWrapper = (action?: string, RowData?: any) => {
    return (
      <>
        <div
          className={Constants.ag_grid_theme}
          style={{
            height: gridHeight,
            width: 1200,
            minHeight: 100,
          }}
        >
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            onGridReady={onGridReady}
            rowData={action === "add" ? initialRowData : RowData}
            defaultColDef={defaultColDef}
            rowHeight={25}
            rowSelection="single"
            animateRows={true}
            // @ts-ignore
            ref={gridRef}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            onRowEditingStopped={onRowEditingStopped}
            stopEditingWhenCellsLoseFocus={true}
            onRowEditingStarted={() => setEnableSave(true)}
            suppressClickEdit={enableSave ? false : true}
            //suppressRowClickSelection={enableSave ? false : true}
          />
        </div>
      </>
    );
  };

  useEffect(() => {
    getGridTypesList(record);
  }, []);

  const handleRadioChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setExpandedAccordionIndex(null);
    setAddGrid((event.target as HTMLInputElement).value);
    setInstallDateValue("");
    setUpdateDate(false);
    setNoOfRows(16);
    if (event.target.value === "grid") {
      setSaveClickedFlag(false);
      setRowData(null);
      setIncompleteError(false);
      setInvalidLength(false);
      setInvalidError(false);
    } else if (event.target.value === "upload") {
    } else if (event.target.value === "fixed_rate") {
    }
  };

  const formatCellValueDescription = (gridTypeData: any) => {
    return gridTypeData.gridType.length > 15
      ? gridTypeData.gridType.substring(0, 15) + `...`
      : gridTypeData.gridType;
  };

  const handleSubmit = async () => {
    setSubmitLoading(true);
    try {
      const result = await UpdateSubmitStatus(
        record.tenantId,
        record.storeId,
        "labor_grid"
      );
      if (result?.results[0]?.status === 1) {
        const statusMessage = translate(result?.results[0]?.msg);
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
        getStoreQuery();
        setStatusMessageType(Constants.statusType.success);
      }
    } catch (error) {
      const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
      setStatusMessageType(Constants.statusType.error);
    } finally {
      setSubmitLoading(false);
    }
  };

  const addOrRemoveGridRow = (action: string, rowCount: number) => {
    let dummy;
    if (action === "add") {
      dummy = [...tempData, { hours: rowCount - 1, ...LaborGridCols }];
      setInitialRowData(dummy);
      setTempData(dummy);
      setRowData(dummy);
    } else if (action === "remove") {
      dummy = tempData.filter((item: any) => item.hours !== noOfRows - 1);
      setInitialRowData(dummy);
      setTempData(dummy);
      setRowData(dummy);
    }
    setGridHeight(rowCount * 25 + 40);
  };

  const cancelGridAddition = () => {
    setAddGrid("");
    setExpandedAccordionIndex(null);
    setInstallDateValue("");
    setUpdateDate(false);
    setNoOfRows(16);
    setRowData(null);
  };

  return (
    <Box sx={{ margin: "8px" }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          padding: "0 18px",
        }}
      >
        <Button
          size="small"
          variant="outlined"
          color="primary"
          sx={{
            textTransform: "none",
          }}
          onClick={() => {
            setUpdatedDefaultGridType("");
            setOpenGridTypeModal(true);
          }}
        >
          All Model Grid Names
        </Button>
        <GridNameModal
          openGridTypeModal={openGridTypeModal}
          setOpenGridTypeModal={setOpenGridTypeModal}
          setGridTypeChanged={setGridTypeChanged}
          setUpdatedDefaultGridType={setUpdatedDefaultGridType}
          hasDefaultType={hasDefaultType}
          gridTypeChoice={gridTypeChoice}
          updatedDefaultGridType={updatedDefaultGridType}
          defaultGridType={defaultGridType}
          getGridTypesList={getGridTypesList}
          setStatusMessage={setStatusMessage}
          setOpenSnackbar={setOpenSnackbar}
          setStatusMessageType={setStatusMessageType}
          gridTypeChanged={gridTypeChanged}
          saveDefaultGridType={saveDefaultGridType}
          record={record}
          getLaborGridList={getLaborGridList}
          openSnackbar={openSnackbar}
          statusMessage={statusMessage}
          statusMessageType={statusMessageType}
        />
        {!record.isLaborGridSubmitted && (
          <Button
            disabled={gridTypeList.length === 0}
            size="small"
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            sx={{ ml: 1 }}
          >
            {submitLoading ? <CircularProgress size={22} /> : "Submit"}
          </Button>
        )}
      </Box>
      <NewGridOrMatrix
        cancelGridAddition={cancelGridAddition}
        getGridTypesList={getGridTypesList}
        defaultGridType={defaultGridType}
        hasDefaultType={hasDefaultType}
        gridTypeChoice={gridTypeChoice}
        addGrid={addGrid}
        handleRadioChange={handleRadioChange}
        incompleteError={incompleteError}
        invalidError={invalidError}
        invalidLength={invalidLength}
        getGridWrapper={getGridWrapper}
        handleSubmitLaborGrid={handleSubmitLaborGrid}
        setAddGrid={setAddGrid}
        component="laborGrid"
        installDateValue={installDateValue}
        setInstallDateValue={setInstallDateValue}
        updateDate={updateDate}
        setUpdateDate={setUpdateDate}
        setStatusMessage={setStatusMessage}
        setOpenSnackbar={setOpenSnackbar}
        setStatusMessageType={setStatusMessageType}
        setNoOfRows={setNoOfRows}
        noOfRows={noOfRows}
        addOrRemoveGridRow={addOrRemoveGridRow}
        submitLoader={submitLoader}
        setSubmitLoader={setSubmitLoader}
      />
      {/* } */}
      {listLoading ? (
        <Loading size="small" />
      ) : gridTypeList.length === 0 ? (
        <Box
          sx={{ display: "flex", margin: "30px 0px", justifyContent: "center" }}
        >
          <Typography
            sx={{
              fontSize: 16,
              color: "#003d6b",
            }}
          >
            No data to show
          </Typography>
        </Box>
      ) : (
        <Paper sx={{ margin: "16px" }}>
          {gridTypeList &&
            gridTypeList.map((gridTypeData: any, index: any) => (
              <Accordion
                key={index}
                expanded={index === expandedAccordionIndex}
                style={{ marginBottom: 16 }}
                onChange={(e: React.SyntheticEvent, expanded: boolean) => {
                  setAddGrid("");
                  setRowData(null);
                  setEnableSave(false);
                  setIncompleteError(false);
                  setInvalidLength(false);
                  setInvalidError(false);
                  if (expanded) {
                    setSaveClickedFlag(false);
                    setExpandedAccordionIndex(index);
                    setInstallDate(new Date(gridTypeData.storeInstallDate));
                    setGridType(gridTypeData.gridType);
                    getLaborGridList({
                      realm: record.realmName,
                      storeId: record.storeId,
                      callType: "Grid",
                      gridType: gridTypeData.gridType,
                      gridFor: gridTypeData.gridFor,
                      createdDate: gridTypeData.createdDate,
                    });
                  } else {
                    setExpandedAccordionIndex(null);
                  }
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{ display: "flex" }}
                  onClick={(event) => event.stopPropagation()}
                >
                  <Box sx={{ flex: 4 }}>
                    <Box sx={{ display: "flex", flexDirection: "column" }}>
                      <Box sx={{ display: "flex", flexDirection: "row" }}>
                        <Typography
                          sx={{
                            width: "6rem",
                            fontSize: 14,
                            color: "#003d6b",
                            fontWeight: "bold",
                          }}
                        >
                          Grid Period
                        </Typography>
                        <Typography
                          sx={{
                            fontSize: 14,
                            color: "#003d6b",
                            fontWeight: "bold",
                          }}
                        >
                          {gridTypeData.gridOrder == 1
                            ? ": Current"
                            : `: Prior ${gridTypeData.gridOrder - 1}`}
                        </Typography>
                      </Box>
                      <Box sx={{ display: "flex", flexDirection: "row" }}>
                        <Typography
                          sx={{
                            width: "6rem",
                            color: "#003d6b",
                            fontWeight: "bold",
                            fontSize: 14,
                          }}
                        >
                          Grid Name
                        </Typography>
                        <Tooltip title={gridTypeData.gridType}>
                          <Typography
                            sx={{
                              fontSize: 14,
                              color: "#003d6b",
                              fontWeight: "bold",
                            }}
                          >
                            : {formatCellValueDescription(gridTypeData)}
                          </Typography>
                        </Tooltip>
                        {/* )} */}
                      </Box>
                      <Box sx={{ display: "flex", flexDirection: "row" }}>
                        <Typography
                          sx={{
                            width: "6rem",
                            fontSize: 14,
                            color: "#003d6b",
                            fontWeight: "bold",
                          }}
                        >
                          Grid Type
                        </Typography>
                        <Typography
                          sx={{
                            fontSize: 14,
                            color: "#003d6b",
                            fontWeight: "bold",
                          }}
                        >
                          :{" "}
                          {gridTypeData.gridFor.charAt(0).toUpperCase() +
                            gridTypeData.gridFor.slice(1)}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ flex: 13 }}>
                    <Box sx={{ display: "flex", flexDirection: "column" }}>
                      <Box sx={{ display: "flex", flexDirection: "row" }}>
                        <Typography
                          sx={{
                            width: "13rem",
                            fontSize: 14,
                            color: "#003d6b",
                            fontWeight: "bold",
                          }}
                        >
                          Store Install Date
                        </Typography>
                        {enableSave && index === expandedAccordionIndex ? (
                          <Box
                            sx={{
                              fontSize: 14,
                              color: "#003d6b",
                              fontWeight: "bold",
                            }}
                            onClick={(event: any) => {
                              event.preventDefault();
                              event.stopPropagation();
                            }}
                          >
                            {": "}
                            <DatePicker
                              dateFormat="MM-dd-yyyy"
                              selected={installDate}
                              onChange={(date: any) => {
                                setInstallDate(date);
                              }}
                              customInput={<CustomDatePickerInput />}
                            />
                          </Box>
                        ) : (
                          <Typography
                            sx={{
                              fontSize: 14,
                              color: "#003d6b",
                              fontWeight: "bold",
                            }}
                          >
                            {": "}
                            {moment(gridTypeData.storeInstallDate).format(
                              "MM/DD/YYYY"
                            )}
                          </Typography>
                        )}
                      </Box>
                      <Box sx={{ display: "flex", flexDirection: "row" }}>
                        <Typography
                          sx={{
                            width: "13rem",
                            color: "#003d6b",
                            fontWeight: "bold",
                            fontSize: 14,
                          }}
                        >
                          FOPC Calculated Date From
                        </Typography>
                        <Typography
                          sx={{
                            fontSize: 14,
                            color: "#003d6b",
                            fontWeight: "bold",
                          }}
                        >
                          :{" "}
                          {moment(gridTypeData.createdDate).format(
                            "MM/DD/YYYY"
                          )}{" "}
                          {/* (This may be different from store install date if no
                          prior pricing provided). */}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                  <Box
                    sx={{
                      flex: 0.5,
                      display: "flex",
                      justifyContent: "right",
                      alignItems: "center",
                    }}
                  >
                    <DeleteButton
                      isSuccessful={isSuccessful}
                      openDelete={openDelete}
                      setOpenDelete={setOpenDelete}
                      handleDelete={handleSubmitLaborGrid}
                      setReassignGridType={setReassignGridType}
                      reassignGridType={reassignGridType}
                      newDefaultGridType={newDefaultGridType}
                      gridTypeData={{
                        ...gridTypeData,
                        storeId: record.storeId,
                        realmName: record.realmName,
                      }}
                      setNewDefaultGridType={setNewDefaultGridType}
                      getLaborGridList={getLaborGridList}
                      record={record}
                      gridTypeChoice={gridTypeChoice}
                      gridTypeList={gridTypeList}
                      setExpandedAccordionIndex={setExpandedAccordionIndex}
                    />
                  </Box>
                </AccordionSummary>
                {rowData && index === expandedAccordionIndex ? (
                  <AccordionDetails>
                    <Grid container display={"contents"}>
                      <>
                        <Box sx={{ flex: 1 }}>
                          <Box sx={{ display: "flex", flexDirection: "row" }}>
                            <Button
                              onClick={(params: any) => {
                                setSaveClickedFlag(true);
                                let rowIndex = 0;
                                gridRef.current.setFocusedCell(0, "col0");
                                gridRef.current.startEditingCell({
                                  rowIndex,
                                  colKey: "col0",
                                });
                                setTempData(rowData);
                              }}
                              disabled={enableSave}
                              color="primary"
                              variant="contained"
                              size="small"
                              startIcon={<EditIcon />}
                              sx={{ m: "15px 0", width: "7rem" }}
                            >
                              Edit Grid
                            </Button>
                            <Button
                              disabled={enableSave ? false : true}
                              onClick={() => {
                                const modifiedGridObject = {
                                  ...gridTypeData,
                                  storeInstallDate: installDate,
                                };
                                !gridTypeError &&
                                  handleSubmitLaborGrid(
                                    {
                                      ...modifiedGridObject,
                                      storeId: record.storeId,
                                      realmName: record.realmName,
                                      edit: true,
                                    },
                                    "update"
                                  );
                              }}
                              color="primary"
                              variant="contained"
                              size="small"
                              startIcon={<SaveIcon />}
                              sx={{ m: "15px 5px", width: "7rem" }}
                            >
                              Update
                            </Button>
                            <Button
                              disabled={enableSave ? false : true}
                              onClick={() => {
                                setEnableSave(false);

                                setExpandedAccordionIndex(null);
                              }}
                              color="primary"
                              variant="contained"
                              size="small"
                              startIcon={<CancelIcon />}
                              sx={{ m: "15px 5px", width: "7rem" }}
                            >
                              CANCEL
                            </Button>
                          </Box>
                        </Box>
                        {/* )} */}
                        <Grid item xs={8}>
                          <Typography
                            fontSize={"small"}
                            alignContent={"left"}
                            marginBottom={"5px"}
                            color={"red"}
                          >
                            {incompleteError &&
                              "*Please completely fill in the grid. "}
                            {invalidError && "*Enter numeric values only."}{" "}
                            {invalidLength &&
                              "* Value should contain only five digits with up to two decimal places, e.g. 12345.67"}
                          </Typography>

                          {getGridWrapper("view", rowData)}
                          {enableSave && (
                            <Box display="flex" justifyContent="flex-end">
                              <Button
                                sx={{
                                  textTransform: "none",
                                  fontSize: "12px",
                                }}
                                onClick={() => {
                                  setNoOfRows((prev: number) => {
                                    const updatedRows = prev + 1;
                                    addOrRemoveGridRow("add", updatedRows);
                                    return updatedRows;
                                  });
                                }}
                              >
                                + Add new row
                              </Button>
                              <Button
                                sx={{
                                  textTransform: "none",
                                  fontSize: "12px",
                                }}
                                disabled={noOfRows <= 16}
                                onClick={() => {
                                  setNoOfRows((prev: number) => {
                                    const updatedRows = prev - 1;
                                    addOrRemoveGridRow("remove", updatedRows);
                                    return updatedRows;
                                  });
                                }}
                              >
                                - Remove last row
                              </Button>
                            </Box>
                          )}
                        </Grid>
                      </>
                    </Grid>
                  </AccordionDetails>
                ) : !rowData && index === expandedAccordionIndex ? (
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                    }}
                  >
                    <CustomLinearProgress />
                  </Box>
                ) : (
                  <></>
                )}
              </Accordion>
            ))}
        </Paper>
      )}
      {submitLoader && (
        <Backdrop
          open={submitLoader}
          style={{
            zIndex: 9999,
            color: "#fff",
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
          }}
          >
          <CircularProgress color="inherit" />
        </Backdrop>
      )}
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={statusMessageType}
      />
      {snackbarMessages?.length > 0 && (
        <MultipleSnackbars
          messages={snackbarMessages}
          setSnackbarMessages={setSnackbarMessages}
        />
      )}
    </Box>
  );
};

export default LaborGrid;
