import * as React from "react";
import { useEffect } from "react";
import {
  Form,
  SaveButton,
  Toolbar,
  useGetList,
  useTranslate,
} from "react-admin";
import { Card, CardContent, Box, Button } from "@mui/material";
import { StoreInputs } from "./StoreInputs";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { Constants } from "../../utils/constants";
import useOnboarding from "../../CustomHooks/useOnboarding";
import useStoreCreate from "../../CustomHooks/useStoreCreate";
import Loading from "../../components/Loading";
import CancelIcon from "@mui/icons-material/Cancel";
import { PageRoutes } from "../../utils/pageRoutes";
import SnackBarMessage from "../../components/SnackBarMessage";
import dayjs from "dayjs";

export const StoreCreateOrEdit = () => {
  const { data, isLoading } = useGetList("dmsMasters");
  const location = useLocation();
  const { id } = useParams();
  const translate = useTranslate();
  const navigate = useNavigate();
  const { getStoreQuery, storeRecord } = useOnboarding({ id: id });
  const { record, action } = location.state;
  const {
    handleSubmit,
    isMakesLoading,
    manufacturers,
    getManufacturers,
    openSnackbar,
    setOpenSnackbar,
    statusMessage,
    estLaunchDate,
    setEstLaunchDate,
  } = useStoreCreate({action: action, record: record});

  useEffect(() => {
    if (action === Constants.actions.edit) {
      getStoreQuery();
    }
    getManufacturers();
  }, []);

  useEffect(() => {
    storeRecord?.storeLaunchedDate && setEstLaunchDate(dayjs(storeRecord?.storeLaunchedDate));
  }, [storeRecord]);

  if (isLoading || isMakesLoading) return <Loading />;
  return (
    <Box mt={2} display="flex" width="inherit">
      <Box
        sx={{
          flex: 1,
          width: "40%",
          display: "flex",
          justifyContent: "space-around",
        }}
      >
        <Form
          onSubmit={handleSubmit}
          defaultValues={
            action === Constants.actions.edit
              ? {
                  storeName: storeRecord?.storeName,
                  storeDesc: storeRecord?.storeDesc,
                  dms: storeRecord?.dms,
                  manufacturer: storeRecord?.manufacturer,
                  sortOrder: storeRecord?.sortOrder,
                  dealerAddress: storeRecord?.dealerAddress,
                  website: storeRecord?.website,
                  companyNumber: storeRecord?.companyNumber,
                  dealerId: storeRecord?.dealerId ? storeRecord?.dealerId : "",
                  enterpriseCode: storeRecord?.enterpriseCode,
                  serverName: storeRecord?.serverName,
                  stateCode: storeRecord?.stateCode,
                  inSfStoreId: storeRecord?.sfStoreId,
                }
              : {}
          }
        >
          <Card>
            <CardContent
              style={{
                paddingBottom: 0,
              }}
            >
              <Box display="flex">
                <StoreInputs
                  storeRecord={storeRecord}
                  action={action}
                  tenantId={record.id}
                  sortOrder={Number(record.count) + 1}
                  data={data}
                  manufacturers={manufacturers}
                  estLaunchDate={estLaunchDate}
                  setEstLaunchDate={setEstLaunchDate}
                />
              </Box>
            </CardContent>
            <Toolbar
              sx={{ backgroundColor: "white", justifyContent: "flex-end" }}
            >
              <SaveButton alwaysEnable />
              <Button
                color="primary"
                variant="contained"
                startIcon={<CancelIcon />}
                sx={{ m: 2 }}
                onClick={() =>
                  action === Constants.actions.edit
                    ? navigate(PageRoutes.getStoreDetailsRoute(record.id))
                    : navigate(PageRoutes.tenantShowPageRoute(record.id))
                }
              >
                {translate("BUTTONS.CANCEL")}
              </Button>
            </Toolbar>
          </Card>
        </Form>
        <SnackBarMessage
          onClose={() => setOpenSnackbar(false)}
          open={openSnackbar}
          message={statusMessage}
        />
      </Box>
    </Box>
  );
};
