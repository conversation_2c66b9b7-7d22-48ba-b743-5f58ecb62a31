import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useMemo } from "react";
import { Constants } from "../../utils/constants";
import {
  faBan,
  faPencil,
  faFloppyDisk,
} from "@fortawesome/free-solid-svg-icons";

const TechGridDefs = (storeLaunched: string) => {
  const buttonStyle = {
    borderRadius: "3px",
    height: "22px",
    border: "0",
    fontSize: "12px",
    cursor: "pointer",
    lineHeight: "13px",
  };
  const objCategoryMappings = [
    {
      1: "Active",
      0: "Inactive",
    },
  ];
  function extractValues(mappings: any) {
    return Object.keys(mappings[0]);
  }
  const columnDefs: any = [
    {
      headerName: " Id",
      chartDataType: "series",
      suppressMovable: true,
      width: 150,
      field: "id",
      hide: true,
      editable: false,
    },
    {
      headerName: "Tech Number",
      chartDataType: "series",
      width: 150,
      field: "lbrtechno",
      suppressMovable: true,
      suppressMenu: true,
      unSortIcon: true,
      comparator: function (valueA: any, valueB: any) {
        return valueA - valueB;
      },
      cellStyle() {
        return { textAlign: "center", border: " 0px white" };
      },
      editable: function (params: any) {
        if (params.data.id == "") {
          return true;
        } else {
          return false;
        }
      },
    },
    {
      headerName: "Name",
      chartDataType: "series",
      width: 150,
      field: "name",
      editable: false,
      suppressMovable: true,
      suppressMenu: true,
      unSortIcon: true,
      cellStyle() {
        return { border: " 0px white", marginLeft: "6px" };
      },
    },
    {
      headerName: "Nick Name",
      chartDataType: "series",
      width: 150,
      field: "nickname",
      cellEditor: 'agTextCellEditor',
      cellEditorParams: {
          maxLength: 20
      },
      suppressMovable: true,
      suppressMenu: true,
      singleClickEdit: true,
      unSortIcon: true,
      editable: true,
      cellStyle() {
        return { border: " 0px white", marginLeft: "6px" };
      },
    },

    {
      headerName: "Active / Inactive",
      chartDataType: "series",
      width: 150,
      suppressMovable: true,
      field: "active",
      suppressMenu: true,
      unSortIcon: true,
      editable: true,
      cellEditor: "agSelectCellEditor",
      hide: false,
      valueFormatter: function (params: any) {
        return params.value == 1
          ? "Active"
          : params.value == null
          ? "----"
          : "Inactive";
      },
      cellEditorParams: {
        values: extractValues(objCategoryMappings),
      },
      filterParams: {
        valueFormatter: function (params: any) {
          return params.value == 1
            ? "Active"
            : params.value == null
            ? null
            : "Inactive";
        },
      },
    },
    {
      headerName: "Action",
      minWidth: 150,
      cellStyle: { border: "none" },
      suppressNavigable: true,
      sortable: false,
      filter: false,
      unSortIcon: false,
      cellRenderer: function (params: any) {
        const editingCells = params?.api?.getEditingCells();
        const isCurrentRowEditing = editingCells?.some((cell: any) => {
          return cell?.rowIndex === params?.node?.rowIndex;
        });
        if (isCurrentRowEditing) {
          return (
            <div style={{ marginTop: "0px" }}>
              <button style={buttonStyle}
                className="action-button update"
                data-action={Constants.actions.update}
              >
                <FontAwesomeIcon icon={faFloppyDisk} />
              </button>
              <button style={buttonStyle}
                className="action-button cancel"
                data-action={Constants.actions.cancel}
              >
                <FontAwesomeIcon icon={faBan} />
              </button>
            </div>
          );
        } else {
          return (
            <div style={{ marginTop: "0px" }}>
               <button style={buttonStyle}
                className="action-button"
                // hide for store launched status
                // className={`action-button ${
                //   storeLaunched === "Completed" ? "action-button-disabled" : ""
                // }`}
                data-action={Constants.actions.edit}
                // disabled={storeLaunched === "Completed"}
              >

                <FontAwesomeIcon icon={faPencil} />
              </button>
            </div>
          );
        }
      },
      editable: false,
      colId: "action",
    },
  ];
  const defaultColDef = useMemo(() => {
    return {
      flex: 1,
      minWidth: 150,
      resizable: true,
      floatingFilter: true,
      sortable: true,
      filter: true,
      suppressMenu: true,
      unSortIcon: true,
      filterParams: {
        applyMiniFilterWhileTyping: true,
      },
      enableValue: true,
      editable: true,
    };
  }, []);

  return {
    columnDefs,
    defaultColDef,
  };
};

export default TechGridDefs;
