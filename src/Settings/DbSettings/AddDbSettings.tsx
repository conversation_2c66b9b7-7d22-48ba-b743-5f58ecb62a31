import React, { useState } from "react";
import { useForm } from "react-hook-form";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Container,
  Typography,
  Box,
  Grid,
  MenuItem,
  CircularProgress,
  Alert,
} from "@mui/material";
import SettingsQueries from "../../service/Mutations/Settings";
import { useNavigate } from "react-router-dom";
import SnackBarMessage from "../../components/SnackBarMessage";
import { NotificationType } from "../../types";

const MuiForm = () => {
  const {
    register,
    handleSubmit,
    setValue,
    clearErrors,
    formState: { errors },
  } = useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState<boolean>(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState("");
  const [statusMessageType, setStatusMessageType] =
    useState<NotificationType>("error");

  const { UpsertDbSettings } = SettingsQueries;

  const onSubmit = async (data: any) => {
    setLoading(true);
    data.dbPort = parseInt(data.dbPort);
    data.isFull = false;

    //await UpsertDbSettings(data).then((res: any) => {

    data.dbPort = parseInt(data.dbPort);
    console.log("Saved data:", data);
    UpsertDbSettings(data)
      .then((res: any) => {
        setOpenSnackbar(true);
        setStatusMessage(res);
        setStatusMessageType("success");
        setLoading(false);
        setTimeout(() => {
          navigate("/Settings/DbSettings");
        }, 2000);
      })
      .catch((err) => {
        setOpenSnackbar(true);
        setStatusMessage("Something went wrong");
        setStatusMessageType("error");
        setLoading(false);
        console.log("err", err);
      });
    // if (res.status === "failed") {
    //   setOpenSnackbar(true);
    //   setStatusMessage(res.message);
    //   setStatusMessageType("error");
    //   setLoading(false);
    // } else {
    //   UpsertDbSettings(data).then((response: any) => {});
    //   setLoading(false);
    //   navigate("/Settings/DbSettings", {
    //     state: { status: "User creation is in progress" },
    //   });
    // }
    //});
  };

  return (
    <Container
      sx={{
        width: "100%",
        height: "70vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={statusMessageType}
      />
      <Box
        sx={{
          border: "1px solid #d3d3d3",
          padding: 3,
          borderRadius: 1,
          boxShadow: 1,
          width: "50%",
          height: "fit-content",
          position: "relative",
          opacity: loading ? 0.7 : 1,
        }}>
        {loading && (
          <CircularProgress
            size={24}
            sx={{
              position: "absolute",
              top: "50%",
              left: "50%",
              marginTop: "-12px",
              marginLeft: "-12px",
            }}
          />
        )}
        <Typography
          variant="h5"
          gutterBottom
          fontWeight={"bold"}
          textAlign={"center"}>
          Add Db Settings
        </Typography>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={2} alignItems="center" marginBottom={2}>
            <Grid item xs={4}>
              <Typography fontSize={"14px"}>IP Address</Typography>
            </Grid>
            <Grid item xs={1}>
              <Typography textAlign={"end"} fontSize={"14px"}>
                :
              </Typography>
            </Grid>
            <Grid item xs={7}>
              <TextField
                fullWidth
                variant="outlined"
                {...register("ipAddress", {
                  required: "IP Address is required",
                  pattern: {
                    value:
                      /^(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}$/,
                    message: "Invalid IP address",
                  },
                })}
                error={!!errors.ipAddress}
                helperText={
                  errors.ipAddress?.message
                    ? String(errors.ipAddress.message)
                    : ""
                }
                size="small"
                sx={{
                  "& .MuiInputBase-root": { height: 30 },
                  "& .MuiInputBase-input": { fontSize: "14px" },
                }}
                onInput={(e: any) => {
                  let value = e.target.value;

                  // Allow only numbers and dots
                  value = value.replace(/[^0-9.]/g, "");

                  // Prevent more than 3 dots
                  const dotCount = (value.match(/\./g) || []).length;
                  if (dotCount > 3) {
                    value = value.substring(0, value.length - 1);
                  }

                  // Split by dots and check each block
                  let parts = value.split(".");
                  parts = parts.map((part: string) => {
                    if (part === "") return part; // Allow empty while typing
                    let num = parseInt(part, 10);
                    if (isNaN(num)) return "";
                    if (num > 255) return "255";
                    return String(num);
                  });

                  // Rejoin and set the value
                  e.target.value = parts.join(".");
                }}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2} alignItems="center" marginBottom={2}>
            <Grid item xs={4}>
              <Typography fontSize={"14px"}>Db Name</Typography>
            </Grid>
            <Grid item xs={1}>
              <Typography textAlign={"end"} fontSize={"14px"}>
                :
              </Typography>
            </Grid>
            <Grid item xs={7}>
              <TextField
                fullWidth
                variant="outlined"
                {...register("dbName", {
                  required: "Db name is required",
                  pattern: {
                    value: /^[a-zA-Z0-9]*$/,
                    message: "Only letters and numbers are allowed",
                  },
                })}
                error={!!errors.dbName}
                helperText={
                  errors.dbName?.message ? String(errors.dbName.message) : ""
                }
                onInput={(e: any) => {
                  e.target.value = e.target.value.replace(/[^a-zA-Z0-9]/g, "");
                }}
                sx={{
                  "& .MuiInputBase-root": { height: 30 },
                  "& .MuiInputBase-input": { fontSize: "14px" },
                }}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2} alignItems="center" marginBottom={2}>
            <Grid item xs={4}>
              <Typography fontSize={"14px"}>User Name</Typography>
            </Grid>
            <Grid item xs={1}>
              <Typography textAlign={"end"} fontSize={"14px"}>
                :
              </Typography>
            </Grid>
            <Grid item xs={7}>
              <TextField
                fullWidth
                variant="outlined"
                {...register("dbUser", {
                  required: "User name is required",
                  pattern: {
                    value: /^[a-zA-Z0-9]*$/,
                    message: "Only letters and numbers are allowed",
                  },
                })}
                error={!!errors.dbUser}
                helperText={
                  errors.dbUser?.message ? String(errors.dbUser.message) : ""
                }
                onInput={(e: any) => {
                  e.target.value = e.target.value.replace(/[^a-zA-Z0-9]/g, "");
                }}
                sx={{
                  "& .MuiInputBase-root": { height: 30 },
                  "& .MuiInputBase-input": { fontSize: "14px" },
                }}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2} alignItems="center" marginBottom={2}>
            <Grid item xs={4}>
              <Typography fontSize={"14px"}>Password</Typography>
            </Grid>
            <Grid item xs={1}>
              <Typography textAlign={"end"} fontSize={"14px"}>
                :
              </Typography>
            </Grid>
            <Grid item xs={7}>
              <TextField
                fullWidth
                variant="outlined"
                {...register("dbPassword", {
                  required: "Password is required",
                  validate: (value) =>
                    !/\s/.test(value) || "Spaces are not allowed in password",
                })}
                error={!!errors.dbPassword}
                helperText={
                  errors.dbPassword?.message
                    ? String(errors.dbPassword.message)
                    : ""
                }
                onKeyDown={(e) => {
                  if (e.key === " ") {
                    e.preventDefault();
                  }
                }}
                onPaste={(e) => {
                  const paste = e.clipboardData.getData("text");
                  if (/\s/.test(paste)) {
                    e.preventDefault();
                  }
                }}
                sx={{
                  "& .MuiInputBase-root": { height: 30 },
                  "& .MuiInputBase-input": { fontSize: "14px" },
                }}
              />
            </Grid>
          </Grid>

          <Grid container spacing={2} alignItems="center" marginBottom={2}>
            <Grid item xs={4}>
              <Typography fontSize={"14px"}>Port</Typography>
            </Grid>
            <Grid item xs={1}>
              <Typography textAlign={"end"} fontSize={"14px"}>
                :
              </Typography>
            </Grid>
            <Grid item xs={7}>
              <TextField
                fullWidth
                variant="outlined"
                {...register("dbPort", {
                  required: "Port is required",
                  pattern: {
                    value:
                      /^([1-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$/,
                    message: "Port must be a number between 1 and 65535",
                  },
                })}
                error={!!errors.dbPort}
                helperText={
                  errors.dbPort?.message ? String(errors.dbPort.message) : ""
                }
                onKeyDown={(e) => {
                  const allowedKeys = [
                    "Backspace",
                    "ArrowLeft",
                    "ArrowRight",
                    "Delete",
                    "Tab",
                  ];
                  if (!/^[0-9]$/.test(e.key) && !allowedKeys.includes(e.key)) {
                    e.preventDefault(); // block special chars, space, letters
                  }
                }}
                onInput={(e: any) => {
                  const value = e.target.value;
                  if (value === "") return;

                  const num = parseInt(value, 10);

                  if (num < 1) {
                    e.target.value = "1";
                  } else if (num > 65535) {
                    e.target.value = "65535";
                  }
                }}
                sx={{
                  "& .MuiInputBase-root": { height: 30 },
                  "& .MuiInputBase-input": { fontSize: "14px" },
                }}
              />
            </Grid>
          </Grid>

          <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
            <Button
              variant="outlined"
              color="secondary"
              onClick={() => navigate("/settings/dbSettings")}>
              Cancel
            </Button>
            <Button type="submit" variant="contained" color="primary">
              Submit
            </Button>
          </Box>
        </form>
      </Box>
    </Container>
  );
};

export default MuiForm;
