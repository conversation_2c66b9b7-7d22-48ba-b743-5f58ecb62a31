import { <PERSON>, Button } from "@mui/material";
import React, { useState, useRef } from "react";
import { AgGridReact } from "ag-grid-react";
import { Constants } from "../../utils/constants";
import DbSettingsGridDefs from "./DbSettingsGridDefs";
import SnackBarMessage from "../../components/SnackBarMessage";

import { NotificationType } from "../../types";
import { useNavigate } from "react-router-dom";

const DbSettings = () => {
  const navigate = useNavigate();

  const [openSnackbar, setOpenSnackbar] = useState<boolean>(false);
  const [statusMessage, setStatusMessage] = useState<string>("");
  const [statusMessageType, setStatusMessageType] =
    useState<NotificationType>();

  // Grid API ref
  const gridApiRef = useRef<any>(null);

  const handleSaveSuccess = (
    type: NotificationType,
    message: string,
    status: boolean
  ) => {
    setStatusMessageType(type);
    setStatusMessage(message);
    setOpenSnackbar(status);
  };

  const onFilterChanged = () => {
    gridApiRef.current?.redrawRows();
  };

  const onSortChanged = () => {
    gridApiRef.current?.redrawRows();
  };

  const {
    columnDefs,
    defaultColDef,
    onGridReady: defsOnGridReady,
    allDataList,
  } = DbSettingsGridDefs({ onSaveSuccess: handleSaveSuccess });

  const onGridReady = (params: any) => {
    gridApiRef.current = params.api;
    defsOnGridReady(params); // Call your defs logic too
  };

  return (
    <>
      <Box sx={{ paddingX: "10px", width: "100%", marginTop: "20px" }}>
        <Button variant="outlined" onClick={() => navigate("create")}>
          Add Db Settings
        </Button>
      </Box>
      <Box sx={{ paddingX: "10px", width: "100%", marginTop: "20px" }}>
        <div
          className={Constants.ag_grid_theme}
          style={{ height: "90vh", width: "100%" }}>
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            rowData={allDataList}
            defaultColDef={defaultColDef}
            rowSelection="single"
            onGridReady={onGridReady}
            singleClickEdit={false}
            suppressClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            suppressCellSelection={true}
            suppressRowClickSelection={true}
            stopEditingWhenCellsLoseFocus={false}
            onFilterChanged={onFilterChanged}
            onSortChanged={onSortChanged}
          />
        </div>
      </Box>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={statusMessageType}
      />
    </>
  );
};

export default DbSettings;
