import { Box } from "@mui/material";
import React from "react";
import { AgGridReact } from "ag-grid-react";
import { Constants } from "../../utils/constants";
import SchemaConfigGridDefs from "./SchemaConfigGridDefs";

const SchemaConfig = () => {
  const {
    columnDefs,
    defaultColDef,
    onGridReady,
    allDataList,
  } = SchemaConfigGridDefs();

  return (
    <Box sx={{ paddingX: "10px", width: "100%" ,marginTop: "20px" }}>
      <div
        className={Constants.ag_grid_theme}
        style={{ height: "90vh", width: "100%" }}
      >
        <AgGridReact
          columnDefs={columnDefs}
          editType="fullRow"
          rowData={allDataList}
          defaultColDef={defaultColDef}
          rowSelection="single"
          onGridReady={(params: any) => onGridReady(params)}
          singleClickEdit={true}
          suppressColumnVirtualisation={true}
          suppressChangeDetection={true}
          stopEditingWhenCellsLoseFocus={true}
        />
      </div>
    </Box>
  );
};

export default SchemaConfig;