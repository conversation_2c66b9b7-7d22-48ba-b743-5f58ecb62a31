import React from "react";
import SettingsQueries from "../../service/Mutations/Settings";
import { ColDef } from "ag-grid-community";

const SchemaConfigGridDefs = () => {
  const { GetSchemaConfig } = SettingsQueries;
  const [allDataList, setAllDataList] = React.useState<any>([]);

  const getSchemaList = () => {
    GetSchemaConfig().then((res: any) => {
      setAllDataList(res);
    });
  };

  const onGridReady = (params: any) => {
    params?.api?.showLoadingOverlay();
    getSchemaList();
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "Realm",
      field: "realmName",
      flex: 3,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Host Name",
      field: "hostname",
      cellStyle: { textAlign: "left" },
      flex: 2,
    },
    {
      headerName: "Port",
      field: "port",
      flex: 1,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "DB Server",
      field: "dbServer",
      flex: 1,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "DB Name",
      field: "dbName",
      flex: 2,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Auth User Name",
      field: "authUsername",
      flex: 2,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Auth Password",
      field: "authPassword",
      flex: 3,
      cellStyle: { textAlign: "left" },
    },
  ];
  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      flex: 3,
      filterParams: {
        applyMiniFilterWhileTyping: true,
      },
    };
  }, []);
  return {
    columnDefs,
    defaultColDef,
    onGridReady,
    allDataList,
  };
};

export default SchemaConfigGridDefs;
