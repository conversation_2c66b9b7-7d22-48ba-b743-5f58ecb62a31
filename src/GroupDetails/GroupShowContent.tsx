import { useEffect, useState } from "react";
import { Box } from "@mui/material";
import {
  Datagrid,
  ListContextProvider,
  TextField,
  useList,
  useTranslate,
} from "react-admin";
import { DeleteGroupStore } from "./DeleteGroupStores";
import StoreQueries from "../service/DataFetchQueries/storeQueries";

export const GroupShowContent = (props: any) => {
  const { GetStoreNamesQuery } = StoreQueries;
  const { group, getGroupingDetalis } = props;
  const translate = useTranslate();
  const [groupStores, setGroupStores] = useState();

  useEffect(() => {
    GetStoreNamesQuery(group.tenantId).then((data) => {
      const tempGroupStores = data.filter((store: any) =>
        group.stores.includes(store.storeId)
      );
      setGroupStores(tempGroupStores);
    });
  }, [group]);
  const listContext = useList({
    data: groupStores,
  });
  if (!groupStores) return null;
  return (
    <Box display="flex" sx={{ margin: "0 20px", padding: "0 30px" }}>
      <Box flex="1">
        <ListContextProvider value={listContext}>
          <Datagrid
            bulkActionButtons={false}
            sx={{
              "& .RaDatagrid-headerCell": {
                fontWeight: "bold",
              },
            }}
          >
            <TextField label={translate("LABELS.STORES")} source="storeName" />
            <DeleteGroupStore group={group} getGroupingDetalis={getGroupingDetalis}/>
          </Datagrid>
        </ListContextProvider>
      </Box>
    </Box>
  );
};
