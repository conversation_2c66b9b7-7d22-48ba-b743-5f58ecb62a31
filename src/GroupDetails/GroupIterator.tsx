import React from "react";
import {
  Box,
  Button,
  Dialog,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import {
  Confirm,
  RecordContextProvider,
  useNotify,
  useRecordContext,
  useTranslate,
} from "react-admin";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import BorderColorIcon from "@mui/icons-material/BorderColor";
import GroupForm from "./GroupForm";
import { GroupShowContent } from "./GroupShowContent";
import { EditGroup } from "../types";
import { Delete } from "@mui/icons-material";
import { MutateGroupingMaster } from "../service/mutations";
import { Constants } from "../utils/constants";
import AddBusinessIcon from "@mui/icons-material/AddBusiness";
import SnackBarMessage from "../components/SnackBarMessage";

export const GroupIterator = (props: any) => {
  const { getGroupingDetalis, groupData } = props;
  const record = useRecordContext();
  const [openGroup, setOpenGroup] = useState(false);
  const [openEditGroup, setOpenEditGroup] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState<number>();
  const [editingGroup, setEditingGroup] = useState<EditGroup>();
  const [deleteDialog, setDeleteDialog] = useState<boolean>(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState<any>("");
  const translate = useTranslate();

  useEffect(() => {
    getGroupingDetalis();
  }, []);

  let tenantGroups: any = [];
  groupData &&
    groupData.map((item: any) =>
      !tenantGroups.some((obj: any) => obj.groupId === item.groupId)
        ? tenantGroups.push({
            groupId: item.groupId,
            name: item.groupName,
            desc: item.groupDesc,
            stores: [item.storeId],
            tenantId: record.tenantId,
          })
        : tenantGroups
            .find((obj: any) => obj.groupId === item.groupId)
            .stores.push(item.storeId)
    );

  const handleClick = (groupId: number) => {
    setSelectedGroupId(groupId === selectedGroupId ? 0 : groupId);
    setOpenGroup(!openGroup);
  };

  const handleEditButton = (group: EditGroup) => {
    setEditingGroup(group);
    setOpenEditGroup(true);
  };

  const handleDelete = (group: EditGroup) => {
    setEditingGroup(group);
    setDeleteDialog(true);
  };

  const handleConfirmDelete = async (group: EditGroup) => {
    await MutateGroupingMaster(
      Constants.actions.delete,
      group.desc,
      group.groupId,
      [],
      group.name,
      group.tenantId
    )
      .then((result: any) => {
        if (result.string === Constants.success) {
          setOpenSnackbar(true);
          setStatusMessage(
            translate("SUCCESS_MESSAGES.DELETE_MESSAGE", {
              entityName: Constants.groupingDetails,
            })
          );
          getGroupingDetalis();
        }
      })
      .catch((error: any) => {
        // ToDo: Need show Error Message
      });
    setDeleteDialog(false);
  };
  if (!groupData) return null;
  return (
    <Box>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
      />
      <Box
        display="flex"
        justifyContent={"space-between"}
        alignItems={"center"}
      >
        <Typography variant="h6" marginLeft={"10px"} fontWeight={600}>
          {translate("LABELS.STORE_GROUPS")}
        </Typography>
        {!record.cancelled && (
          <CreateStoreGroupButton getGroupingDetalis={getGroupingDetalis} />
        )}
      </Box>

      {tenantGroups && tenantGroups.length !== 0 ? (
        <List>
          {tenantGroups.map((group: any) => (
            <RecordContextProvider key={group.groupId} value={group}>
              <Box display={"flex"}>
                <ListItem key={group.groupId}>
                  <ListItemAvatar
                    onClick={() => handleClick(group.groupId)}
                    className="expandCollapse"
                  >
                    {group.groupId === selectedGroupId && openGroup ? (
                      <ExpandLess />
                    ) : (
                      <ExpandMore />
                    )}
                  </ListItemAvatar>
                  <ListItemText primary={group.name} secondary={group.desc} />
                </ListItem>
                <Box display={"flex"}>
                  <IconButton
                    onClick={() => handleEditButton(group)}
                    sx={{ margin: "30px 0" }}
                    title={translate("BUTTONS.EDIT_GROUP")}
                  >
                    <BorderColorIcon />
                  </IconButton>
                  <IconButton
                    sx={{ margin: "30px 0" }}
                    onClick={() => handleDelete(group)}
                    title={translate("BUTTONS.DELETE_GROUP")}
                  >
                    <Delete />
                  </IconButton>
                </Box>
              </Box>

              {group.groupId === selectedGroupId && (
                <GroupShowContent
                  group={group}
                  getGroupingDetalis={getGroupingDetalis}
                />
              )}
            </RecordContextProvider>
          ))}
        </List>
      ) : (
        <Box sx={{ display: "flex", justifyContent: "center", margin: "2rem" }}>
          <Typography color={"grey"}>
            {translate("MESSAGES.NO_GROUPS")}
          </Typography>
        </Box>
      )}
      {editingGroup && (
        <Confirm
          isOpen={deleteDialog}
          // loading={isLoading}
          title={`Delete ${editingGroup.name}`}
          content={translate("DIALOG_BOX.GROUP_DELETE")}
          onConfirm={() => handleConfirmDelete(editingGroup)}
          onClose={() => {
            setDeleteDialog(false);
          }}
        />
      )}
      <Dialog open={openEditGroup}>
        <GroupForm
          setOpen={setOpenEditGroup}
          action={Constants.actions.update}
          editGroup={editingGroup}
          getGroupingDetalis={getGroupingDetalis}
        />
      </Dialog>
    </Box>
  );
};

const CreateStoreGroupButton = (props: any) => {
  const { getGroupingDetalis } = props;
  const [open, setOpen] = useState<boolean>(false);
  const translate = useTranslate();
  return (
    <>
      <Button
        onClick={() => setOpen(true)}
        color="primary"
        variant="contained"
        size="small"
        startIcon={<AddBusinessIcon />}
        sx={{ m: 2, width: "10rem" }}
      >
        {translate("BUTTONS.CREATE_GROUP")}
      </Button>
      <Dialog open={open}>
        <GroupForm
          setOpen={setOpen}
          action={Constants.actions.insert}
          getGroupingDetalis={getGroupingDetalis}
        />
      </Dialog>
    </>
  );
};
