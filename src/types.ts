import { RaRecord, Identifier } from "react-admin";
import dayjs from "dayjs";

export interface Tenant extends RaRecord {
  tenantName?: string;
  dms?: string;
  count?: string | number;
  tenantId?: string;
  dmsList?: string[];
  id: Identifier;
  subDomain?: string;
  isConfigured?: boolean;
  tenantImg?: string;
  realmName?: string;
  infrastructureConfiguration?: string;
  cancelled?: boolean;
  onboarding?: boolean;
  nsQaValidation?: boolean;
  review?: boolean;
  readyToLaunch?: boolean;
  launched?: boolean;
  agreementDate?: string | Date;
  deletedStoreCount?: string | number;
  activeStoreCount?: string | number;
  deletedstoreortenant?: boolean;
  isTestTenant?: boolean;
  tenantDeletedDate?: string | Date;
  __typename?: string;
}
export interface TenantRecord extends RaRecord {
  dms: string;
  dmsImg: string;
  id: number;
  tenantId: string;
  tenantName: string;
  __typename: string;
}

export interface Store extends RaRecord {
  id: number;
  stateCode: string;
  tenantId: string;
  storeId: string;
  storeName: string;
  storeDesc: string;
  manufacturer: string;
  website: string;
  dealerId: string;
  dealerAddress: string;
  dms: string;
  groupName: string;
  dmsType: string;
  enterpriseCode: string;
  companyNumber: string;
  serverName: string;
  secondProjectId: string;
  projectId: string;
  curlRequest: string;
  __typename: string;
}

export interface EditGroup extends RaRecord {
  desc: string;
  groupId: number;
  name: string;
  stores: string[];
  tenantId: string;
}

export interface GroupStore extends RaRecord {
  __typename: string;
  groupDesc: string;
  groupId: number;
  groupName: string;
  id: number;
  storeId: string;
  storeName: string;
  tenantId: string;
}

export interface TabPanelProps {
  children?: React.ReactNode;
  index: any;
  value: any;
}

export interface Tag extends RaRecord {
  name: string;
  color: string;
}

export interface DmsListRecord extends RaRecord {
  id: number;
  createdTime: string;
  dmsImg: string;
  dms: string;
  lastUpdatedBy: string;
  lastUpdatedOn: string;
  __typename: string;
}

export interface IFetchConfigReturn {
  status: number;
  data?: any;
  error?: any;
}
export interface SelectInputChoices {
  id: string;
  name: string;
  isDefaultGridType?: boolean;
}

export interface IEmailQueryInput {
  pDms: string;
  pNewStore: number;
  pStore: string;
  pStoreId: string;
  pTenant: string;
  pTenantId: string;
}

export interface CommonColumnDefinition {
  cprocount: string;
  totallabordollars: string;
  totalhours: string;
  retailqualhours: string;
  retailqualsale: string;
  elr: string;
  lbrlabortype: string;
}
export interface CategorizedColumnDefinition extends CommonColumnDefinition {
  opcategory: string;
  lbropcode: string;
  lbropcodedesc: string;
  additionalDescription: string;
  department: string;
}
export interface UnCategorizedColumnDefinition extends CommonColumnDefinition {
  lbropcode: string;
  lbropcodedesc: string;
  additionalDescription: string;
}
export interface GridTypeMaster extends RaRecord {
  value: string;
  isDefaultGridType: boolean;
}
export interface Subscription {
  tenantId: string;
  subscriptionId: string;
  invoiceDate: string;
  subscriptionStatus: string;
  dmsOneTimeFee: number;
  dmsMonthlyRecurringFee: number;
  fopcMonthlyRecurringFee: number;
  cancelledDate: string;
  cancelledBy: string;
  cancelledByDate: string;
  cancellationConfirmedBy: string;
  cancellationConfirmedByDate: string;
  createdAt: string;
  tenantName: string;
  storeName: string[];
}

export interface DatesObject {
  [x: string]: string | dayjs.Dayjs | null;
}

export type NotificationType = "success" | "error" | "warning";

export interface SmokeTestRunList {
  storeId: string;
  storeName: string;
  subDomain: string;
  tenantId: string;
  tenantName: string;
  testStatus: string;
  userRole: string;
}
export interface UserData {
  agreementReceived: Date;
  coach: string;
  coachComments: string;
  coachValue: string;
  dms: string;
  dmsActiveDate: Date;
  dmsValue: string;
  groupName: string;
  groupNameComments: string;
  laborPricingGridEnteredInBzo: Date;
  laborPricingGridReceived: Date;
  laborPricingGridReceivedComments: string;
  launchCompleted: Date;
  modelMapping: Date;
  modelMappingComments: string;
  obAge: number;
  obMeetingCompleted: string;
  obMeetingCompletedComments: string;
  onboardingCoordinator: string;
  opCodesAvailable: Date;
  onboardingCoordinatorValue: string;
  opCodesCategorized: Date;
  partsMatrixEnteredInBzo: Date;
  partsMatrixReceived: Date;
  partsMatrixReceivedComments: string;
  readyForReview: Date;
  review_completed: Date;
  requestedDmsAccess: Date;
  requestedDmsAccessComments: string;
  requestedObMeetingComments: string;
  requestedObMeeting: Date;
  reviewCompleted: Date;
  salesperson: string;
  salespersonValue: string;
  scheduledLaunch: Date;
  slno: number;
  smokeTestCompleted: Date;
  storeName: string;
  storeNameComments: string;
  storeProjectId: number;
  tenantProjectId: number;
  totalDaysToLaunch: number;
  usersCreated: Date;
  usersCreatedComments: string;
}
