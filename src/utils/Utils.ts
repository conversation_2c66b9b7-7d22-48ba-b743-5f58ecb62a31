export function formatCellValue(params:any) {
    if (params != null && params != 0) {
      return Math.sign(params) > -1
        ? '$' +
            parseFloat(params)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      if (params == '  ' || params == '') {
        return params;
      } else {
        return 0;
      }
    }
  }
  export function formatCellValuePerc(heading:any, params:any) {
    if (heading.startsWith('-')) {
      if (params != null && params != 0) {
        return Math.sign(params) > -1
          ? parseFloat(params)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
          : Math.abs(parseFloat(params))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%';
      } else {
        if (params == '  ' || params == '') {
          return params;
        } else {
          return '0.00%';
        }
      }
    } else {
      if (params != null && params != 0) {
        return Math.sign(params) > -1
          ? '$' +
              parseFloat(params)
                .toFixed(2)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
          : '-$' +
              Math.abs(parseFloat(params))
                .toFixed(2)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else {
        if (params == '  ' || params == '') {
          return params;
        } else {
          return 0;
        }
      }
    }
  }
  export function formatCellValueCount(params:any) {
    if (params != null && params != 0) {
      return (Math.round(Number(params) * 100) / 100).toLocaleString();
    } else {
      if (params == '  ' || params == '') {
        return params;
      } else {
        return 0;
      }
    }
  }