import React from "react";
import DataFetchQueries from "../service/dataFetchQueries";
import { ColDef } from "ag-grid-community";
import moment from "moment";

const DailyLoadGridDefs = () => {
  const { GetDailyDataImportStatusFailed } = DataFetchQueries;
  const [allLoadList, setAllLoadList] = React.useState<any>([]);
  const [tenantList, setTenantList] = React.useState<any>([]);
  const [showDateSaveButton, setShowDateSaveButton] = React.useState(false);
  const [groupedData, setGroupedData] = React.useState<any>([]);
  const [loading, setLoading] = React.useState<boolean>(true);

  const statusFont = (params: any) => {
    return {
      color:
        params.value == "SUCCESS"
          ? "green"
          : params.value == "FAILED"
          ? "red"
          : params.value == "IN PROGRESS"
          ? "orange"
          : "black",
      fontWeight: "bold",
    };
  };
  const groupData = (res: any) => {
    return res.reduce((acc: Record<string, any[]>, item: any) => {
      if (!acc[item.clientname]) {
        acc[item.clientname] = [];
      }
      acc[item.clientname].push({
        storeId: item.storeId,
        storeName: item.storeName,
        startdate: item.startdate,
        enddate: item.enddate,
        statuss: item.statuss,
        dataLoadDate: item.dataLoadDate,
        errMessage: item.errMessage,
        dms: item.dms,
      });
      return acc;
    }, {});
  };
  const getDailyLoadList = async () => {
    GetDailyDataImportStatusFailed().then((res: any) => {
      const clientNames = Array.from(
        new Set(res.map((item: any) => item.clientname))
      ).sort();
      setTenantList(clientNames);
      setGroupedData(groupData(res));
    }).finally(() => {
      setLoading(false);
    });
  };
  const onGridReady = (params: any) => {
    // params?.api?.showLoadingOverlay();
    // getDailyLoadList();
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "Store Name",
      field: "storeName",
      flex: 5,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "DMS",
      field: "dms",
      flex: 2,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Data Load Date",
      field: "dataLoadDate",
      cellStyle: { textAlign: "left" },
      flex: 1.5,
      valueFormatter: (params: any) =>
        params.value ? moment(params.value).format("MM/DD/YYYY") : params.value,
    },
    {
      headerName: "Start Date",
      field: "startdate",
      flex: 1,
      cellStyle: { textAlign: "left" },
      valueFormatter: (params: any) =>
        params.value ? moment(params.value).format("MM/DD/YYYY") : params.value,
    },
    {
      headerName: "End Date",
      field: "enddate",
      cellStyle: { textAlign: "left" },
      flex: 1,
      valueFormatter: (params: any) =>
        params.value ? moment(params.value).format("MM/DD/YYYY") : params.value,
    },
    {
      headerName: "Status",
      field: "statuss",
      flex: 2,
      cellStyle: statusFont,
    },
    {
      headerName: "Reason",
      field: "errMessage",
      flex: 6,
    },
  ];
  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      enableValue: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      flex: 3,
      headerClass: "custom-header",
    };
  }, []);
  return {
    columnDefs,
    defaultColDef,
    onGridReady,
    allLoadList,
    showDateSaveButton,
    setShowDateSaveButton,
    getDailyLoadList,
    tenantList,
    groupedData,
    loading
  };
};

export default DailyLoadGridDefs;
