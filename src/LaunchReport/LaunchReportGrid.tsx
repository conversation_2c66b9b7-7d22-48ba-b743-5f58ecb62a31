import React, { useEffect, useState } from "react";
import { Box, Button, Grid, Typography } from "@mui/material";

import { AgGridReact } from "ag-grid-react";
import ReportGridDefs from "./ReportGridDefs";
import { Constants } from "../utils/constants";
import {
  GetLaunchReportsListDetails,
  GetAduOpenStoreWorkpackages,
} from "../service/launchReportsList";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import CircularProgress from "@mui/material/CircularProgress";
import { UserData } from "../types";
import ExampleComponent from "./HandsonTable";
import { useSidebarState } from "react-admin";
const LaunchReportGrid = () => {
  const [sidebarIsOpen] = useSidebarState();
  const [allMenu, setAllMenu] = useState<any[]>([]);
  const [selectedKey, setSelectedKey] = useState<string | null>(
    "onboardingTotal"
  );
  const [tableData, setTableData] = useState<UserData[]>([]);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    fetchLaunchReportData();
  }, []);
  useEffect(() => {
    console.log("selectedKeyyyyyyyy", selectedKey);
    fetchTableData();
  }, [selectedKey]);
  const fetchLaunchReportData = async () => {
    try {
      const res = await GetLaunchReportsListDetails();
      if (res) {
        setAllMenu(res[0]);
        if (res) {
        }
        // var resultArr = res.sort(customSort);
        // setAllMenu(resultArr);
      }
    } catch (error) {
      console.log(error);
      throw error;
    } finally {
    }
  };
  // const formatLabel = (label:any) => {  label.replace(/([A-Z])/g, " $1").replace(/^./, (str:any) => str.toUpperCase());}
  // const colorList = [
  //   "#FAD7A0",
  //   "#D2B4DE",
  //   "#A9CCE3",
  //   "#F5B7B1",
  //   "#A3E4D7",
  //   "#73C6B6",
  //   "#F9E79F",
  //   "#D7DBDD",
  //   "#BB8FCE",
  //   "#85C1E9",
  // ];
  const colorList = [
    "#FFB3BA", // pastel red
    "#FFDFBA", // pastel orange
    "#FFFFBA", // pastel yellow
    "#BAFFC9", // pastel green
    "#BAE1FF", // pastel blue
    "#D5BAFF", // pastel purple
    "#FFC8DD", // pastel pink
    "#C2F0FC", // pastel cyan
    "#E2F0CB", // pastel lime
    "#F4C2C2", // pastel rose
    "#F0E5DE", // pastel beige
  ];

  const handleButtonClick = (key: string) => {
    setSelectedKey(key);
    // fetchTableData();
  };
  const fetchTableData = async () => {
    setLoading(true);
    try {
      const res = await GetAduOpenStoreWorkpackages();
      const onboardingTotal = res.filter(
        (item: any) => item.launchCompleted == null
      );
      const ObMeetingrequest = res.filter(
        (item: any) =>
          item.obMeetingCompleted == null && item.launchCompleted == null
      );
      const dmsNotActive = res.filter(
        (item: any) =>
          item.dmsActiveDate == null && item.launchCompleted == null
      );
      const opsNotCategorized = res.filter(
        (item: any) =>
          item.opCodesCategorized == null && item.launchCompleted == null
      );
      const gridNotEntered = res.filter(
        (item: any) =>
          item.laborPricingGridEnteredInBzo == null &&
          item.launchCompleted == null
      );
      const modelsNotMapped = res.filter(
        (item: any) => item.modelMapping == null && item.launchCompleted == null
      );
      const matrixNotEntered = res.filter(
        (item: any) =>
          item.partsMatrixEnteredInBzo == null && item.launchCompleted == null
      );
      const usersCreated = res.filter(
        (item: any) => item.usersCreated == null && item.launchCompleted == null
      );
      const launchNotScheduled = res.filter(
        (item: any) =>
          item.scheduledLaunch == null && item.launchCompleted == null
      );
      const launchCompleted = res.filter(
        (item: any) => item.launchCompleted != null
      );
      console.log("selectedKeyyyy", selectedKey, dmsNotActive, res);
      if (res) {
        if (selectedKey === "onboardingTotal") {
          setTableData(onboardingTotal);
        } else if (selectedKey === "needsObMeeting") {
          setTableData(ObMeetingrequest);
        } else if (selectedKey === "dmsNotActive") {
          setTableData(dmsNotActive);
        } else if (selectedKey === "opsNotCategorized") {
          setTableData(opsNotCategorized);
        } else if (selectedKey === "gridNotEntered") {
          setTableData(gridNotEntered);
        } else if (selectedKey === "modelsNotMapped") {
          setTableData(modelsNotMapped);
        } else if (selectedKey === "matrixNotEntered") {
          setTableData(matrixNotEntered);
        } else if (selectedKey === "launchNotScheduled") {
          setTableData(launchNotScheduled);
        } else if (selectedKey === "launchCompleted") {
          setTableData(launchCompleted);
        } else if (selectedKey === "usersNotCreated") {
          setTableData(usersCreated);
        } else if (selectedKey === "allStores") {
          setTableData(res);
        }
      }
    } catch {
    } finally {
      setLoading(false);
    }
  };
  return (
    <Box
      sx={{
        paddingX: "10px",
        mt: "15px",
        width: "100%",
        "@media (max-width: 1440px)": {
          width: "83vw",
        },
        "@media (max-width: 1366px)": {
          width: "83vw",
        },
        "@media (max-width: 1280px)": {
          width: "83vw",
        },
        "@media (max-width: 1000px)": {
          width: "83vw",
        },
      }}>
      <Grid container spacing={2}>
        {Object.entries(allMenu).map(
          ([key, value]: [string, number | string], index: number) => (
            <Grid item key={key}>
              <Button
                variant='contained'
                disableElevation
                onClick={() => {
                  if (!loading) {
                    setSelectedKey(key);
                  }
                }}
                disabled={loading && selectedKey !== key}
                sx={{
                  backgroundColor: colorList[index % colorList.length],
                  color: "#000",
                  height: 100,
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  textAlign: "center",
                  padding: 1,
                  width: 100, // default width
                  minWidth: 80,
                  boxShadow: "none",
                  "&:hover": {
                    backgroundColor: colorList[index % colorList.length],
                    opacity: 0.9,
                  },
                  "&.Mui-disabled": {
                    backgroundColor: colorList[index % colorList.length],
                    color: "#aaa",
                    opacity: 1,
                  },
                  // 👉 CUSTOM WIDTH FOR SMALL LAPTOP RESOLUTIONS
                  "@media (max-width: 1440px)": {
                    width: 80,
                  },
                  "@media (max-width: 1366px)": {
                    width: 80,
                  },
                  "@media (max-width: 1280px)": {
                    width: 80,
                  },
                  "@media (max-width: 1000px)": {
                    width: 80,
                  },
                }}>
                <Typography
                  variant='body2'
                  sx={{
                    mb:
                      key
                        .replace(/([A-Z])/g, " $1")
                        .replace(/^./, (str) => str.toUpperCase()) ===
                      "All Stores"
                        ? 3.5
                        : 1,
                    // 👉 CUSTOM WIDTH FOR SMALL LAPTOP RESOLUTIONS
                    "@media (max-width: 1440px)": {
                      fontSize: "0.7rem",
                    },
                    "@media (max-width: 1366px)": {
                      fontSize: "0.7rem",
                    },
                    "@media (max-width: 1280px)": {
                      fontSize: "0.7rem",
                    },
                    "@media (max-width: 1000px)": {
                      fontSize: "0.6rem",
                    },
                  }}>
                  {key
                    .replace(/([A-Z])/g, " $1")
                    .replace(/^./, (str) => str.toUpperCase())}
                </Typography>

                <Box display='flex' alignItems='center' justifyContent='center'>
                  {selectedKey === key ? (
                    <CheckBoxIcon fontSize='small' />
                  ) : (
                    <CheckBoxOutlineBlankIcon fontSize='small' />
                  )}

                  <Typography variant='h6' sx={{ ml: 1 }}>
                    {value}
                  </Typography>
                </Box>
              </Button>
            </Grid>
          )
        )}
      </Grid>
      {selectedKey && (
        <Box sx={{ mt: "15px", width: "100%" }}>
          <div
            id='hand-table'
            // className={Constants.ag_grid_theme}
            className={sidebarIsOpen ? "sidebar-open" : "sidebar-closed"}
            style={{
              height: "90vh",
              width: sidebarIsOpen ? "84.5vw" : "94.5vw",
            }}>
            {loading ? (
              <Box
                display='flex'
                justifyContent='center'
                alignItems='center'
                height={200}>
                <CircularProgress />
              </Box>
            ) : (
              <ExampleComponent
                tableData={tableData}
                selectedKey={selectedKey}
              />
            )}
          </div>
        </Box>
      )}
    </Box>
  );
};

export default LaunchReportGrid;
