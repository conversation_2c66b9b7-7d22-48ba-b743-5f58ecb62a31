import { Link } from "@mui/material";
import { ColDef } from "ag-grid-community";
import moment from "moment";
import React, { useState } from "react";
import DataFetchQueries from "../service/dataFetchQueries";

const ReportGridDefs = () => {
  const { GetLaunchReportsList } = DataFetchQueries;
  const [launchReportList, setLaunchReportList] = useState<any>([]);

  const fetchLaunchReportsList = async () => {
    try {
      const res = await GetLaunchReportsList();
      setLaunchReportList(res);
    } catch (error) {
      console.error("Error fetching launch reports list:", error);
    }
  };

  const onGridReady = (params: any) => {
    params?.api?.showLoadingOverlay();
    fetchLaunchReportsList().then(() => {
      params?.api?.hideOverlay();
    });
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "Store Name",
      field: "storeProjectName",
      width: 350,
      cellStyle: { textAlign: "left" },
      valueFormatter: (params: any) => params.value,
      cellRenderer: (params: any) => params.value,
      pinned: "left",
    },
    {
      headerName: "Tenant Name",
      field: "tenantProjectName",
      width: 300,
      cellStyle: { textAlign: "left" },
      valueFormatter: (params: any) => params.value,
      cellRenderer: (params: any) => params.value,
    },
    {
      headerName: "Rooftops",
      field: "roofTops",
      width: 150,
      cellStyle: { textAlign: "left" },
      valueFormatter: (params: any) => params.value,
    },
    {
      headerName: "Lead Credit",
      field: "leadCredit",
      width: 150,
      cellStyle: { textAlign: "left" },
      valueFormatter: (params: any) => params.value,
    },
    {
      headerName: "DMS",
      field: "dmsValue",
      width: 150,
      cellStyle: { textAlign: "left" },
      valueFormatter: (params: any) => params.value,
    },
    {
      headerName: "Signed Agreement",
      field: "signedAgreement",
    },
    {
      headerName: "Update CRM - FOPC info. & Agreement",
      field: "updateCrmAgreement",
    },
    {
      headerName: "Launch Date Target",
      field: "launchDateTarget",
    },
    {
      headerName: "Configure Keycloak",
      field: "configureKeycloak",
    },
    {
      headerName: "Store Naming/Dropdown Order sent to Engineering Team",
      field: "storeNaming",
    },
    {
      headerName: "Configure Identity Provider",
      field: "configureIdentityProvider",
    },
    {
      headerName: "Request For Dms Access",
      field: "requestForDmsAccess",
    },
    {
      headerName: "Receive Confirmation From Dms Provider",
      field: "recieveConfirmationFromDmsProvider",
    },

    {
      headerName: "Configure Bzo For Store",
      field: "configureBzoForStore",
    },
    {
      headerName: "Add Store Meta Data",
      field: "addStoreMetaData",
    },
    {
      headerName: "Bulk Data Pull From Dms To Db",
      field: "bulkDataPullFromDmsToDb",
    },
    {
      headerName: "Opcode Categorization",
      field: "opcodeCategorization",
    },
    {
      headerName: "Post Onboarding Data",
      field: "postOnboardingData",
    },
    {
      headerName: "Setting Up Daily Load",
      field: "settingUpDailyLoad",
    },
    {
      headerName: "Add Enrollments",
      field: "addEnrollments",
    },
    {
      headerName: "Request For Enrollment Items From Store",
      field: "requestForEnrollmentItemsFromStore",
    },
    {
      headerName: "Receive Enrollment Items From Store",
      field: "recieveEnrollmentItemsFromStore",
    },
    {
      headerName: "Monthly Sales Report Total Shop",
      field: "monthlySalesReportTotalShop",
    },
    {
      headerName: "Monthly Sales Report By Store And Advisor",
      field: "monthlySalesReportsByStoreAndAdvisor",
    },
    {
      headerName: "Parts Pricing Matrix Added To Bzo",
      field: "partsPricingMatrixAddedToBzo",
    },
    {
      headerName: "Add Repair Pricing Policy Labor Grid To Bzo",
      field: "addRepairPricingPolicyLaborGridToBzo",
    },
    {
      headerName: "Setup Keycloak",
      field: "setupKeycloak",
    },
    {
      headerName: "Configure Group From Bzo",
      field: "configureGroupFromBzo",
    },
    {
      headerName: "Credentials Verified With Store",
      field: "credentialsVerifiedWithStore",
    },
    {
      headerName: "File Upload Validation Result On Bzo",
      field: "fileUploadValidationResultOnBzo",
    },
    {
      headerName: "Site Ready For Review",
      field: "siteReadyForReview",
    },
    {
      headerName: "Fopc Site Ready For Review Uat",
      field: "fopcSiteReadyForReviewUat",
    },
    {
      headerName: "Fopc Site Ready For Review Dev",
      field: "fopcSiteReadyForReviewDev",
    },
    {
      headerName: "Fopc Site Ready For Launch Aws",
      field: "fopcSiteReadyForLaunchAws",
    },
    {
      headerName: "Setup Site Access For Store Admin And Group Users",
      field: "setupSiteAccessForStoreAdminAndGroupUsers",
    },
  ];
  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { textAlign: "center" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      width: 180,
      suppressSizeToFit: true,
      valueFormatter: (params: any) => {
        return params.value && moment(params.value).format("MM/DD/YYYY");
      },
      cellRenderer: (params: any) => {
        let valueId = launchReportList.filter(
          (itm: any) => itm.slno === params.data.slno
        );
        let linkValueId =
          params.colDef.field === "roofTops" ||
          params.colDef.field === "leadCredit" ||
          params.colDef.field === "dmsValue"
            ? valueId[0]["signedAgreementId"]
            : valueId[0][`${params.colDef.field}Id`];
        let OPurl = process.env.REACT_APP_OP_URL + linkValueId;

        return (
          <Link
            href={OPurl}
            target="_blank"
            rel="noopener noreferrer"
            underline="hover"
          >
            {params.colDef.field === "roofTops" ||
            params.colDef.field === "leadCredit" ||
            params.colDef.field === "dmsValue"
              ? params.value
              : params.value
              ? moment(params.value).format("MM/DD/YYYY")
              : "..."}
          </Link>
        );
      },
    };
  }, [launchReportList]);
  return {
    columnDefs,
    defaultColDef,
    onGridReady,
    launchReportList,
  };
};

export default ReportGridDefs;
