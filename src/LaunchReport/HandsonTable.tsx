import { FC } from "react";
import { HotTable, HotTableClass } from "@handsontable/react";
import { registerAllModules } from "handsontable/registry";
import { ColumnSettings } from "handsontable/settings";
import "handsontable/dist/handsontable.full.min.css";
import "handsontable/styles/ht-theme-main.css";
import {
  UpdateTextFieldComments,
  GetDropDownValues,
  UpdateDropdownField,
} from "../service/launchReportsList";
import Handsontable from "handsontable";
import { MenuItemConfig } from "handsontable/plugins/contextMenu";
import React, { useEffect, useState, useRef, useCallback } from "react";
import "../style.css";
import * as XLSX from "xlsx-js-style";
import { Grid, IconButton, Tooltip, Typography } from "@mui/material";
import FileDownloadOutlinedIcon from "@mui/icons-material/FileDownloadOutlined";
import { useSidebarState } from "react-admin";

// Register all modules (includes Comments plugin)
registerAllModules();

interface ExampleComponentProps {
  tableData: any[]; // You can replace `any` with your specific data type
  selectedKey: any;
}

const ExampleComponent: FC<ExampleComponentProps> = ({
  tableData,
  selectedKey,
}) => {
  const prevCommentRef = useRef<{ [key: string]: string }>({});
  const isInitializing = useRef(true);
  const isProgrammatic = useRef(false);

  const [dropdownSources, setDropdownSources] = useState<{
    [key: string]: any[];
  }>({});

  const fieldNames = [
    "Tag Group Pay",
    "Onboarding Coordinator",
    "Salesperson",
    "DMS",
    "Coach",
  ];

  const [sidebarIsOpen] = useSidebarState();

  useEffect(() => {
    function adjustMainHeight() {
      const main = document.getElementById("main-content");
      if (main) {
        main.style.height = "auto";
        const contentHeight = main.scrollHeight + 15;
        main.style.height = contentHeight + "px";
      }

      const mainHot = document.getElementById("hot");
      if (mainHot) {
        mainHot.style.width = sidebarIsOpen ? "90vw" : "87vw";
      }
    }

    adjustMainHeight();

    window.addEventListener("resize", adjustMainHeight);

    return () => {
      window.removeEventListener("resize", adjustMainHeight);
    };
  }, [sidebarIsOpen]);

  useEffect(() => {
    const syncScroll = () => {
      const container = document.getElementById("hot");
      const customScrollbar = document.getElementById("customScrollbar");

      if (container && customScrollbar && customScrollbar.firstElementChild) {
        const totalTableWidth = 900;
        const fixedColsCount = 3;
        const colWidth = 337;
        const fixedColumnsWidth = fixedColsCount * colWidth;
        const scrollableWidth = totalTableWidth - fixedColumnsWidth;

        customScrollbar.style.width = scrollableWidth + "px";

        const scrollableColsCount = 10 - fixedColsCount;
        const scrollableContentWidth = scrollableColsCount * colWidth;
        (customScrollbar.firstElementChild as HTMLElement).style.width =
          scrollableContentWidth + "px";

        const wtHolders = document.querySelectorAll(".wtHolder");

        wtHolders.forEach((wtHolder) => {
          const element = wtHolder as HTMLElement;
          if (element.closest(".ht_clone_left")) {
            element.style.setProperty("padding-bottom", "15px");
          }

          // Sync scroll positions both ways
          customScrollbar.addEventListener("scroll", () => {
            element.scrollLeft = customScrollbar.scrollLeft;
          });

          element.addEventListener("scroll", () => {
            customScrollbar.scrollLeft = element.scrollLeft;
          });
        });
      } else {
        console.warn(
          "Required elements not found: container, customScrollbar, or its first child."
        );
      }
    };

    // Wait for next tick or Handsontable init, to be safe
    const timeoutId = setTimeout(syncScroll, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  useEffect(() => {
    fieldNames.forEach((field) => {
      GetDropDownValues(field).then((res: any) => {
        const source = res.map(
          (opt: { customOptionValue: any }) => opt.customOptionValue
        );
        const normalizedSource = moveSelectToTop(source);
        setDropdownSources((prev) => ({ ...prev, [field]: normalizedSource }));
      });
    });
  }, []);
  const moveSelectToTop = (source: any[]) => {
    const rest = source.filter((item) => item !== "-- Select --");
    return ["-- Select --", ...rest];
  };
  useEffect(() => {
    const hot = hotRef.current?.hotInstance;
    if (!hot) return;

    const afterFilterHandler = () => {
      const visibleRows = hot.countVisibleRows?.() ?? hot.countRows();
      setShowNoData(visibleRows === 0);
    };

    hot.addHook("afterFilter", afterFilterHandler);
  }, []);
  useEffect(() => {
    const hot = hotRef.current?.hotInstance;

    if (!hot) return;

    // Wait until Handsontable is ready
    if (!isInitializing.current) {
      commentCells.forEach(({ row, col, comment }) => {
        safeSetComment(row, col, comment.value);
      });
    }
  }, [tableData]); // Or add any other dependency that updates the table
  function safeSetComment(row: number, col: number, commentValue: string) {
    isProgrammatic.current = true;
    hotRef.current?.hotInstance?.setCellMeta(row, col, "comment", {
      value: commentValue,
    });
    prevCommentRef.current[`${row}-${col}`] = commentValue;
    isProgrammatic.current = false;
  }
  const customDropdownRenderer = (
    instance: Handsontable,
    td: HTMLTableCellElement,
    row: number,
    col: number,
    prop: string | number,
    value: any,
    cellProperties: Handsontable.CellProperties
  ) => {
    // Use the default text renderer
    Handsontable.renderers.TextRenderer(
      instance,
      td,
      row,
      col,
      prop,
      value,
      cellProperties
    );
    // Display empty string if value is '--Select--'
    if (value == "-- Select --") {
      td.innerText = " ";
    }
    // Add dropdown icon
    const icon = document.createElement("span");
    icon.className = "dropdown-icon";
    icon.innerHTML = "▾"; // Or use ▼ / ⌄ or an actual icon

    // Make sure the icon doesn't block cell interaction
    icon.style.pointerEvents = "none";

    td.classList.add("always-dropdown-cell"); // Add class for styling
    td.appendChild(icon);
  };

  const formatDateRenderer = (
    instance: Handsontable.Core,
    td: HTMLTableCellElement,
    row: number,
    col: number,
    prop: string | number,
    value: any,
    cellProperties: Handsontable.CellProperties
  ) => {
    let displayValue = "";

    if (value) {
      const parsedDate = new Date(value);
      const isValid = !isNaN(parsedDate.getTime());

      if (isValid) {
        const mm = String(parsedDate.getMonth() + 1).padStart(2, "0");
        const dd = String(parsedDate.getDate()).padStart(2, "0");
        const yyyy = parsedDate.getFullYear();
        displayValue = `${mm}/${dd}/${yyyy}`;
      } else {
        displayValue = value; // fallback to original value if invalid
      }
    }

    Handsontable.renderers.TextRenderer(
      instance,
      td,
      row,
      col,
      prop,
      displayValue,
      cellProperties
    );
    return td;
  };
  console.log("Table data >>>", tableData);
  const [showNoData, setShowNoData] = useState(false);
  const rowHeight = 22;
  const headerHeight = 30;
  const maxHeight = 600;

  var tableHeight =
    Math.min((tableData?.length || 1) * rowHeight + headerHeight, maxHeight) +
    50;
  const hotRef = useRef<HotTableClass>(null);
  const scrollRef = useRef(null);

  const TAGGED_CELL_CLASS = "tagged-cell";
  const columns: ColumnSettings[] = [
    {
      title: "OB Age",
      type: "numeric",
      data: "obAge",
      width: 60,
      headerClassName: "center-header",
      editor: false,
    },
    {
      title: "Group Pay",
      type: "dropdown",
      data: "tagGroupPay",
      selectOptions: dropdownSources["Tag Group Pay"],
      renderer: customDropdownRenderer,
      width: 80,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
      editor: "select",
      className: "dropdown-only",
    },
    {
      title: "Group Name",
      type: "text",
      data: "groupName",
      width: 200,
      headerClassName: "center-header",
      editor: false,
    },
    {
      title: "Store Name",
      type: "text",
      data: "storeName",
      width: 300,
      headerClassName: "center-header",
      editor: false,
    },
    {
      title: "Onboarding<br>Coordinator",
      type: "dropdown",
      data: "onboardingCoordinator",
      width: 100,
      selectOptions: dropdownSources["Onboarding Coordinator"],
      renderer: customDropdownRenderer,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
      editor: "select",
      className: "dropdown-only",
    },
    {
      title: "Sales Person",
      type: "dropdown",
      data: "salesperson",
      width: 100,
      selectOptions: dropdownSources["Salesperson"],
      renderer: customDropdownRenderer,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
      editor: "select",
      className: "dropdown-only",
    },
    {
      title: "DMS",
      type: "dropdown",
      data: "dms",
      // className: "htCenter",
      width: 80,
      selectOptions: dropdownSources["DMS"],
      renderer: customDropdownRenderer,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
      editor: "select",
      className: "dropdown-only",
    },
    {
      title: "Agreement<br>Received",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "agreementReceived",
      width: 100,
      datePickerConfig: {
        maxDate: new Date(
          new Date().getFullYear(),
          new Date().getMonth(),
          new Date().getDate()
        ),
      },
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Requested<br>DMS Access",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      data: "requestedDmsAccess",
      width: 100,
      headerClassName: "center-header",
      renderer: formatDateRenderer,
      strict: true,
      allowInvalid: false,
    },
    {
      title: "DMS Active<br>Date",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "dmsActiveDate",
      className: "htCenter",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    // {
    //   title: "Requested OB Meeting Comments",
    //   type: "text",
    //   data: "requestedObMeetingComments",
    //   width: 100,
    // },
    {
      title: "Requested OB<br>Meeting",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "requestedObMeeting",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },

    {
      title: "OB Meeting<br>Completed",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "obMeetingCompleted",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "OpCodes<br>Available",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "opCodesAvailable",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "OpCodes<br>Categorized",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "opCodesCategorized",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Labor<br>Pricing/Grid<br>Received",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "laborPricingGridReceived",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Labor<br>Pricing/Grid<br>Entered in BZO",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "laborPricingGridEnteredInBzo",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Model<br>Mapping",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "modelMapping",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Parts Matrix<br>Received",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "partsMatrixReceived",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Parts Matrix<br>Entered In BZO",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "partsMatrixEnteredInBzo",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Users Created",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "usersCreated",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Smoke Test<br>Completed",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "smokeTestCompleted",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Ready For<br>Review",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "readyForReview",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Review<br>Completed",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "reviewCompleted",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },

    {
      title: "Coach",
      type: "dropdown",
      data: "coach",
      width: 80,
      selectOptions: dropdownSources["Coach"],
      renderer: customDropdownRenderer,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
      editor: "select",
      className: "dropdown-only",
    },
    {
      title: "Scheduled<br>Launch",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "scheduledLaunch",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Launch<br>Completed",
      type: "date",
      dateFormat: "MM/DD/YYYY",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "launchCompleted",
      width: 100,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Total Days<br>To Launch",
      type: "numeric",
      data: "totalDaysToLaunch",
      width: 100,
      headerClassName: "center-header",
      editor: false,
    },

    /////////////////////////// ///////////////////////////
    // {
    //   title: "Coach Comments",
    //   type: "text",
    //   data: "coachComments",
    //   correctFormat: true,
    //   className: "htRight",
    // },
    // {
    //   title: "Coach Value",
    //   type: "text",
    //   data: "coachValue",
    //   width: 80,
    // },

    // {
    //   title: "DMS Value",
    //   type: "text",
    //   data: "dmsValue",
    //   className: "htCenter",
    //   width: 80,
    // },

    // {
    //   title: "Labor Pricing Grid Received Comments",
    //   type: "text",
    //   data: "laborPricingGridReceivedComments",
    // },
    // {
    //   title: "Group Name Comments",
    //   type: "text",
    //   data: "groupNameComments",
    // },
    // {
    //   title: "Store Name Comments",
    //   type: "text",
    //   data: "storeNameComments",
    // },
    // {
    //   title: "Model Mapping - Comments",
    //   type: "text",
    //   data: "modelMappingComments",
    // },

    // {
    //   title: "OB Meeting Completed Comments",
    //   type: "text",
    //   data: "obMeetingCompletedComments",
    // },

    // {
    //   title: "Onboarding Coordinator Value",
    //   type: "text",
    //   data: "onboardingCoordinatorValue",
    //   width: 80,
    // },

    // {
    //   title: "Parts Matrix Received Comments",
    //   type: "text",
    //   data: "partsMatrixReceivedComments",
    // },

    // {
    //   title: "Requested Dms Access Comments",
    //   type: "text",
    //   data: "requestedDmsAccessComments",
    // },

    // {
    //   title: "Sales person Value",
    //   type: "text",
    //   data: "salespersonValue",
    //   width: 80,
    // },

    // {
    //   title: "Slno",
    //   type: "numeric",
    //   data: "slno",
    //   width: 80,
    // },

    // {
    //   title: "Store Project Id",
    //   type: "numeric",
    //   data: "storeProjectId",
    //   width: 70,
    // },
    // {
    //   title: "Tenant Project Id",
    //   type: "numeric",
    //   data: "tenantProjectId",
    //   width: 70,
    // },

    // {
    //   title: "Users Created Comments",
    //   type: "text",
    //   data: "usersCreatedComments",
    //   width: 80,
    // },
  ];
  const commentColumnMapping: Record<string, string> = {
    groupName: "groupNameComments",
    storeName: "storeNameComments",
    requestedObMeeting: "requestedObMeetingComments",
    requestedDmsAccess: "requestedDmsAccessComments",
    obMeetingCompleted: "obMeetingCompletedComments",
    laborPricingGridReceived: "laborPricingGridReceivedComments",
    modelMapping: "modelMappingComments",
    partsMatrixReceived: "partsMatrixReceivedComments",
    usersCreated: "usersCreatedComments",
    coach: "coachComments",
  };
  const tagColumnMapping: Record<string, string> = {
    groupName: "taggrouphold",
    storeName: "tagStoreHold",
    dms: "tagDmsAction",
    requestedDmsAccess: "tagRequestedDmsAccessFollowup",
    requestedObMeeting: "tagRequestedObMeetingFollowup",
    obMeetingCompleted: "tagObMeetingCompletedAction",
    laborPricingGridReceived: "tagLaborPricingGridReceived",
    scheduledLaunch: "tagScheduledLaunchActionNeeded",
    partsMatrixReceived: "tagPartsMatrixReceivedFollowup", // double mapping (check if intentional)
  };

  // Map column keys to indices
  const columnKeys = columns.map((col: any) => col.data);
  const commentCells: {
    row: number;
    col: number;
    comment: { value: string };
  }[] = tableData.flatMap((row: Record<string, any>, rowIndex: number) =>
    Object.entries(commentColumnMapping).flatMap(([dataKey, commentKey]) => {
      const colIndex = columnKeys.indexOf(dataKey);
      const commentValue = row[commentKey];

      // Only add comment cell if value is non-empty and column exists
      if (colIndex !== -1 && commentValue?.trim()) {
        return [
          {
            row: rowIndex,
            col: colIndex,
            comment: { value: commentValue },
          },
        ];
      }

      return []; // Skip empty comment
    })
  );
  // const tagCells: {
  //   row: number;
  //   col: number;
  //   className: string;
  // }[] = tableData.flatMap((row: Record<string, any>, rowIndex: number) =>
  //   Object.entries(tagColumnMapping).flatMap(([dataKey, tagKey]) => {
  //     const colIndex = columnKeys.indexOf(dataKey);
  //     const tagValue = row[tagKey];

  //     if (colIndex !== -1 && tagValue === "t") {
  //       console.log("tagCells", dataKey, tagKey, colIndex, tagValue);

  //       return [
  //         {
  //           row: rowIndex,
  //           col: colIndex,
  //           className: "tagged-cell", // Apply a CSS class for tags
  //         },
  //       ];
  //     }

  //     return [];
  //   })
  // );
  const tagCells = tableData.flatMap(
    (row: Record<string, any>, rowIndex: number) =>
      Object.entries(tagColumnMapping).flatMap(([dataKey, tagKey]) => {
        const tagValue = row[tagKey];
        const colIndex = columnKeys.indexOf(dataKey);
        if (colIndex !== -1 && tagValue === "t") {
          if (tagKey === "taggrouphold") {
            return columnKeys.map((_, colIdx) => ({
              row: rowIndex,
              col: colIdx,
              className: "onholdGrp-cell",
            }));
          } else if (tagKey === "tagStoreHold") {
            return columnKeys.map((_, colIdx) => ({
              row: rowIndex,
              col: colIdx,
              className: "onhold-cell",
            }));
          } else {
            return [
              {
                row: rowIndex,
                col: colIndex,
                className: "tagged-cell",
              },
            ];
          }
        }

        return [];
      })
  );

  const mergedCells = [...commentCells, ...tagCells];

  const handleTextFieldUpdate = (
    row: number,
    col: number,
    key: string,
    value: any
  ) => {
    const newValue = value.value;
    const cellKey = `${row}-${col}`;
    const prevValue = prevCommentRef.current[cellKey] ?? "";

    const fieldNameMapComments: { [key: string]: string } = {
      modelMappingComments: "Model Mapping - Comments",
      groupNameComments: "Group Name Comments",
      storeNameComments: "Store Name Comments",
      requestedObMeetingComments: "Requested OB Meeting - Comments",
      requestedDmsAccessComments: "Requested DMS Access - Comments",
      obMeetingCompletedComments: "OB Meeting Completed - Comments",
      laborPricingGridReceivedComments:
        "Labor Pricing/Grid Received - Comments",
      partsMatrixReceivedComments: "Parts Matrix Received - Comments",
      usersCreatedComments: "Users Created - Comments",
      coachComments: "Coach - Comments",
    };
    const fieldNameMap: { [key: string]: string } = {
      groupName: "Tag Group Hold",
      storeName: "Tag Store Hold",
      dms: "Tag DMS Action",

      requestedDmsAccess: "Tag Requested DMS Access Followup",
      requestedObMeeting: "Tag Requested OB Meeting Followup",
      obMeetingCompleted: "Tag OB Meeting Completed Action",
      laborPricingGridReceived: "Tag Labor Pricing Grid Received",
      partsMatrixReceived: "Tag Parts Matrix Received Followup",
      scheduledLaunch: "Tag Scheduled Launch Action Needed",
    };
    const workPackageId = tableData[row]?.slno;
    // tag functionality
    if (key === "addTags" || key === "removeTags") {
      const fieldName: string = columns[col]?.data as string; // e.g. "modelMappingComments"
      const fieldValue = value;
      const displayName = fieldNameMap[fieldName];
      if (displayName && fieldValue && workPackageId) {
        UpdateTextFieldComments(displayName, fieldValue, workPackageId)
          .then(() => {
            console.log(`Tags saved for ${fieldName}`);
          })
          .catch((err) => {
            console.error("Failed to save tags", err);
          });
      }
    }
    // deleteComment Functionality
    else if (key === "commentsRemove") {
      const fieldName = columns[col].data + "Comments";
      const fieldValue = value.value;
      const displayName = fieldNameMapComments[fieldName];
      if (displayName && workPackageId) {
        UpdateTextFieldComments(displayName, "", workPackageId)
          .then(() => {
            console.log(`Comment Deleted for ${fieldName}`);
          })
          .catch((err) => {
            console.error("Failed to save comment", err);
          });
      }
    }

    if (key === "comment" && newValue !== prevValue) {
      const fieldName = columns[col]?.data + "Comments";
      const displayName = fieldNameMapComments[fieldName];
      console.log("displayNamemm", newValue, prevValue);
      if (displayName && workPackageId) {
        UpdateTextFieldComments(displayName, newValue, workPackageId)
          .then(() => {
            console.log(`Comment updated for ${fieldName}`);
            prevCommentRef.current[cellKey] = newValue; // Update tracker
          })
          .catch((err) => {
            console.error("Failed to update comment:", err);
          });
      }
    }
    // AddComment Functionality
    // else if (
    //   key === "comment" &&
    //   typeof value?.value === "string" &&
    //   value?.value == ""
    // ) {
    //   const fieldName = columns[col].data + "Comments"; // e.g. "modelMappingComments"
    //   const fieldValue = value.value;
    //   const displayName = fieldNameMapComments[fieldName];

    //   if (displayName && workPackageId) {
    //     UpdateTextFieldComments(displayName, "", workPackageId)
    //       .then(() => {
    //         console.log(`Comment Deleted for ${fieldName}`);
    //       })
    //       .catch((err) => {
    //         console.error("Failed to save comment", err);
    //       });
    //   }
    // }
    // // Edit/Clear comment Functionality
    // else if (key === "comment" && value?.value && value?.value != "") {
    //   const newValue = typeof value === "string" ? value : value?.value ?? "";
    //   const cellKey = `${row}-${col}`;
    //   const prevValue = prevCommentRef.current[cellKey];

    //   const fieldName = columns[col].data + "Comments"; // e.g. "modelMappingComments"
    //   const fieldValue = value.value;
    //   if (
    //     (prevValue && newValue !== prevValue) ||
    //     (typeof prevValue === "string" && prevValue == "")
    //   ) {
    //     const displayName = fieldNameMapComments[fieldName];

    //     if (displayName && fieldValue && workPackageId) {
    //       UpdateTextFieldComments(displayName, fieldValue, workPackageId)
    //         .then(() => {
    //           console.log(`Comment saved for ${fieldName}`);
    //         })
    //         .catch((err) => {
    //           console.error("Failed to save comment", err);
    //         });
    //     }
    //   }
    // }
  };
  function convertDateFormat(dateStr: string): string {
    if (!dateStr || typeof dateStr !== "string") {
      throw new Error("Invalid input: dateStr is required.");
    }

    // ✅ If already in YYYY-MM-DD format, return it
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
      return dateStr;
    }

    const parts = dateStr.split("/");

    if (parts.length !== 3) {
      throw new Error("Invalid date format. Expected MM/DD/YYYY.");
    }

    let [month, day, year] = parts;

    if (!month || !day || !year) {
      throw new Error("Missing date parts. Expected MM/DD/YYYY.");
    }

    month = month.trim().padStart(2, "0");
    day = day.trim().padStart(2, "0");

    return `${year.trim()}-${month}-${day}`;
  }

  const handleSaveDropdown = (
    row: number,
    prop: string,
    newValue: any,
    oldValue: any
  ) => {
    const workPackageId = Number(tableData[row]?.slno);

    const contentValue = newValue;
    const fieldNameMap: { [key: string]: string } = {
      tagGroupPay: "Tag Group Pay",
      onboardingCoordinator: "Onboarding Coordinator",
      salesperson: "Salesperson",
      dms: "DMS",
      coach: "Coach",
    };

    const displayName = fieldNameMap[prop];

    if (displayName && contentValue && workPackageId) {
      UpdateDropdownField(displayName, contentValue, workPackageId)
        .then(() => {
          console.log(`Value updated for ${displayName}`);
        })
        .catch((err) => {
          console.error("Failed to save comment", err);
        });
    }
  };
  const handleSaveDatePicker = (
    row: number,
    prop: string,
    newValue: any,
    oldValue: any
  ) => {
    const workPackageId = tableData[row]?.slno;
    //13/03/2025-2025-03-13
    const DateValue = newValue && convertDateFormat(newValue);
    const fieldNameMap: { [key: string]: string } = {
      agreementReceived: "Agreement Received",
      requestedDmsAccess: "Requested DMS Access",
      dmsActiveDate: "DMS Active Date",
      obMeetingCompleted: "OB Meeting Completed",
      opCodesAvailable: "Op Codes Available",
      opCodesCategorized: "Op Codes Categorized",
      laborPricingGridReceived: "Labor Pricing/Grid Received",

      laborPricingGridEnteredInBzo: "Labor Pricing/Grid Entered in BZO",
      modelMapping: "Model Mapping",
      partsMatrixReceived: "Parts Matrix Received",
      partsMatrixEnteredInBzo: "Parts Matrix Entered in BZO",
      usersCreated: "Users Created",
      smokeTestCompleted: "Smoke Test Completed",
      readyForReview: "Ready For Review",
      reviewCompleted: "Review Completed",
      scheduledLaunch: "Scheduled Launch",
      launchCompleted: "Launch Completed",
      requestedObMeeting: "Requested OB Meeting",
    };
    const displayName = fieldNameMap[prop];

    if (displayName && DateValue && workPackageId) {
      UpdateTextFieldComments(displayName, DateValue, workPackageId)
        .then(() => {
          console.log(`Comment saved for ${displayName}`);
        })
        .catch((err) => {
          console.error("Failed to save comment", err);
        });
    }
  };
  const columnsToHide = [
    "laborPricingGridReceivedComments",
    "modelMappingComments",
    "obMeetingCompletedComments",
    "partsMatrixReceivedComments",
    "requestedDmsAccessComments",
    "usersCreatedComments",
    "coachComments",
    "groupNameComments",
    "storeNameComments",
    "requestedObMeetingComments",
  ];
  const allowedTagColumns = [
    "groupName",
    "storeName",
    "dms",
    "requestedDmsAccess",
    "requestedObMeeting",
    "obMeetingCompleted",
    "laborPricingGridReceived",
    "scheduledLaunch",
    "partsMatrixReceived",
  ];
  const hiddenColumnIndexes = columns
    .map((col: any, index: any) =>
      columnsToHide.includes(col.data) ? index : -1
    )
    .filter((index) => index !== -1);
  //comments allowing columns
  const allowedCommentColumns = [
    "groupName",
    "storeName",
    "requestedObMeeting",
    "requestedDmsAccess",
    "obMeetingCompleted",
    "laborPricingGridReceived",
    "modelMapping",
    "partsMatrixReceived",
    "usersCreated",
    "coach",
  ];
  const columnTagMap: { [key: string]: string } = {
    groupName: "On hold tag",
    storeName: "On hold tag",
    dms: "Action Needed tag",
    obMeetingCompleted: "Action Needed tag",
    laborPricingGridReceived: "Action Needed tag",
    partsMatrixReceived: "Action Needed tag",
    scheduledLaunch: "Action Needed tag",
    requestedDmsAccess: "Follow Up tag",
    requestedObMeeting: "Follow Up tag",

    // Add more column-property-to-tag-name mappings as needed
  };
  //context menu custom items for enabling and disabling
  const contextMenu: {
    callback: (key: string, options: any[]) => void;
    items: Record<string, MenuItemConfig>;
  } = {
    callback: (key, options) => {
      const cell = options[0]; // selection info

      const hot = hotRef.current?.hotInstance;
      if (!hot || !options?.[0]) return;

      const { start } = options[0];
      const row = start?.row;
      const col = start?.col;

      if (row == null || col == null) return;

      if (key === "addTags") {
        if (col === 2) {
          const colCount = hot.countCols(); // get total number of columns

          for (let i = 0; i < colCount; i++) {
            hot.setCellMeta(row, i, "className", "onholdGrp-cell");
          }

          hot.render(); // force re-render to apply the class
        } else if (col === 3) {
          const colCount = hot.countCols(); // get total number of columns

          for (let i = 0; i < colCount; i++) {
            hot.setCellMeta(row, i, "className", "onhold-cell");
          }

          hot.render(); // force re-render to apply the class
        } else {
          hot.setCellMeta(row, col, "className", "tagged-cell");
          hot.render();
        }

        handleTextFieldUpdate(row, col, key, "true");
      } else if (key === "removeTags") {
        if (col === 2 || col === 3) {
          const colCount = hot.countCols(); // get total number of columns

          for (let i = 0; i < colCount; i++) {
            hot.removeCellMeta(row, i, "className");
          }

          hot.render();
        } else {
          hot.removeCellMeta(row, col, "className");
          hot.render();
        }

        // hot.removeCellMeta(row, col, "className");
        // hot.render();
        handleTextFieldUpdate(row, col, key, "false");
      } else if (key === "commentsRemove") {
        hot.setCellMeta(row, col, "comment", undefined);
        hot.render(); // Already calling render here
        handleTextFieldUpdate(row, col, key, "");
      }
    },
    items: {
      commentsAddEdit: {
        name: "Add/Edit Comment",
        disabled(this: Handsontable) {
          const selection = this.getSelectedLast();
          if (!selection) return true;
          const [, col] = selection;
          const prop = this.colToProp(col);
          return !allowedCommentColumns.includes(prop as string);
        },
        callback(this: Handsontable) {
          // Add this callback to handle comment editing
          const selection = this.getSelectedLast();
          if (!selection) return;
          const [row, col] = selection;
          this.getPlugin("comments").showAtCell(row, col);
          this.render(); // Force refresh after showing comment editor
        },
      },
      commentsRemove: {
        name: "Delete Comment",
        disabled(this: Handsontable) {
          const selection = this.getSelectedLast();
          if (!selection) return true;
          const [row, col] = selection;
          const prop = this.colToProp(col);

          // First check if it's in allowed columns
          if (!allowedCommentColumns.includes(prop as string)) return true;

          // Then check if the cell actually has a comment
          const cellMeta = this.getCellMeta(row, col);
          return !cellMeta.comment || !cellMeta.comment.value;
        },
      },
      addTags: {
        // name: "Add Tags",
        name(this: Handsontable) {
          const selection = this.getSelectedLast();
          if (!selection) return "Add Tags";

          const [, col] = selection;
          const prop = this.colToProp(col);
          const tagName = columnTagMap[prop as string] || "Add Tags";

          return `${tagName}`;
        },
        disabled(this: Handsontable) {
          const selection = this.getSelectedLast();
          if (!selection) return true;

          const [row, col] = selection;
          const prop = this.colToProp(col);

          // Condition 1: Column must be in allowedTagColumns
          const isAllowedColumn = allowedTagColumns.includes(prop as string);

          // Condition 2: Row must not be on hold
          const rowData = tableData?.[row];
          const isRowOnHold = rowData?.["taggrouphold"] === "t";
          const isStoreRowOnHold = rowData?.["tagStoreHold"] === "t";
          return !isAllowedColumn || isRowOnHold || isStoreRowOnHold;
        },
      },
      removeTags: {
        name: "Remove Tags",
        disabled(this: Handsontable) {
          const selection = this.getSelectedLast();
          if (!selection) return true;
          const [row, col] = selection;
          const prop = this.colToProp(col);

          // First check if it's in allowed columns
          if (!allowedTagColumns.includes(prop as string)) return true;

          // Then check if the cell actually has a tag applied
          const cellMeta = this.getCellMeta(row, col);
          const className = cellMeta.className || "";

          // Special case for group/store hold which apply to entire row
          if (col === 2) {
            return !className.includes("onholdGrp-cell");
          } else if (col === 3) {
            return !className.includes("onhold-cell");
          } else {
            return !className.includes("tagged-cell");
          }
        },
      },
    },
  };

  const buttonClickCallback = () => {
    const hot = hotRef.current?.hotInstance;
    if (!hot) return;

    const data = hot.getData();
    const rawHeaders = hot.getColHeader();
    const headers = rawHeaders.map((header) =>
      String(header)
        .replace(/<[^>]*>?/gm, " ")
        .trim()
    );

    const cleanedData = data.map((row) =>
      row.map((cell: string) => {
        if (cell === "-- Select --") return "";

        if (/^\d{4}-\d{2}-\d{2}$/.test(cell)) {
          const date = new Date(cell);
          if (!isNaN(date.getTime())) {
            const mm = String(date.getMonth() + 1).padStart(2, "0");
            const dd = String(date.getDate()).padStart(2, "0");
            const yy = date.getFullYear();
            return `${mm}/${dd}/${yy}`;
          }
        }
        return cell;
      })
    );
    const worksheetData = [["Launch Report"], headers, ...cleanedData];

    const ws = XLSX.utils.aoa_to_sheet(worksheetData);

    ws["!merges"] = [
      {
        s: { r: 0, c: 0 },
        e: { r: 0, c: headers.length - 1 },
      },
    ];

    ws["A1"].s = {
      font: { bold: true, sz: 14 },
      alignment: { horizontal: "center", vertical: "center" },
    };

    ws["!cols"] = headers.map(() => ({ wch: 20 }));
    ws["!rows"] = [{ hpt: 28 }, { hpt: 20 }, ...data.map(() => ({ hpt: 18 }))];

    headers.forEach((header, col) => {
      const cellAddress = XLSX.utils.encode_cell({ r: 1, c: col });
      ws[cellAddress] = ws[cellAddress] || { t: "s", v: header };
      ws[cellAddress].s = {
        font: { bold: true },
        alignment: { horizontal: "center", vertical: "center" },
        fill: { patternType: "solid", fgColor: { rgb: "D9E1F2" } },
        border: {
          top: { style: "thin", color: { rgb: "000000" } },
          bottom: { style: "thin", color: { rgb: "000000" } },
          left: { style: "thin", color: { rgb: "000000" } },
          right: { style: "thin", color: { rgb: "000000" } },
        },
      };
    });

    for (let row = 0; row < data.length; row++) {
      for (let col = 0; col < headers.length; col++) {
        const meta = hot.getCellMeta(row, col);
        const meta1 = hot.getCellMeta(row, col + 1);

        let className = meta.className || "";
        if (col == 0) {
          className = meta1.className || "";
        }

        console.log("mmm---", className + "==" + row + "-----" + col);

        const cellRef = XLSX.utils.encode_cell({ r: row + 2, c: col });

        if (!ws[cellRef]) {
          ws[cellRef] = { t: "s", v: data[row][col] ?? "" };
        }

        ws[cellRef].s = ws[cellRef].s || {
          alignment: { vertical: "center", horizontal: "left" },
        };

        if (!ws[cellRef].s.fill) {
          if (className?.includes("tagged-cell")) {
            ws[cellRef].s.fill = {
              patternType: "solid",
              fgColor: { rgb: "C7CEEA" },
            };
          } else if (className?.includes("onholdGrp-cell")) {
            ws[cellRef].s.fill = {
              patternType: "solid",
              fgColor: { rgb: "A3E4D7" },
            };
          } else if (className?.includes("onhold-cell")) {
            ws[cellRef].s.fill = {
              patternType: "solid",
              fgColor: { rgb: "FAD7A0" },
            };
          }
        }

        if (meta.comment?.value && !meta.tag) {
          ws[cellRef].c = [{ t: meta.comment.value, a: "User" }];
          ws[cellRef].c.hidden = true;
        }
      }
    }

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Sheet1");

    const now = new Date();
    const pad = (n: number) => n.toString().padStart(2, "0");
    const ms = now.getMilliseconds().toString().padStart(3, "0");
    const timestamp = `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(
      now.getDate()
    )}_${pad(now.getHours())}-${pad(now.getMinutes())}-${pad(
      now.getSeconds()
    )}-${ms}`;

    const fileName = `launch_report_${selectedKey}_${timestamp}.xlsx`;
    XLSX.writeFile(wb, fileName);
  };

  const hideLaunchColumnsForKeys = [
    "onboardingTotal",
    "needsObMeeting",
    "dmsNotActive",
    "opsNotCategorized",
    "gridNotEntered",
    "modelsNotMapped",
    "matrixNotEntered",
    "usersNotCreated",
    "launchNotScheduled",
  ];

  // Keys for hiding 'OB Age'
  const hideObAgeForKeys = ["launchCompleted"];
  const visibleColumns = columns.filter((col) => {
    if (
      hideLaunchColumnsForKeys.includes(selectedKey) &&
      col.data === "totalDaysToLaunch"
    ) {
      return false;
    }
    if (hideObAgeForKeys.includes(selectedKey) && col.data === "obAge") {
      return false;
    }
    return true;
  });
  const legends = [
    { color: "#FAD7A0 ", label: "Store Hold" },
    { color: "#A3E4D7", label: "Group Hold" },
    { color: "#c7ceea", label: "Action / Follow Up" },
  ];
  return (
    <>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          marginBottom: "0px",
          width: "100%",
        }}>
        {/* Legends on the left */}
        <div style={{ display: "flex", gap: "10px", flexGrow: 1 }}>
          {legends.map((item) => (
            <div
              key={item.label}
              style={{ display: "flex", alignItems: "center" }}>
              <div
                style={{
                  width: 12,
                  height: 12,
                  backgroundColor: item.color,
                  marginRight: 5,
                }}></div>
              <span>{item.label}</span>
            </div>
          ))}
        </div>

        {/* Export Button on the far right */}
        {tableData.length > 0 && !showNoData && (
          <Tooltip title='Export to Excel'>
            <IconButton
              onClick={buttonClickCallback}
              color='primary'
              style={{
                marginLeft: "auto",
              }}>
              <FileDownloadOutlinedIcon />
            </IconButton>
          </Tooltip>
        )}
      </div>

      <>
        <div
          id='hot'
          style={{
            height: tableData.length > 20 ? "650px" : `${tableHeight}px`,
            overflowY: tableData.length > 20 ? "auto" : "hidden",
          }}>
          <HotTable
            ref={hotRef}
            data={tableData}
            columns={visibleColumns}
            colHeaders={true}
            rowHeights={22}
            width='100%'
            stretchH='all'
            fixedColumnsLeft={4}
            autoColumnSize={true}
            autoWrapRow={true}
            wordWrap={false}
            filters={true}
            dropdownMenu={{
              items: {
                filter_by_value: {
                  name: "Filter by value",
                },
                filter_action_bar: {
                  name: "Action bar",
                },
              },
            }}
            // afterFilter={handleFilter}
            contextMenu={contextMenu}
            comments={true}
            cell={mergedCells}
            afterInit={() => {
              isInitializing.current = false;
            }}
            afterSetCellMeta={(row, col, key, value) => {
              if (
                key === "comment" &&
                !isInitializing.current &&
                !isProgrammatic.current &&
                value?.value?.trim() !== undefined
              ) {
                handleTextFieldUpdate(row, col, key, value);
                hotRef.current?.hotInstance?.render(); // Force refresh after comment update
              }
            }}
            afterChange={(changes, source) => {
              changes?.forEach(([row, prop, oldValue, newValue]) => {
                if (
                  typeof prop === "string" &&
                  [
                    "tagGroupPay",
                    "onboardingCoordinator",
                    "salesperson",
                    "dms",
                    "coach",
                  ].includes(prop)
                ) {
                  handleSaveDropdown(row, prop as string, newValue, oldValue);
                } else {
                  handleSaveDatePicker(row, prop as string, newValue, oldValue);
                }
              });
            }}
            licenseKey='non-commercial-and-evaluation'
          />
        </div>
        {(showNoData || tableData.length === 0) && (
          <div
            style={{
              marginTop: 75,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: "1.2rem",
              fontWeight: "bold",
              color: "#666",
            }}>
            No data available
          </div>
        )}

        <div
          id='customScrollbar'
          ref={scrollRef}
          style={{
            marginTop: tableData.length <= 15 ? "20px" : "25px",
            display: tableData.length > 0 && !showNoData ? "block" : "none",
          }}>
          <div></div>
        </div>
      </>
    </>
  );
};

export default ExampleComponent;
