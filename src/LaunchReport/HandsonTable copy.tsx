import { FC } from "react";
import { HotTable } from "@handsontable/react";
import { registerAllModules } from "handsontable/registry";
import "handsontable/dist/handsontable.full.min.css";
import "handsontable/styles/ht-theme-main.css";

// Register all modules (includes Comments plugin)
registerAllModules();

const ExampleComponent: FC = (data) => {
  console.log("tableDttaaaa>>>>", data);
  return (
    <HotTable
      data={[
        {
          model: "Racing Socks",
          size: "S",
          price: 30,
          sellDate: "Oct 11, 2023",
          sellTime: "01:23 AM",
          inStock: false,
          color: "Black",
          email: "<EMAIL>",
        },
        {
          model: "HL Mountain Shirt",
          size: "XS",
          price: 1890.9,
          sellDate: "May 3, 2023",
          sellTime: "11:27 AM",
          inStock: false,
          color: "White",
          email: "<EMAIL>",
        },
        {
          model: "Cycling Cap",
          size: "L",
          price: 130.1,
          sellDate: "Mar 27, 2023",
          sellTime: "03:17 AM",
          inStock: true,
          color: "Green",
          email: "<EMAIL>",
        },
        {
          model: "Ski Jacket",
          size: "M",
          price: 59,
          sellDate: "Aug 28, 2023",
          sellTime: "08:01 AM",
          inStock: true,
          color: "Blue",
          email: "<EMAIL>",
        },
        {
          model: "HL Goggles",
          size: "XL",
          price: 279.99,
          sellDate: "Oct 2, 2023",
          sellTime: "13:23 AM",
          inStock: true,
          color: "Black",
          email: "<EMAIL>",
        },
      ]}
      columns={[
        {
          title: "Model<br>(text)",
          type: "text",
          data: "model",
        },
        {
          title: "Price<br>(numeric)",
          type: "numeric",
          data: "price",
          numericFormat: {
            pattern: "$ 0,0.00",
            culture: "en-US",
          },
        },
        {
          title: "Sold on<br>(date)",
          type: "date",
          data: "sellDate",
          //   dateFormat: 'MMM D, YYYY',
          correctFormat: true,
          className: "htRight",
        },
        {
          title: "Time<br>(time)",
          type: "time",
          data: "sellTime",
          timeFormat: "hh:mm A",
          correctFormat: true,
          className: "htRight",
        },
        {
          title: "In stock<br>(checkbox)",
          type: "checkbox",
          data: "inStock",
          className: "htCenter",
        },
        {
          title: "Size<br>(dropdown)",
          type: "dropdown",
          data: "size",
          source: ["XS", "S", "M", "L", "XL"],
          className: "htCenter",
        },
        {
          title: "Color<br>(autocomplete)",
          type: "autocomplete",
          data: "color",
          source: ["White", "Black", "Yellow", "Blue", "Green"],
          className: "htCenter",
        },
        {
          title: "Email<br>(password)",
          type: "password",
          data: "email",
        },
      ]}
      // 🔽 Enable these features:
      columnSorting={true}
      filters={true}
      dropdownMenu={true}
      contextMenu={true}
      comments={true}
      cell={[
        { row: 0, col: 0, comment: { value: "First item" } },
        { row: 1, col: 2, comment: { value: "Expensive item" } },
        { row: 3, col: 4, comment: { value: "In stock?" } },
      ]}
      height='auto'
      stretchH='all'
      autoWrapRow={true}
      autoWrapCol={true}
      licenseKey='non-commercial-and-evaluation'
    />
  );
};

export default ExampleComponent;
