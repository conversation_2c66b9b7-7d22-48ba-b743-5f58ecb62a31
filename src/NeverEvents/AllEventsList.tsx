import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Grid,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

import React from "react";
import DailyLoad from "../DailyLoad/dailyLoad";
import ErrorStatuses from "./ErrorStatuses";
import UsersList from "./UserRoles/userRoles";

/*
  This page is no longer in use. 
  The child pages have been separated independently to create the side menu.
*/

const AllEventsList = () => {
  return (
    <Box sx={{ padding: "30px 5px" ,border:'solid',width:'88%'}}>
      <Accordion
        style={{
          marginBottom: 16,
          backgroundColor: "#eaf1f6",
          border: "1px solid",
          borderColor: "#003d6b",
        }}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{ display: "flex" }}
        >
          <span style={{ color: "#003d6b", fontSize: "14px" }}>EDI Never Events</span>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container display={"contents"}>
            <DailyLoad />
          </Grid>
        </AccordionDetails>
      </Accordion>
      {/* Never Events */}
      <Accordion
        style={{
          marginBottom: 16,
          backgroundColor: "#eaf1f6",
          border: "1px solid",
          borderColor: "#003d6b",
        }}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{ display: "flex" }}
        >
          <span style={{ color: "#003d6b", fontSize: "14px" }}>FOPC Never Events</span>
        </AccordionSummary>
        <AccordionDetails>
        <Grid container display={"contents"}>
            <ErrorStatuses eventType="never"/>
          </Grid>
        </AccordionDetails>
      </Accordion>
      {/* Maintenance Events */}
      <Accordion
        style={{
          marginBottom: 16,
          backgroundColor: "#eaf1f6",
          border: "1px solid",
          borderColor: "#003d6b",
        }}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{ display: "flex" }}
        >
          <span style={{ color: "#003d6b", fontSize: "14px" }}>FOPC Maintenance Events</span>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container display={"contents"}>
            <ErrorStatuses eventType="maintenance"/>
          </Grid>
        </AccordionDetails>
      </Accordion>
      <Accordion
        style={{
          marginBottom: 16,
          backgroundColor: "#eaf1f6",
          border: "1px solid",
          borderColor: "#003d6b",
        }}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{ display: "flex" }}
        >
          <span style={{ color: "#003d6b", fontSize: "14px" }}>User Roles</span>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container display={"contents"}>
            <UsersList/>
          </Grid>
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default AllEventsList;
