import React from "react";
import { AgGridReact } from "ag-grid-react";
import { Box, Typography, Grid, Paper, Button, Divider } from "@mui/material";
import moment from "moment";
import { ColDef } from "ag-grid-community";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";

import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import "./LoginSummaryList.css";

interface LoginAttempt {
  [x: string]: any;
  user_name: string;
  last_login_date: string;
  unique_logins_per_day: string;
  enabled: boolean;
  first_name?: string;
  last_name?: string;
  user_creation_date: string;
}

interface AgGridLoginTableProps {
  clientName: string;
  loginAttempts: LoginAttempt[];
  startDate?: string;
  endDate?: string;
}

const LoginSummarySplitView: React.FC<AgGridLoginTableProps> = ({
  clientName,
  loginAttempts,
}) => {
  const leftGridData = loginAttempts.filter((user) => user.last_login_date);
  const rightGridData = loginAttempts.filter((user) => !user.last_login_date);

  const defaultColDef: ColDef = {
    sortable: true,
    resizable: false,
    filter: true,
    suppressMenu: true,
    flex: 1,
    suppressCellFlash: true,
  };

  const leftGridColumns: ColDef[] = [
    {
      headerName: "Name",
      tooltipField: "user_name",
      valueGetter: (params) => {
        const firstName = params.data?.first_name || "";
        const lastName = params.data?.last_name || "";
        return `${firstName} ${lastName}`.trim();
      },
      cellStyle: {
        textAlign: "left",
        padding: "0 8px",
        backgroundColor: "#f1f8e9",
        borderRight: "1px solid #e0e0e0",
      },
      headerClass: "activity-border-right",
      width: 182,
    },

    {
      headerName: "User Name",
      field: "user_name",
      tooltipField: "user_name",
      cellStyle: {
        textAlign: "left",
        padding: "0 8px",
        backgroundColor: "#f1f8e9",
        fontWeight: "normal",
        borderRight: "1px solid #e0e0e0",
      },
      headerClass: "activity-border-right",
      suppressMovable: true,
    },

    {
      headerName: "Last Login Date",
      field: "last_login_date",
      valueFormatter: ({ value }) =>
        value ? moment(value).format("MM/DD/YYYY") : "-",
      cellStyle: {
        textAlign: "center",
        padding: "0 8px",
        backgroundColor: "#f1f8e9",
      },
    },
  ];

  const rightGridColumns: ColDef[] = [
    {
      headerName: "Name",
      tooltipField: "user_name",
      valueGetter: (params) => {
        const firstName = params.data?.first_name || "";
        const lastName = params.data?.last_name || "";
        return `${firstName} ${lastName}`.trim();
      },
      cellStyle: {
        textAlign: "left",
        padding: "0 8px",
        backgroundColor: "#fff8e1",
        borderRight: "1px solid #e0e0e0",
      },
      width: 182,
      headerClass: "activity-border-right",
    },
    {
      headerName: "User Name",
      field: "user_name",
      tooltipField: "user_name",
      cellStyle: {
        textAlign: "left",
        padding: "0 8px",
        backgroundColor: "#fff8e1",
        fontWeight: "normal",
        borderRight: "1px solid #e0e0e0",
      },
    },
    {
      headerName: "Created Date",
      field: "user_creation_date",
      valueFormatter: ({ value }) =>
        value ? moment(value).format("MM/DD/YYYY") : "-",
      cellStyle: {
        textAlign: "center",
        padding: "0 8px",
        backgroundColor: "#fff8e1",
        fontWeight: "normal",
        borderRight: "1px solid #e0e0e0",
      },
    },
    {
      headerName: "Status",
      field: "enabled",
      cellRenderer: (params: { value: boolean }) => {
        const isEnabled = params.value === true;
        return isEnabled ? (
          "-"
        ) : (
          <Button
            variant="outlined"
            color="error"
            size="small"
            sx={{
              borderRadius: "20px",
              textTransform: "none",
              fontWeight: 600,
              maxHeight: "17px",
              fontSize: "10px",
              px: 0.8,
              py: 0.3,
            }}>
            <span
              style={{ fontSize: "15px", marginRight: "4px", lineHeight: 1 }}>
              !
            </span>
            Disabled
          </Button>
        );
      },
      cellStyle: {
        textAlign: "center",
        padding: "0 8px",
        backgroundColor: "#fff8e1",
      },
    },
  ];

  const exportToPDF = () => {
    const doc = new jsPDF({
      orientation: "portrait",
      unit: "pt",
      format: "A4",
    });

    const marginX = 40;
    let currentY = 40;

    // Main Title
    doc.setFontSize(18);
    doc.setFont("helvetica", "bold");
    doc.setTextColor("#1976d2");
    doc.text(
      `Login Summary: ${clientName}`,
      doc.internal.pageSize.getWidth() / 2,
      currentY,
      {
        align: "center",
      }
    );

    currentY += 30;

    // --- Users with Last Login ---
    doc.setFontSize(14);
    doc.setTextColor("#2e7d32");
    doc.setFont("helvetica", "bold");
    doc.text("Users with Last Login", marginX, currentY);

    currentY += 10;

    autoTable(doc, {
      startY: currentY + 5,
      margin: { left: marginX, right: marginX },
      head: [["Name", "User Name", "Last Login Date"]],
      body: leftGridData.map((row) => {
        const fullName = `${row.first_name || ""} ${
          row.last_name || ""
        }`.trim();
        return [
          fullName,
          row.user_name,
          row.last_login_date
            ? moment(row.last_login_date).format("MM/DD/YYYY")
            : "-",
        ];
      }),
      theme: "striped",
      headStyles: {
        fillColor: [200, 230, 201], // green
        textColor: "#1b5e20",
        fontStyle: "bold",
      },
      styles: {
        fontSize: 10,
        cellPadding: 5,
        valign: "middle",
        halign: "left",
      },
      didDrawPage: (data) => {
        if (data.cursor) {
          currentY = data.cursor.y + 30;
        }
      },
    });

    // --- Users with No Login ---
    doc.setFontSize(14);
    doc.setTextColor("#ef6c00");
    doc.setFont("helvetica", "bold");
    doc.text("Users with No Login", marginX, currentY);

    currentY += 10;

    autoTable(doc, {
      startY: currentY + 5,
      margin: { left: marginX, right: marginX },
      head: [["Name", "User Name", "Created Date", "Status"]],
      body: rightGridData.map((row) => {
        const fullName = `${row.first_name || ""} ${
          row.last_name || ""
        }`.trim();
        const status = row.enabled == false ? "Disabled ❗" : "-";
        const createdDate = row.user_creation_date
          ? moment(row.user_creation_date).format("MM/DD/YYYY")
          : "-";
        return [fullName, row.user_name, createdDate, status];
      }),
      theme: "striped",
      headStyles: {
        fillColor: [255, 224, 178], // orange
        textColor: "#ef6c00",
        fontStyle: "bold",
      },
      styles: {
        fontSize: 10,
        cellPadding: 5,
        valign: "middle",
        halign: "left",
      },
    });

    doc.save(`Login_Summary_${clientName}.pdf`);
  };

  return (
    <Box
      sx={{
        pt: 1,
        pb: 2,
        px: 3,
        backgroundColor: "#f5f5f5",
        borderRadius: 3,
      }}>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2} // increased margin bottom here
      >
        <Typography fontSize={18} color="primary" fontWeight="bold">
          📊 {clientName}
        </Typography>

        {loginAttempts?.length > 0 && (
          <Button
            variant="contained"
            startIcon={<PictureAsPdfIcon />}
            size="small"
            onClick={exportToPDF}
            sx={{
              textTransform: "none",
              borderRadius: 2,
              fontWeight: 600,
              boxShadow: "0 2px 8px rgba(25, 118, 210, 0.3)",
              background: "#1976d2",
              "&:hover": {
                background: "#1565c0",
              },
            }}>
            Download PDF
          </Button>
        )}
      </Box>
      <Divider sx={{ mb: 4 }} /> {/* increased mb */}
      <Grid container spacing={4}>
        {" "}
        {/* increased spacing */}
        <Grid item xs={12} md={6} sx={{ mb: { xs: 3, md: 0 } }}>
          {" "}
          {/* added bottom margin on small */}
          <Paper
            elevation={4}
            sx={{
              p: 3, // increased padding
              backgroundColor: "#f1f8e9",
              borderRadius: 2,
              height: "100%",
            }}>
            <Typography
              variant="subtitle1"
              fontWeight={600}
              color="success.main"
              mb={2} // added margin bottom
            >
              ✅ Users with Last Login
            </Typography>
            <Box
              className="ag-theme-alpine"
              sx={{
                height: 370,
                width: "100%",
                fontSize: 13,
                "& .ag-root-wrapper": { borderRadius: 1 },
                "& .ag-header": {
                  backgroundColor: "#c5e1a5",
                },
                "& .ag-row:hover": {
                  backgroundColor: "#dcedc8 !important",
                },
              }}>
              <AgGridReact
                rowHeight={32}
                rowData={leftGridData}
                columnDefs={leftGridColumns}
                defaultColDef={defaultColDef}
                onGridReady={(params) => params.api.sizeColumnsToFit()}
              />
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6} sx={{ mb: { xs: 3, md: 0 } }}>
          {" "}
          {/* added bottom margin on small */}
          <Paper
            elevation={4}
            sx={{
              p: 3, // increased padding
              backgroundColor: "#fff8e1",
              borderRadius: 2,
              height: "100%",
            }}>
            <Typography
              variant="subtitle1"
              fontWeight={600}
              color="warning.main"
              mb={2} // added margin bottom
            >
              ⚠️ Users with No Login
            </Typography>
            <Box
              className="ag-theme-alpine"
              sx={{
                height: 370,
                width: "100%",
                fontSize: 13,
                "& .ag-root-wrapper": { borderRadius: 1 },
                "& .ag-header": {
                  backgroundColor: "#ffe082",
                },
                "& .ag-row:hover": {
                  backgroundColor: "#ffecb3 !important",
                },
              }}>
              <AgGridReact
                rowHeight={32}
                rowData={rightGridData}
                columnDefs={rightGridColumns}
                defaultColDef={defaultColDef}
                onGridReady={(params) => params.api.sizeColumnsToFit()}
              />
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default LoginSummarySplitView;
