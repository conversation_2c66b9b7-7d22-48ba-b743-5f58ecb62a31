import { useEffect } from "react";
import { But<PERSON>, DatePicker, Form, Space } from "antd";
import { useRecordContext } from "react-admin";
import EmailReportGridDefs from "../../../src/NeverEvents/EmailReport/EmailReportGridDefs";
import dayjs from "dayjs";
import { AgGridReact } from "ag-grid-react";
import { Box, Typography } from "@mui/material";
import { RangePickerProps } from "antd/es/date-picker";
import { Constants } from "../../utils/constants";

const { RangePicker } = DatePicker;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

export default function EmailReport(props?: any) {
  const { spec } = props;
  const record = useRecordContext();
  const {
    emailReportList,
    columnDefs,
    defaultColDef,
    onGridReady,
    selectedDateRange,
    setSelectedDateRange,
    setShowDateSaveButton,
    getEmailList,
    getEmailListNew,
  } = EmailReportGridDefs({
    spec,
    storeId: record?.storeId,
    tenantId: record?.tenantId,
  });

  const onFinish = () => {
    updateDateRange();
  };

  const updateDateRange = () => {
    getEmailListNew();
    setShowDateSaveButton(false);
  };

  // const onDateChange: RangePickerProps["onChange"] = (
  //   dates: any,
  //   dateStrings: [string, string]
  // ) => {
  //   if (dates && dates?.length === 2) {
  //     const startDate = dates[1].endOf("day");
  //     const endDate = dates[1].endOf("day");
  //     setSelectedDateRange([startDate, endDate]);
  //     setShowDateSaveButton(true);
  //   } else {
  //     alert("kk");
  //     setSelectedDateRange(dates);
  //     setShowDateSaveButton(true);
  //   }
  // };

  useEffect(() => {
    const today = dayjs();
    setSelectedDateRange([today.startOf("day"), today.endOf("day")]);
  }, []);

  return (
    <Space direction="vertical" style={{ marginTop: "16px" }}>
      <Box display="flex" alignItems="center">
        <Form
          {...formItemLayout}
          onFinish={onFinish}
          initialValues={{
            Date: dayjs(), // default to today
          }}
          style={{
            maxWidth: 600,
            display: "flex",
            gap: "10px",
          }} // Added gap for spacing
        >
          <Form.Item
            label={
              <span style={{ color: "#003D6B", fontWeight: "bold" }}>Date</span>
            }
            name="Date"
            rules={[{ required: true }]}
            style={{ marginBottom: 30 }} // Removes extra space below item
          >
            <DatePicker
              format="MM/DD/YYYY"
              inputReadOnly={true}
              style={{ width: 180 }} // Set user-friendly width here
              onChange={(date) => {
                if (date) {
                  const start = date.clone().endOf("day");
                  const end = date.clone().endOf("day");
                  setSelectedDateRange([start, end]);
                  setShowDateSaveButton(true);
                }
              }}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0 }}>
            <Button
              style={{ backgroundColor: "#1976D2", borderColor: "#1976D2" }}
              type="primary"
              size="middle"
              htmlType="submit">
              Apply
            </Button>
          </Form.Item>
        </Form>
      </Box>
      <Box>
        <div
          className={Constants.ag_grid_theme}
          style={{
            height: "83vh",
            width:
              spec === "storeSpecific"
                ? "81vw"
                : spec === "tenantSpecific"
                ? "83vw"
                : "85vw",
            marginTop: "-15px",
          }}>
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            rowData={emailReportList}
            defaultColDef={defaultColDef}
            rowSelection="single"
            onGridReady={(params: any) => onGridReady(params)}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            stopEditingWhenCellsLoseFocus={true}
            pagination={true}
            paginationPageSize={100}
          />
        </div>
      </Box>
    </Space>
  );
}
