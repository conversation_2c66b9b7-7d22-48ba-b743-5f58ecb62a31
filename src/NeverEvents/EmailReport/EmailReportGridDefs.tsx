import { ColDef } from "ag-grid-community";
import moment from "moment";
import React, { useCallback } from "react";
import { GetEmailList } from "../../service/emailReportList";
import dayjs from "dayjs";

const EmailReportGridDefs = (props: any) => {
  const { spec, storeId, tenantId } = props;
  const [emailReportList, setEmailReportList] = React.useState([]);
  const [selectedDateRange, setSelectedDateRange] = React.useState<any>([
    dayjs().subtract(1, "week"),
    dayjs(),
  ]);
  const [showDateSaveButton, setShowDateSaveButton] = React.useState(false);
  const getEmailList = () => {
    const input = {
      startDate: selectedDateRange[1].toISOString().split("T")[0],
      endDate: selectedDateRange[1].toISOString().split("T")[0],
    };
    GetEmailList(input).then((response: any) => {
      const parsedData = JSON.parse(response)?.items || [];
      setEmailReportList(parsedData);
    });
  };
  const getEmailListNew = () => {
    gridApiRef.current?.showLoadingOverlay();
    getEmailList();
  };
  const gridApiRef = React.useRef<any>(null);
  const onGridReady = useCallback((params: any) => {
    gridApiRef.current = params.api;
    params?.api?.showLoadingOverlay();
    getEmailList();
  }, []);
  const columnDefs = [
    {
      headerName: "Email To",
      field: "to",
      cellStyle: { textAlign: "left" },
      flex: 2,
    },
    {
      headerName: "Email Date",
      field: "date",
      flex: 1.5,
      cellStyle: { textAlign: "left" },
      valueFormatter: (params: any) => {
        if (!params.value) return "";

        const date = new Date(params.value);

        const mm = String(date.getMonth() + 1).padStart(2, "0");
        const dd = String(date.getDate()).padStart(2, "0");
        const yy = String(date.getFullYear()).slice(-2);

        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");

        return `${mm}/${dd}/${yy} ${hours}:${minutes}:${seconds}`;
      },
    },
    {
      headerName: "Event",
      field: "event",
      flex: 1.5,
    },
    {
      headerName: "Subject",
      field: "subject",
      flex: 3,
    },
    {
      headerName: "File Name",
      field: "filename",
      flex: 3.5,
    },
    {
      headerName: "Delivery Status Message",
      field: "delivery_status_message",
      flex: 2.5,
    },
  ];
  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { fontSize: "12px", textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      flex: 3,
      filterParams: {
        applyMiniFilterWhileTyping: true,
      },
    };
  }, []);
  return {
    defaultColDef,
    onGridReady,
    emailReportList,
    selectedDateRange,
    setSelectedDateRange,
    showDateSaveButton,
    setShowDateSaveButton,
    getEmailList,
    columnDefs,
    getEmailListNew,
  };
};

export default EmailReportGridDefs;
