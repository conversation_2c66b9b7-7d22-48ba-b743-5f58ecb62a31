import React from "react";
import { Link as MuiLink } from "@mui/material";
import DataFetchQueries from "../../service/dataFetchQueries";
import { ColDef } from "ag-grid-community";
import moment from "moment";
import { ICellRendererParams } from "@ag-grid-enterprise/all-modules";

const DataAsOfGridDefs = () => {
  const { GetDataAsOf } = DataFetchQueries;
  const [allLoadList, setAllLoadList] = React.useState<any>([]);
  const statusFont = (params: any) => {
    const dateValue = moment(params.value);
    const today = moment();
    const diffDays = today.diff(dateValue, "days");
    if (diffDays <= 2) {
      return { fontWeight: "bold", color: "green" };
    } else if (diffDays <= 4) {
      return { fontWeight: "bold", color: "orange" };
    } else {
      return { fontWeight: "bold", color: "red" };
    }
  };

  const getDataAsOfList = () => {
    GetDataAsOf().then((res: any) => {
      setAllLoadList(res);
    });
  };

  const onGridReady = (params: any) => {
    params?.api?.showLoadingOverlay();
    getDataAsOfList();
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "Tenant Name",
      field: "tenantName",
      cellStyle: { textAlign: "left" },
      flex: 4,
    },
    {
      headerName: "Store Name",
      field: "storeName",
      flex: 5,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "DMS",
      field: "dms",
      flex: 1,
    },
    {
      headerName: "Data As Of",
      field: "value",
      cellStyle: statusFont,
      flex: 2,
      valueFormatter: (params: any) =>
        params.value ? moment(params.value).format("MM/DD/YYYY") : params.value,
    },
    {
      headerName: "Domain",
      field: "subDomain",
      flex: 4,
      cellRenderer: (params: any) => {
        if (!params.data?.subDomain) return null;
        return (
          <MuiLink
            href={`https://${params.data.subDomain}/`}
            target="_blank"
            rel="noopener noreferrer">
            {params.data.subDomain}
          </MuiLink>
        );
      },
    },
  ];
  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      flex: 3,
      filterParams: {
        applyMiniFilterWhileTyping: true,
      },
    };
  }, []);
  return {
    columnDefs,
    defaultColDef,
    onGridReady,
    allLoadList,
  };
};

export default DataAsOfGridDefs;
