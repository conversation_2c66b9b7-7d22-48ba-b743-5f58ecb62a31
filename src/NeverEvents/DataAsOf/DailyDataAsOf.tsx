import { Box } from "@mui/material";
import React from "react";
import { AgGridReact } from "ag-grid-react";
import { Constants } from "../../utils/constants";
import DataAsOfGridDefs from "./DataAsOfGridDefs";

const DailyDataAsOf = () => {
  const {
    columnDefs,
    defaultColDef,
    onGridReady,
    allLoadList,
  } = DataAsOfGridDefs();

  return (
    <Box sx={{ paddingX: "10px", width: "100%" ,marginTop: "20px" }}>
      <div
        className={Constants.ag_grid_theme}
        style={{ height: "90vh", width: "100%" }}
      >
        <AgGridReact
          columnDefs={columnDefs}
          editType="fullRow"
          rowData={allLoadList}
          defaultColDef={defaultColDef}
          rowSelection="single"
          onGridReady={(params: any) => onGridReady(params)}
          singleClickEdit={true}
          suppressColumnVirtualisation={true}
          suppressChangeDetection={true}
          stopEditingWhenCellsLoseFocus={true}
        />
      </div>
    </Box>
  );
};

export default DailyDataAsOf;