import { useEffect, useState } from "react";
import { Button, DatePicker, Form, Space } from "antd";
import { useRecordContext } from "react-admin";
import EmailReportGridDefs from "../../../src/NeverEvents/RO13MonthReport/RO13MonthReportGridDefs";
import dayjs from "dayjs";
import { AgGridReact } from "ag-grid-react";
import { Box, Typography } from "@mui/material";
import { RangePickerProps } from "antd/es/date-picker";
import { Constants } from "../../utils/constants";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css"; // or your chosen theme

const { RangePicker } = DatePicker;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

export default function RO13MonthReport(props?: any) {
  const { spec } = props;
  const record = useRecordContext();
  const [gridApi, setGridApi] = useState<any>(null);

  const {
    reportList,
    columnDefs,
    defaultColDef,
    onGridReady,
    setShowDateSaveButton,
    getRO13MonthCount,
    getEmailListNew,
  } = EmailReportGridDefs({
    spec,
    storeId: record?.storeId,
    tenantId: record?.tenantId,
  });

  const onFinish = () => {
    updateDateRange();
  };

  const updateDateRange = () => {
    getEmailListNew();
    setShowDateSaveButton(false);
  };

  const handleExport = () => {
    if (gridApi) {
      gridApi.exportDataAsExcel({
        prependContent: [
          [
            {
              data: { value: "RO13 Month Report", type: "String" },
              mergeAcross: columnDefs.length - 1,
              styleId: "header",
            },
          ],
          [],
        ],
        sheetName: "RO13 Month Report",
      });
    }
  };

  return (
    <Space
      direction="vertical"
      style={{ marginTop: "16px", width: "85vw !important" }}>
      {/* Header with Export Button */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        paddingRight={2}>
        <Typography variant="h6"></Typography>
        <Button
          type="primary"
          icon={<FileDownloadIcon />}
          style={{ backgroundColor: "#1976d2", color: "#fff" }}
          onClick={handleExport}>
          Export
        </Button>
      </Box>

      {/* AG Grid */}
      <Box>
        <div
          className={Constants.ag_grid_theme} // e.g., "ag-theme-alpine"
          style={{
            height: "83vh",
            width: "85vw",
          }}>
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            rowData={reportList}
            defaultColDef={defaultColDef}
            rowSelection="single"
            onGridReady={(params: any) => {
              onGridReady(params);
              setGridApi(params.api);
            }}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            stopEditingWhenCellsLoseFocus={true}
            pagination={true}
            paginationPageSize={100}
            excelStyles={[
              {
                id: "header",
                font: { size: 12, bold: true },
                alignment: { horizontal: "Center" },
              },
            ]}
          />
        </div>
      </Box>
    </Space>
  );
}
