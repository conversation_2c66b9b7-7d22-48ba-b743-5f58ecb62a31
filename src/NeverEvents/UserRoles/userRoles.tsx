import { Box, FormControl, InputLabel, MenuItem, Select } from "@mui/material";
import React, { useEffect } from "react";
import { AgGridReact } from "ag-grid-react";
import UsersListGridDefs from "./userRolesGridRefs";
import { Constants } from "../../utils/constants";

const UsersList = () => {
  const {
    columnDefs,
    defaultColDef,
    onGridReady,
    usersList,
    sideBar,
    selectedModule,
    handleTenantFilter,
    fetchAllTenantsList,
    allTenants,
  } = UsersListGridDefs();
  useEffect(() => {
    fetchAllTenantsList();
  }, []);
  return (
    <Box sx={{ paddingX: "10px", width: "100%", marginTop: "16px" }}>
      <Box sx={{ marginBottom: "16px", width: "100%" }}>
        <FormControl variant="outlined" 
        sx={{ width: "20%", margin: 0, mr: 2 }}>
          <InputLabel id="demo-simple-select-label" sx={{ fontSize: "14px" }}>
            Tenant*
          </InputLabel>
          <Select
            labelId="demo-simple-select-label"
            id="demo-simple-select"
            value={selectedModule}
            onChange={handleTenantFilter}
            label="Tenant*"
            sx={{ height: "40px", fontSize: "14px" }}
            // displayEmpty // Ensures placeholder behavior when no value is selected
          >
            <MenuItem value="" disabled>
              Select a Tenant
            </MenuItem>
            {allTenants.map((item: any) => (
              <MenuItem key={item.realmName} value={item.realmName}>
                {item.tenantName}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
      </Box>
      <Box sx={{ marginBottom: "16px", width: "100%" }}>
        <div
          className={Constants.ag_grid_theme}
          style={{ height: "85vh", width: "100%" }}
        >
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            rowData={usersList}
            defaultColDef={defaultColDef}
            rowSelection="single"
            onGridReady={(params: any) => onGridReady(params)}
            // rowHeight={30}
            sideBar={sideBar}
            // sideBar={"columns"}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            stopEditingWhenCellsLoseFocus={true}
            overlayNoRowsTemplate={"Select a tenant to view users list"}
          />
        </div>
      </Box>
    </Box>
  );
};

export default UsersList;
