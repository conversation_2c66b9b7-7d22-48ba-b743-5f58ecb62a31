import FetchConfig from "../fetchConfig";
import { IFetchConfigReturn } from "../../types";

const InsertUpdateGridType = (input: any) => {
  const variables = {
    inCreatedUser: localStorage.getItem("userEmail"),
    inNewGridName: input.inNewGridName,
    inIsDefaultGridName: input.inIsDefaultGridName,
    inStoreId: input.inStoreId,
    inTenantId: input.inTenantId,
    inActivity: input.inActivity,
    inOldGridName: input.inOldGridName
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.Realm,
    query: `mutation InsertUpdateGridType
    (
     $inActivity: String!,
     $inCreatedUser: String!, 
     $inNewGridName: String!, 
     $inOldGridName: String!, 
     $inIsDefaultGridName: Boolean!, 
     $inStoreId: String!, 
     $inTenantId: String! 
     ) {
        statelessCcPhysicalRwInsertOrUpdateLaborGridTypeMasterDetails(
          input: {
          inActivity: $inActivity, 
          inCreatedUser: $inCreatedUser, 
          inNewGridName: $inNewGridName, 
          inOldGridName: $inOldGridName, 
          inIsDefaultGridName: $inIsDefaultGridName, 
          inStoreId: $inStoreId, 
          inTenantId: $inTenantId}
        ) {
          results {
            msg
            status
          }
        }
      }
      `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwInsertOrUpdateLaborGridTypeMasterDetails
      .results;
  });
};

const LaborGridMutations = {
  InsertUpdateGridType,
};

export default LaborGridMutations;
