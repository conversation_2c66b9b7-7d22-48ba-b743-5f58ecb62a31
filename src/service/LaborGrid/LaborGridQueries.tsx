import FetchConfig from "../fetchConfig";
import { IFetchConfigReturn } from "../../types";

const GetGridTypeMaster = (storeId: string, realm: string) => {
    const variables = {
      storeId: storeId,
    };
    return FetchConfig({
      anonymous: false,
      Realm: realm,
      query: `query GetGridTypeMaster ($storeId: String!){
        statelessCcPhysicalRwLaborGridTypeMasterDetails(
          condition: {storeId: $storeId}
        ) {
          nodes {
            value
            isDefaultGridType
            gridCount
          }
        }
      }`,
      variables: variables,
    }).then((response: IFetchConfigReturn) => {
      return response.data.statelessCcPhysicalRwLaborGridTypeMasterDetails.nodes;
    });
  };


const LaborGridQueries = {
  GetGridTypeMaster,
};

export default LaborGridQueries;
