import FetchConfig from "./fetchConfig";
import { IEmailQueryInput, IFetchConfigReturn } from "../types";

const userEmailId = localStorage.getItem("userEmail");
export const DeleteTenant = (
  pAction: string,
  tenantid: string,
  tenantname: string,
  inDeletedDate?: any
) => {
  const variables = {
    pAction: pAction,
    tenantid: tenantid,
    tenantname: tenantname,
    inDeletedDate: inDeletedDate,
    userid: userEmailId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoInsertOrUpdateTenantMaster(
      $pAction: String!
      $tenantid: String!
      $tenantname: String!
      $userid: String!
      $inDeletedDate: Date
    ) {
      statelessServiceBzoInsertOrUpdateTenantMaster(
        input: {
          pAction: $pAction
          inDeletedDate: $inDeletedDate
          tenantid: $tenantid
          tenantname: $tenantname
          userid: $userid
        }
      ) {
        string
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertOrUpdateTenantMaster;
  });
};

export const MutateDms = (pAction: string, pDms: string, pDmsImg: string) => {
  const variables = {
    pAction: pAction,
    pDms: pDms,
    pDmsImg: pDmsImg,
    userid: userEmailId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoInsertOrUpdateDmsMaster(
      $pAction: String!
      $pDms: String!
      $pDmsImg: String!
      $userid: String!
    ) {
      statelessServiceBzoInsertOrUpdateDmsMaster(
        input: {
          pAction: $pAction
          pDms: $pDms
          pDmsImg: $pDmsImg
          userid: $userid
        }
      ) {
        string
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertOrUpdateDmsMaster;
  });
};

export const MutateGroupingMaster = (
  pAction: string,
  groupdesc: string,
  groupid: number,
  storeid: string[],
  groupname: string,
  tenantId: string
) => {
  const variables = {
    pAction: pAction,
    groupdesc: groupdesc,
    groupid: groupid,
    storeid: storeid,
    groupname: groupname,
    inTenantId: tenantId,
    userid: userEmailId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoInsertOrUpdateGroupingMaster(
      $pAction: String!
      $groupdesc: String!
      $groupid: Int
      $storeid: [String]
      $groupname: String!
      $userid: String!
      $inTenantId: String!
    ) {
      statelessServiceBzoInsertOrUpdateGroupingMaster(
        input: {
          pAction: $pAction
          groupdesc: $groupdesc
          groupid: $groupid
          storeid: $storeid
          groupname: $groupname
          userid: $userid
          inTenantId: $inTenantId
        }
      ) {
        string
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertOrUpdateGroupingMaster;
  });
};

const InsertWorkPackage = (id: string) => {
  const variables = {
    inId: id,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation InsertWorkPackage($inId: String!) {
      statelessServiceBzoInsertOrUpdateWorkPackage(input: {inId: $inId}) {
        results {
          msg
          status
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertOrUpdateWorkPackage
      .results[0];
  });
};
const InsertWorkPackageHierarchies = (id: string) => {
  const variables = {
    inId: id,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation InsertWorkPackage($inId: String!) {
      statelessServiceBzoInsertOrUpdateWorkPackageHierarchies(input: {inId: $inId}) {
        results {
          msg
          status
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data;
  });
};

export const InsertOrUpdateTenant = (input: any) => {
  const variables = {
    pAction: input.pAction,
    tenantid: input.tenantId,
    tenantname: input.tenantName,
    tenantdesc: input.tenantDesc,
    subdomain: input.subDomain,
    pTenantImg: input.tenantImg,
    agreementDate: input.agreementDate,
    userid: userEmailId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoInsertOrUpdateTenantMaster(
      $pAction: String!
      $tenantid: String!
      $tenantname: String!
      $tenantdesc: String!
      $subdomain: String
      $pTenantImg: String
      $agreementDate: Date
      $userid: String!
    ) {
      statelessServiceBzoInsertOrUpdateTenantMaster(
        input: {
          pAction: $pAction
          tenantid: $tenantid
          tenantname: $tenantname
          tenantdesc: $tenantdesc
          subdomain: $subdomain
          pTenantImg: $pTenantImg
          pAgreementDate: $agreementDate
          userid: $userid 
        }
      ) {
        string
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertOrUpdateTenantMaster;
  });
};

export const OpcodeMutation = (
  pVal: JSON,
  pTenantId: string,
  pStoreId: string
) => {
  const variables = {
    pVal: JSON.stringify(pVal),
    pUserid: userEmailId,
    pTenantId: pTenantId,
    pStoreId: pStoreId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoInsertOrUpdateOnboardingOpcodeCategorization(
      $pVal: JSON!
      $pUserid: String!
      $pTenantId: String!
      $pStoreId: String!
    ) {
      statelessServiceBzoInsertOrUpdateOnboardingOpcodeCategorization(
        input: {
          pVal: $pVal
          pUserid: $pUserid
          pTenantId: $pTenantId
          pStoreId: $pStoreId
        }
      ) {
        string
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data
      .statelessServiceBzoInsertOrUpdateOnboardingOpcodeCategorization;
  });
};

export const InsertStore = (input: any) => {
  const variables = {
    pDms: input.dms,
    storename: input.storeName.trim(),
    storedesc: input.storeDesc,
    storeid: input.storeId,
    tenantid: input.tenantId,
    dealeraddress: input.dealerAddress,
    pManufacturer: input.manufacturer,
    pWebsite: input.website,
    sortorder: input.sortOrder,
    companyNumber: input.companyNumber,
    dealerId: input.dealerId,
    enterpriseCode: input.enterpriseCode,
    serverName: input.serverName,
    stateCode: input.stateCode,
    storelauncheddate: input.storeLaunchedDate,
    billingdate: input.billingDate,
    inSfStoreId: input.inSfStoreId,
    inSubscriptionId: null,
    inRealmName: input.realmName,
    userid: "",
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation InsertStore(
  $pDms: String!
  $storename: String!
  $storedesc: String!
  $storeid: String!
  $tenantid: String!
  $dealeraddress: String!
  $pManufacturer: String!
  $pWebsite: String!
  $sortorder: Int!
  $companyNumber: String
  $dealerId: String
  $enterpriseCode: String
  $serverName: String
  $stateCode: String
  $storelauncheddate: Date
  $billingdate: Date
  $inSfStoreId: String
  $inSubscriptionId: String
  $inRealmName: String
  $userid: String!
) {
  statelessServiceBzoInsertOrUpdateStoreMaster(
    input: {
      pDms: $pDms
      storename: $storename
      storedesc: $storedesc
      storeid: $storeid
      tenantid: $tenantid
      pManufacturer: $pManufacturer
      sortorder: $sortorder
      dealeraddress: $dealeraddress
      pWebsite: $pWebsite
      companynumber: $companyNumber
      dealerid: $dealerId
      enterprisecode: $enterpriseCode
      servername: $serverName
      statecode: $stateCode
      storelauncheddate: $storelauncheddate
      billingdate: $billingdate
      inSfStoreId: $inSfStoreId
      inSubscriptionId: $inSubscriptionId
      inRealmName: $inRealmName
      userid: $userid
    }
  ) {
    string
  }
}
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertOrUpdateStoreMaster.string;
  });
};

export const InsertUpdateMultipleDmsDetails = (input: any) => {
  const variables = {
    pDms: input.dms,
    storename: input.storeName.trim(),
    storedesc: input.storeDesc,
    storeid: input.storeId,
    tenantid: input.tenantId,
    dealeraddress: input.dealerAddress,
    pManufacturer: input.manufacturer,
    pWebsite: input.website,
    sortorder: input.sortOrder,
    userid: userEmailId,
    companyNumber: input.companyNumber,
    dealerId: input.dealerId,
    enterpriseCode: input.enterpriseCode,
    serverName: input.serverName,
    stateCode: input.stateCode,
    storelauncheddate: input.storeLaunchedDate,
    billingdate: input.billingDate,
    inSfStoreId: input.inSfStoreId,
    inSubscriptionId: null,
    inEffectiveDate: input.effectiveDate,
    pAction: input.action,
    inRealmName: input.realmName,
    sId: input.action == "insert" ? null : input.id,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation InsertUpdateMultipleDmsDetails(
  $pDms: String!
  $storename: String!
  $storedesc: String
  $storeid: String!
  $tenantid: String!
  $dealeraddress: String
  $pManufacturer: String
  $pWebsite: String
  $sortorder: Int!
  $userid: String!
  $companyNumber: String
  $dealerId: String
  $enterpriseCode: String
  $serverName: String
  $stateCode: String
  $storelauncheddate: Date
  $billingdate: Date
  $inSfStoreId: String
  $inSubscriptionId: String
  $inEffectiveDate: Date
  $pAction: String!,
    $sId: Int,
    $inRealmName: String
) {
  statelessServiceBzoInsertOrUpdateStoreWithMultipleDms(
    input: {
      pDms: $pDms
      storename: $storename
      storedesc: $storedesc
      storeid: $storeid
      tenantid: $tenantid
      userid: $userid
      pManufacturer: $pManufacturer
      sortorder: $sortorder
      dealeraddress: $dealeraddress
      pWebsite: $pWebsite
      companynumber: $companyNumber
      dealerid: $dealerId
      enterprisecode: $enterpriseCode
      servername: $serverName
      statecode: $stateCode
      storelauncheddate: $storelauncheddate
      billingdate: $billingdate
      inSfStoreId: $inSfStoreId
      inSubscriptionId: $inSubscriptionId
      inEffectiveDate: $inEffectiveDate
      pAction: $pAction,
      sId: $sId,
      inRealmName: $inRealmName
    }
  ) {
    results{
      statusCode
      statusMsg
    }
  }
}
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertOrUpdateStoreWithMultipleDms
      .results[0];
  });
};

export const StoreOnboardingEmail = (input: IEmailQueryInput) => {
  const variables = {
    pDms: input.pDms,
    pNewStore: input.pNewStore,
    pStore: input.pStore,
    pStoreId: input.pStoreId,
    pTenant: input.pTenant,
    pTenantId: input.pTenantId,
    pUserid: userEmailId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoStoreOnboardingEmail ($pDms: String!, $pNewStore: Int!, $pStore: String!, $pStoreId: String!, $pTenant: String,$pTenantId: String, $pUserid: String!) {
      statelessServiceBzoStoreOnboardingEmail(
        input: {pDms: $pDms, pNewStore: $pNewStore, pStore: $pStore,pStoreId: $pStoreId, pTenant: $pTenant,pTenantId: $pTenantId, pUserid: $pUserid}
      ) {
        string
      }
    }
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response;
  });
};

export const SetChecklistStatus = (input: any) => {
  const variables = {
    pProcess: input.pProcess,
    pStatus: input.pStatus,
    pChecklist: input.pChecklist,
    pStore: input.pStore,
    pTenant: input.pTenant,
    pUserid: userEmailId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoSetOnboardingChecklist($pProcess: String!, $pTenant: String!, $pStore: String!, $pUserid: String!, $pChecklist: String!, $pStatus: String!) {
      statelessServiceBzoSetOnboardingChecklist(
        input: {pProcess: $pProcess, pTenant:$pTenant, pStore: $pStore, pUserid: $pUserid, pChecklist: $pChecklist, pStatus: $pStatus}
      ) {
        clientMutationId
        string
      }
    }
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoSetOnboardingChecklist.string;
  });
};

const UpdateAdvisorDetails = (input: any) => {
  const variables = {
    advisor: input.serviceadvisor,
    pCategorized: input.active == null ? 0 : 1,
    statusval: input.active,
    nickName: input.nickname,
    userId: userEmailId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.realm,
    storeid: input.storeId,
    query: `mutation updateServiceAdvisor(
      $advisor: String
      $pCategorized: Int
      $statusval: BitString
      $nickName: String
    ) {
      statelessCcPhysicalRwGetUpdateServiceAdvisorStatus(
        input: {
          advisor: $advisor
          statusval: $statusval
          pCategorized: $pCategorized
          nickName: $nickName
        }
      ) {
        clientMutationId
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response;
  });
};

const GetSetStoreSettings = (input: any) => {
  const variables = {
    getorset: input.action,
    pGoal: input.pGoal,
    settingtype: input.settingtype,
    timezoneValue: input.timezone,
    userid: userEmailId,
    storeNickName: input.storeNickName ? input.storeNickName : "",
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.realm,
    storeid: input.storeId,
    query: `mutation SetStoreSetting(
      $getorset: String
      $pGoal: JSON
      $settingtype: String
      $timezoneValue: String
      $userid: String
      $storeNickName: String
    ) {
      statelessCcPhysicalRwGetorsetStoreSettings(
        input: {
          getorset: $getorset
          pGoal: $pGoal
          settingtype: $settingtype
          timezoneValue: $timezoneValue
          userid: $userid
          storeNickName: $storeNickName
        }
      ) {
        statelessCcPhysicalRwStoreSettings {
          keyname
          keyvalue
          active
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwGetorsetStoreSettings
      .statelessCcPhysicalRwStoreSettings;
  });
};

export const updateTechnicianStatus = (
  input: any,
  storeId: any,
  Realm: any
) => {
  const variables = {
    techno: input.techno,
    pCategorized: input.pCategorized,
    statusval: input.statusval,
    nickName: input.nickName,
    userId: userEmailId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: Realm,
    storeid: storeId,
    query: `mutation updateTechnicianStatus(
      $techno: String
      $pCategorized: Int
      $statusval: BitString
      $nickName: String
      $user_id: String
    ) {
      statelessPhysicalRwUpdateTechnicianStatus(
        input: {
          techno: $techno
          statusval: $statusval
          pCategorized: $pCategorized
          nickName: $nickName
          userId: $user_id
        }
      ) {
        clientMutationId
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessPhysicalRwUpdateTechnicianStatus;
  });
};

export const updatePayTypeMasterByCode = (
  pVal: JSON,
  storeId: any,
  Realm: any
) => {
  const variables = {
    p_paytype: JSON.stringify(pVal),
    store_id: storeId,
    user_id: userEmailId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: Realm,
    storeid: storeId,
    query: `mutation updatePayTypeMasterByCode(
      $p_paytype: JSON
      $store_id: String
      $user_id: String
    ) {
      statelessCcPhysicalRwUpdatePaytypeMasterByPaytypecode(
        input: { pPaytype: $p_paytype, storeid: $store_id, userid: $user_id }
      ) {
        updatePaytypeMasterStatuses {
          status
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwUpdatePaytypeMasterByPaytypecode;
  });
};

const ConfigureTenant = (id: any) => {
  const variables = {
    tenantMasterId: id,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation ConfigureTenant($tenantMasterId: Int!) {
      createTenant(input: {tenantMasterId: $tenantMasterId}) {
        json
        clientMutationId
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(response.data.createTenant.json);
  });
};

const ConfigureStore = (id: any) => {
  const variables = {
    storeMasterId: id,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation ConfigureStore($storeMasterId: Int!) {
      createStore(input: {storeMasterId: $storeMasterId}) {
        json
        clientMutationId
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(response.data.createStore.json);
  });
};

export const InsertOrUpdatePartsMatrix = (
  realm: string,
  pCall: string,
  pStore: string,
  pNewPrtsource: string,
  pOldPrtsource: string,
  pMatrixType: string,
  pMatrixOrFleet: string,
  pCreatedDate: string,
  pStoreInstallDate: string,
  pMatrix: JSON
) => {
  const variables = {
    realm: realm,
    pCall: pCall,
    pStore: pStore,
    pMatrixOrFleet: pMatrixOrFleet,
    pNewPrtsource: pNewPrtsource,
    pOldPrtsource: pOldPrtsource,
    pMatrixType: pMatrixType,
    pCreatedDate: pCreatedDate,
    pStoreInstallDate: pStoreInstallDate,
    pMatrix: pMatrix ? JSON.stringify(pMatrix) : null,
    pUserId: userEmailId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realm,
    storeid: pStore,
    query: `mutation  InsertOrUpdatePartsMatrix( 
      $pCall: String!
      $pStore: String!
      $pMatrixOrFleet: String!
      $pNewPrtsource: [String!]!
      $pOldPrtsource: [String!]!
      $pMatrixType: String
      $pCreatedDate: Date!
      $pStoreInstallDate: Date!
      $pMatrix: JSON
      $pUserId: String!
      
    ) {
      statelessDbdKpiScorecardInsertOrUpdateKpiScorecardPartsMatrix(
        input: {
          pCall: $pCall,
          pStore: $pStore,
          pMatrixOrFleet: $pMatrixOrFleet,
          pNewPrtsource: $pNewPrtsource,
          pOldPrtsource: $pOldPrtsource,
          pMatrixType: $pMatrixType,
          pCreatedDate: $pCreatedDate,
          pStoreInstallDate: $pStoreInstallDate,
          pMatrix: $pMatrix,
          pUserId: $pUserId
        }
      ) {
        results {
          msg
          rStatus
        }
      }

    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data
      .statelessDbdKpiScorecardInsertOrUpdateKpiScorecardPartsMatrix;
  });
};

export const fetchKpiScorecardPartsMatrix = (input: any) => {
  const variables = {
    pCallType: input.pCallType,
    pStore: input.pStore,
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.realm,
    storeid: input.pStore,
    query: `
      query MyQuery(
        $pCallType: String!
        $pStore: String!
      ) {
        statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix(
          pStore: $pStore
          pCallType: $pCallType
        ) {
          nodes {
            partsFor
            priceStartRange
            priceEndRange
            addPercentage
            prtsource
            createdDate
            matrixType
            storeInstallDate
            calcBase
            breakField
            matrixOrder
            prtsourceList
          }
        }
      }
    `,
    variables: variables,
  }).then((response) => {
    return response.data.statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix
      .nodes;
  });
};

export const fetchKpiScorecardPartsMatrixDetails = (input: any) => {
  //ToDo: Need to move this to dataFetchQueries Page
  const variables = {
    pMatrixType: input.pMatrixType,
    pCreatedDate: input.pCreatedDate,
    pStore: input.pStore,
    pCallType: input.pCallType,
    pPrtsource: input.pPrtsource,
  };

  return FetchConfig({
    anonymous: false,
    Realm: input.realm,
    storeid: input.pStore,
    query: `
      query MyQuery(
        $pCallType: String!
        $pStore: String!
        $pMatrixType: String!
        $pCreatedDate: Date!
        $pPrtsource: [String!]!
      ) {
        statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix(
          pCallType: $pCallType
          pStore: $pStore
          pMatrixType: $pMatrixType
          pCreatedDate: $pCreatedDate
          pPrtsource:$pPrtsource
        ) {
          edges {
            node {
            partsFor
            priceStartRange
            priceEndRange
            addPercentage
            prtsource
            createdDate
            matrixType
            storeInstallDate
            calcBase
            breakField
            }
          }
        }
      }
    `,
    variables: variables,
  }).then((response) => {
    return response.data.statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix.edges.map(
      (edge: any) => edge.node
    );
  });
};

const EnableDisableTenant = (id: number, status: string) => {
  const variables = {
    tenantMasterId: id,
  };
  return FetchConfig({
    anonymous: false,
    query:
      status === "Enabled"
        ? `mutation DisableRealm($tenantMasterId: Int!) {
      disableRealm(input: {tenantMasterId: $tenantMasterId}) {
        clientMutationId
        string
      }
    }
  `
        : `mutation EnableRealm($tenantMasterId: Int!) {
    enableRealm(input: {tenantMasterId: $tenantMasterId}) {
      clientMutationId
      string
    }
  } 
`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data;
  });
};

const InsertUpdateLaborGrid = (input: any) => {
  let API =
    input.pCall === "insert"
      ? "statelessDbdKpiScorecardInsertOrUpdateKpiScorecardLaborGridMultiple"
      : "statelessDbdKpiScorecardInsertOrUpdateKpiScorecardLaborGrid";
  let newGridType =
    input.pCall === "insert"
      ? { pNewGridTypes: input.pNewGridType }
      : { pNewGridType: input.pNewGridType };
  const variables = {
    ...newGridType,
    pStore: input.storeId,
    pOldGridType: input.pCall === "insert" ? "" : input.pOldGridType,
    pCreatedDate: input.createdDate,
    pStoreInstallDate: input.storeInstallDate,
    pGrid: input.gridData ? JSON.stringify(input.gridData) : input.gridData,
    pUserId: userEmailId,
    pCall: input.pCall,
    pLaborMissType: input.pLaborMissType,
    pGridFor: input.pGridFor,
    pConfirmFlag: input.pConfirmFlag,
    pIsDefault: input.pIsDefault,
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.realm,
    storeid: input.storeId,
    query: `mutation InsertUpdateLaborGrid($pStore: String!
      $pOldGridType: String!
      ${
        input.pCall === "insert"
          ? "$pNewGridTypes: [String]!"
          : "$pNewGridType: String!"
      }
      $pGridFor: String!
      $pCreatedDate: Date!
      $pStoreInstallDate: Date!
      $pGrid: JSON
      $pUserId: String!
      $pCall: String!
      $pLaborMissType: String!
      $pConfirmFlag: String!
      $pIsDefault: String!
      ) {
        ${API}(input: {pStore: $pStore,
        ${
          input.pCall === "insert"
            ? "pNewGridTypes: $pNewGridTypes"
            : "pNewGridType: $pNewGridType"
        },
          pOldGridType: $pOldGridType,
          pGridFor: $pGridFor,
        pCreatedDate: $pCreatedDate,
        pStoreInstallDate: $pStoreInstallDate,
        pGrid: $pGrid,
        pCall: $pCall,
        pLaborMissType: $pLaborMissType,
        pConfirmFlag: $pConfirmFlag,
        pIsDefault: $pIsDefault,
        pUserId: $pUserId}) {
          clientMutationId
           ${
             input.pCall === "insert"
               ? "json"
               : `results{
            msg
            rStatus
          }`
           }

       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    let json =
      input.pCall === "insert"
        ? response.data
            .statelessDbdKpiScorecardInsertOrUpdateKpiScorecardLaborGridMultiple
            .json
        : "";
    return input.pCall === "insert"
      ? JSON.parse(json)
      : response.data
          .statelessDbdKpiScorecardInsertOrUpdateKpiScorecardLaborGrid
          .results[0];
  });
};

const InsertGriddataDtl = (input: any) => {
  const variables = {
    inCreatedDate: input.inCreatedDate,
    inGridTypes: input.inGridType,
    inStoreId: input.inStoreId,
    inDoorRate: input.inDoorRate,
    inGridFor: input.inGridFor,
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.realm,
    storeid: input.storeId,
    query: `mutation InsertGriddataDtl(
      $inCreatedDate: Date!
      $inGridTypes: [String]
      $inStoreId: String!
      $inDoorRate:BigFloat!
      $inGridFor: String!,
      ) {
        statelessCcPhysicalRwInsertGriddataDtlMultiple(input: {inCreatedDate: $inCreatedDate,
          inGridFor: $inGridFor,
          inGridTypes: $inGridTypes,
          inStoreId: $inStoreId,
          inDoorRate:$inDoorRate}) {
            clientMutationId
            json
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    let json =
      response.data.statelessCcPhysicalRwInsertGriddataDtlMultiple.json;
    return JSON.parse(json);
  });
};

export const FileUpload = (input: any) => {
  const variables = {
    base64Data: input.base64Data,
    inFileName: input.inFileName,
    inGridTypes: input.inGridType,
    inInstallationDate: input.inInstallationDate,
    inStoreId: input.inStoreId,
    inTenantId: input.inTenantId,
    inCreatedUser: userEmailId,
    inGridOrFleet: input.inGridOrFleet,
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.realm,
    storeid: input.storeId,
    query: `mutation FileUpload(
      $base64Data: String!
      $inFileName: String!
      $inGridTypes: [String]
      $inInstallationDate:Date
      $inStoreId: String!
      $inTenantId: String!
      $inCreatedUser:String!
      $inGridOrFleet: String!
      ) {
        statelessCcPhysicalRwInsertGriddataDtlFileUploadLogMultiple(input: {base64Data: $base64Data,
          inFileName: $inFileName,
          inGridTypes: $inGridTypes,
          inInstallationDate:$inInstallationDate,
          inStoreId: $inStoreId,
          inTenantId: $inTenantId,
          inCreatedUser:$inCreatedUser
          inGridOrFleet:$inGridOrFleet
          }) {
            clientMutationId
            json
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    let json =
      response.data.statelessCcPhysicalRwInsertGriddataDtlFileUploadLogMultiple
        .json;
    return JSON.parse(json);
  });
};

const UpdateBulkStatus = (input: any) => {
  const variables = {
    inCallType: input.pCallType,
    inTenantId: input.pTenantId,
    inStoreId: input.pStoreId,
    status: input.status,
  };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    storeid: input.storeId,
    query: `mutation updateBulkStatus ($inCallType: String!,
      $inTenantId: String!,
      $inStoreId: String!,
      $status:String!){
      statelessServiceBzoUpdateBulkLoadStatus(
        input: {inCallType: $inCallType, inStoreId: $inStoreId, inTenantId: $inTenantId, status: $status}
      ) {
        clientMutationId
        string
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoUpdateBulkLoadStatus.string;
  });
};

export const PartsMatrixFileUpload = (input: any) => {
  const variables = {
    base64Data: input.base64Data,
    inFileName: input.inFileName,
    inMatrixType: input.inMatrixType,
    inInstallationDate: input.inInstallationDate,
    inStoreId: input.inStoreId,
    inTenantId: input.inTenantId,
    inPrtsource: input.inPrtsource,
    inCreatedUser: userEmailId,
    inMatrixOrFleet: input.inMatrixOrFleet,
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.realm,
    storeid: input.inStoreId,
    query: `mutation PartsMatrixFileUpload(
      $base64Data: String!
      $inFileName: String!
      $inMatrixType: String
      $inInstallationDate:Date
      $inStoreId: String!
      $inTenantId: String!
      $inPrtsource: [String!]!
      $inCreatedUser:String!
      $inMatrixOrFleet: String!
      ) {
        statelessCcPhysicalRwInsertGriddataDtlPartsMatrixFileUploadLog(input: {base64Data: $base64Data,
          inFileName: $inFileName,
          inMatrixType: $inMatrixType,
          inInstallationDate:$inInstallationDate,
          inMatrixOrFleet: $inMatrixOrFleet,
          inStoreId: $inStoreId,
          inTenantId: $inTenantId,
          inPrtsource:$inPrtsource,
          inCreatedUser:$inCreatedUser}) {
            results {
              msg
              status
            }

       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data
      .statelessCcPhysicalRwInsertGriddataDtlPartsMatrixFileUploadLog;
  });
};

export const UpdateSubmitStatus = (
  inTenantId: any,
  inStoreId: any,
  inType: string
) => {
  const variables = {
    inStoreId: inStoreId,
    inTenantId: inTenantId,
    inType: inType,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation UpdateSubmitStatus(
      $inStoreId: String!
      $inTenantId:String!
      $inType: String!
      ) {
        statelessServiceBzoUpdateSubmissionStatus(input: {
          inStoreId: $inStoreId,
          inTenantId:$inTenantId,
          inType: $inType
        }) {
            clientMutationId
            results {
              msg
              status
            }
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoUpdateSubmissionStatus;
  });
};

export const getOpcodeCategoryStatus = (inTenantId: any, inStoreId: any) => {
  const variables = {
    inStoreId: inStoreId,
    inTenantId: inTenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation updateOpcodeCategorizationStatus(
      $inStoreId: String!
      $inTenantId:String!
      ) {
        statelessServiceBzoGetOpcodeCategorizationStatus(input: {
          inStoreId: $inStoreId,
          inTenantId:$inTenantId}) {
           
    string
  
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetOpcodeCategorizationStatus;
  });
};

export const updateLaunchReport = (inTenantId: any, inStoreId: any) => {
  const variables = {
    inStoreId: inStoreId,
    inTenantId: inTenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation MyMutation {
      statelessServiceBzoInsertOrUpdateWpCustomValues(
        input: {inStoreId: "", inCustomValues: "", inCallType: "Update"}
      ) {
        results {
          msg
          status
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetOpcodeCategorizationStatus;
  });
};

export const updateMatrixType = (input: any) => {
  const variables = {
    inCreatedUser: userEmailId,
    inOldMatrixType: input.inOldMatrixType,
    inNewMatrixType: input.inNewMatrixType,
    inStoreId: input.inStoreId,
    inTenantId: input.inTenantId,
    inActivity: input.inActivity,
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.Realm,
    query: `mutation updateMatrixType(
      $inCreatedUser: String!
      $inOldMatrixType: String!
      $inStoreId: String!
      $inTenantId: String!
      $inNewMatrixType: String!,
      $inActivity: String!
    ) {
      statelessCcPhysicalRwInsertPartsMatrixTypeMasterDetails(
        input: {
          inCreatedUser: $inCreatedUser,
          inOldMatrixType: $inOldMatrixType,
          inNewMatrixType: $inNewMatrixType,
          inStoreId: $inStoreId,
          inTenantId: $inTenantId,
          inActivity: $inActivity,
        }
      ) {
        results {
          msg
          status
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwInsertPartsMatrixTypeMasterDetails
      .results[0];
  });
};

export const GetLaborMissesGridTypes = (record: any) => {
  const variables = {
    newGridtype: null,
    oldGridtype: null,
    pProcess: "get",
    userid: userEmailId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: record.storeId,
    Realm: record.realmName,
    query: `mutation getLaborGridTypes($userid: String, $newGridtype: String, $oldGridtype: String, $pProcess: String) {
         statelessDbdKpiScorecardGetorsetLaborMissesGridTypeMaster(input: {pProcess: $pProcess, oldGridtype: $oldGridtype, newGridtype: $newGridtype, userid: $userid}) {
                clientMutationId
                    laborMissesGridTypes {
                            gridType
                                }
                            }
                        }
                    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data
      .statelessDbdKpiScorecardGetorsetLaborMissesGridTypeMaster
      .laborMissesGridTypes;
  });
};

export const GetLaborMissesModels = (record: any) => {
  const variables = {
    pUserId: userEmailId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: record.storeId,
    Realm: record.realmName,
    query: `mutation getLaborMissesModels($pUserId: String) {
          statelessDbdKpiScorecardGetLaborMissesModels(input: {pUserId: $pUserId}) {
                laborMissesModels {
                        gridType
                        make
                        model
                        categorized
                         __typename
                    }
                __typename
            }
        }                                   
      `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessDbdKpiScorecardGetLaborMissesModels
      .laborMissesModels;
  });
};

export const insertGridModelMapping = (pVal: any, record: any) => {
  const variables = {
    pVal: pVal,
    userid: userEmailId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: record.storeId,
    Realm: record.realmName,
    query: `mutation insertMakeDetails($userid: String, $pVal: JSON) {
        statelessDbdKpiScorecardLaborMissesModelMapping(input: {userid: $userid, pVal: $pVal}) {
              string
              __typename
            }
          }
        `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessDbdKpiScorecardLaborMissesModelMapping;
  });
};

export const updateModels = (pVal: any, realmName: any, storeId: any) => {
  const variables = {
    userid: userEmailId,
    pVal: pVal,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: storeId,
    query: `mutation crudMenuModels($pVal: JSON!, $userid: String) {
      statelessCcPhysicalRwCrudMenuModels(
        input: { pVal: $pVal, userid: $userid }
      ) {
        string
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwCrudMenuModels;
  });
};
export const DeleteSingleStore = (
  storeId: any,
  tenantId: any,
  inDeletedDate?: any
) => {
  const variables = {
    storeid: storeId,
    tenantid: tenantId,
    inDeletedDate: inDeletedDate,
  };
  return FetchConfig({
    anonymous: false,
    // Realm: realmName,
    storeid: storeId,
    query: `mutation DeleteSingleStore($storeid: String!, $tenantid: String!, $inDeletedDate: Date) {
      statelessServiceBzoDeleteStore(input: {storeid: $storeid, tenantid: $tenantid, inDeletedDate: $inDeletedDate}) {
        clientMutationId
        string
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoDeleteStore.string;
  });
};

export const addMenu = (input: any, realmName: any) => {
  const variables = {
    storeid: input.storeid,
    username: input.username,
    inMaxMiles: input.inMaxMiles,
    inMenuname: input.inMenuname,
    inMenuInterval: input.inMenuInterval,
    inMilesAfter: input.inMilesAfter,
    inMilesBefore: input.inMilesBefore,
    isdefault: input.isdefault,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: input.storeid,
    query: `mutation addMenu(
      $storeid: String
      $username: String
      $inMaxMiles: BigFloat
      $inMenuname: String
      $inMenuInterval: BigFloat
      $inMilesAfter: BigFloat
      $inMilesBefore: BigFloat
      $isdefault: BitString
    ) {
      statelessCcPhysicalRwInsertMenuMaster(
        input: {
          storeid: $storeid
          username: $username
          inMaxMiles: $inMaxMiles
          inMenuname: $inMenuname
          inMenuInterval: $inMenuInterval
          inMilesAfter: $inMilesAfter
          inMilesBefore: $inMilesBefore
          isdefault: $isdefault
        }
      ) {
        clientMutationId
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwInsertMenuMaster;
  });
};

export const insertMenuDetails = (input: any, realmName: any) => {
  const variables = {
    storeid: input.storeid,
    username: input.username,
    menudata: input.menudata,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: input.storeid,
    query: `mutation insertUser($menudata:  JSON!, $storeid: String, $username: String) {
      statelessCcPhysicalRwInsertMenuDetails(
        input: { menudata: $menudata, storeid: $storeid, username: $username }
      ) {
        clientMutationId
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwInsertMenuDetails;
  });
};

export const deleteInterval = (input: any, realmName: any) => {
  const variables = {
    storeid: input.storeid,
    userName: input.username,
    inMenuname: input.inMenuname,
    serviceType: input.serviceType,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: input.storeid,
    query: ` mutation deleteMenuMaster(
      $storeid: String
      $userName: String
      $inMenuname: String
      $serviceType: Int
    ) {
      statelessCcPhysicalRwDeleteMenuMaster(
        input: {
          storeid: $storeid
          username: $userName
          inMenuname: $inMenuname
          serviceType: $serviceType
        }
      ) {
        clientMutationId
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwDeleteMenuMaster;
  });
};

export const deleteMenu = (input: any, realmName: any) => {
  const variables = {
    storeid: input.storeid,
    userName: input.username,
    inMenuname: input.inMenuname,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: input.storeid,
    query: ` mutation deleteMenu(
      $storeid: String
      $username: String
      $inMenuname: String
    ) {
      statelessCcPhysicalRwDeleteMenuDetails(
        input: { storeid: $storeid, username: $username, inMenuname: $inMenuname }
      ) {
        clientMutationId
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwDeleteMenuDetails;
  });
};

export const editMenu = (input: any, realmName: any) => {
  const variables = {
    storeid: input.storeid,
    username: input.username,
    inMenuname: input.inMenuname,
    isdefault: input.isdefault,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: input.storeid,
    query: `mutation editMenu(
      $inMenuname: String
      $isdefault: BitString
      $storeid: String
      $username: String
    ) {
      statelessCcPhysicalRwInsertMenuDefaultStatus(
        input: {
          inMenuname: $inMenuname
          isdefault: $isdefault
          storeid: $storeid
          username: $username
        }
      ) {
        clientMutationId
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwInsertMenuDefaultStatus;
  });
};

export const addNewAduUser = (input: any) => {
  const variables = {
    email: input.userName,
    firstname: input.first,
    lastname: input.last,
    userRole: input.role,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation addNewAduUser(
  $email: String!
  $firstname: String!
  $lastname: String!
  $userRole: String!
) {
  statelessServiceBzoCreateAduUsersInAllRealms(
    input: {
      email: $email
      firstname: $firstname
      lastname: $lastname
      userRole: $userRole
    }
  ) {
    string
  }
}`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(
      response.data.statelessServiceBzoCreateAduUsersInAllRealms.string
    );
  });
};

export const checkAduUser = async (input: any) => {
  const variables = {
    email: input.userName,
    firstname: input.first,
    lastname: input.last,
    userRole: input.role,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation checkAduUser(
  $email: String!
  $firstname: String!
  $lastname: String!
  $userRole: String!
) {
  statelessServiceBzoCheckAduUser(
    input: {
      email: $email
      firstname: $firstname
      lastname: $lastname
      userRole: $userRole
    }
  ) {
    string
  }
}`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(response.data.statelessServiceBzoCheckAduUser.string);
  });
};

export const deleteAduUser = async (input: any) => {
  const variables = {
    email: input,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation deleteAduUser(
              $email: String!
            ) {
              deleteAduUserAllRealms(
                input: {
                  email: $email
                }
            ) {
              json
            }
          }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(response.data.deleteAduUserAllRealms.json);
  });
};

export const getOpcodeCounts = (dms: any, inStoreId: any, inTenantId: any) => {
  const variables = {
    dms: dms,
    inStoreId: inStoreId,
    inTenantId: inTenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation getOpcodeCounts(
      $dms: String!
      $inStoreId: String!
      $inTenantId:String!
      ) {
        statelessServiceBzoGetOnboardingOpcodeCategorizationCounts(input: {
          dms: $dms,
          inStoreId: $inStoreId,
          inTenantId:$inTenantId}) {
           
    results {
      categorizedCount
      competitiveCount
      maintenanceCount
      naCount
      repairCount
      totalCount
      uncategorizedCount
    }
  
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data
      .statelessServiceBzoGetOnboardingOpcodeCategorizationCounts;
  });
};

export const getDetailedLoginList = (fromDate: any, toDate: any) => {
  const variables = {
    fromDate: fromDate,
    toDate: toDate,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation getDetailedLoginList(
      $fromDate: Date
      $toDate: Date
      ) {
        statelessServiceReportsUserLoginAttemptsByDate(input: {
          fromDate: $fromDate,
          toDate: $toDate}) {
           
    
      json
    
  
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(
      response.data.statelessServiceReportsUserLoginAttemptsByDate.json
    );
  });
};

export const getDetailedLoginSummary = () => {
  return FetchConfig({
    anonymous: false,
    query: `
      mutation {
        statelessServiceReportsUserLoginStatsDblink(input: {}) {
          clientMutationId
          json
        }
      }
    `,
    variables: {},
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(
      response.data.statelessServiceReportsUserLoginStatsDblink.json
    );
  });
};

const DataMutationQueries = {
  DeleteTenant,
  MutateDms,
  MutateGroupingMaster,
  InsertOrUpdateTenant,
  OpcodeMutation,
  InsertStore,
  StoreOnboardingEmail,
  updateTechnicianStatus,
  updatePayTypeMasterByCode,
  UpdateAdvisorDetails,
  GetSetStoreSettings,
  ConfigureTenant,
  ConfigureStore,
  InsertOrUpdatePartsMatrix,
  fetchKpiScorecardPartsMatrix,
  EnableDisableTenant,
  InsertUpdateLaborGrid,
  InsertGriddataDtl,
  FileUpload,
  UpdateBulkStatus,
  PartsMatrixFileUpload,
  UpdateSubmitStatus,
  getOpcodeCategoryStatus,
  InsertWorkPackage,
  InsertWorkPackageHierarchies,
  updateMatrixType,
  updateModels,
  addMenu,
  addNewAduUser,
  checkAduUser,
  getOpcodeCounts,
  getDetailedLoginList,
  getDetailedLoginSummary,
};

export default DataMutationQueries;
