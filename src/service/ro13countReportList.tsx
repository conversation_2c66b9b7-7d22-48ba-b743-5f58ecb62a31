import FetchConfig from "./fetchConfig";
import { IFetchConfigReturn } from "../types";

export const GetRO13MonthCount = () => {
  const variables = {
    input: {}, // Provide required fields here if needed
  };

  return FetchConfig({
    anonymous: false,
    query: `mutation GetRO13MonthCount($input: StatelessServiceBzoGetLastThirteenMonthsRoCountDetailsInput!) {
      statelessServiceBzoGetLastThirteenMonthsRoCountDetails(input: $input) {
        results {
          month1
          month2
          month3
          month4
          month5
          month6
          month7
          month8
          month9
          month10
          month11
          month12
          month13
          thirteenMonthTotRocount
          tenantname
          storename
          manufacturer
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetLastThirteenMonthsRoCountDetails
      .results;
  });
};
