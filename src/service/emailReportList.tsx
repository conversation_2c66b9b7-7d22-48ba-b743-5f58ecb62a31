import FetchConfig from "./fetchConfig";
import { IFetchConfigReturn } from "../types";
export const GetEmailList = (date: { startDate: String; endDate: String }) => {
  const variables = {
    startDate: date.startDate,
    endDate: date.endDate,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation GetEmailList($startDate: String, $endDate: String) {
         statelessServiceReportsFetchMailgunEvents(input: {startDate: $startDate,endDate: $endDate}) {
                     json
                    }
                  }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceReportsFetchMailgunEvents.json;
  });
};
