import { ApolloClient, InM<PERSON>oryCache, HttpLink, from, ApolloLink } from "@apollo/client";
import { onError } from "@apollo/client/link/error";

/**
 * Apollo Client configuration with Chrome-specific retry prevention
 * Note: The main bulk data load issue is handled in FetchConfig, not Apollo Client
 * This adds an additional safety layer for any Apollo Client operations
 */
const apolloConfig = () => {
    const authMiddleware = new ApolloLink((operation, forward) => {
      operation.setContext({
        headers: {
          authorization: localStorage.getItem("access_token")
            ? `Bearer ${localStorage.getItem("access_token")}`
            : "",
          "auth-strategy": "next",
          appid: "bzo",
          realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
        },
      });
  
      return forward(operation);
    });
  
  // Chrome-specific timeout configuration to prevent browser retries
  const isChrome = /Chrome/.test(navigator.userAgent) && !/Edg/.test(navigator.userAgent);

  const httpLink = new HttpLink({
    uri: process.env.REACT_APP_API_URL,
    // Add fetch options to prevent Chrome's 5-minute retry behavior
    fetchOptions: {
      timeout: isChrome ? 240000 : 300000, // 4 minutes for Chrome, 5 minutes for others
    },
  });

  const errorLink = onError(({ graphQLErrors, networkError }) => {
    if (graphQLErrors)
      graphQLErrors.forEach(({ message, locations, path }) =>
        console.log(
          `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
        )
      );

    if (networkError) {
      console.log(`[Network error]: ${networkError}`);

      // Prevent any automatic retries for Chrome timeout errors
      if (isChrome && (networkError.message?.includes('timeout') || networkError.message?.includes('fetch'))) {
        console.warn('[CHROME RETRY PREVENTION] Network error detected - preventing automatic retry');
        return; // Don't retry
      }
    }
  });

  // If you provide a link chain to ApolloClient, you
  // don't provide the `uri` option.
  const client = new ApolloClient({
    // The `from` function combines an array of individual links
    // into a link chain
    link: from([errorLink, authMiddleware, httpLink]),
    cache: new InMemoryCache(),
  });
  return client;
};
export default apolloConfig;
