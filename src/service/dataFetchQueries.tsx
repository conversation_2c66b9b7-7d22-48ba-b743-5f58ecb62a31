import FetchConfig from "./fetchConfig";
import { IFetchConfigReturn } from "../types";

export const GetDmsList = () => {
  return FetchConfig({
    anonymous: false,
    query: `query GetDmsList() {
      dmsMasters() {
        nodes {
          id
          dms
          dmsImg
          createdTime
          lastUpdatedOn
          lastUpdatedBy
        }
      }
    }`,
  }).then((response: IFetchConfigReturn) => {
    return response.data.dmsMasters.nodes[0];
  });
};

export const GetGroupingDetailsQuery = (tenantId: string) => {
  const variables = {
    tenantId: tenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `query statelessServiceBzoGroupingMasterDetails($tenantId: String!) {
      statelessServiceBzoGroupingMasterDetails(
        condition: { tenantId: $tenantId }
      ) {
        nodes {
          groupDesc
          groupId
          groupName
          id
          storeId
          storeName
          tenantId
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGroupingMasterDetails.nodes;
  });
};
export const GetOpcodeQuery = (input: any) => {
  const variables = {
    pDms: input.dms,
    pTenantId: input.tenantId,
    pStoreId: input.storeId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `mutation statelessServiceBzoGetOnboardingOpcodeCategorization(
      $pDms: String!
      $pStoreId: String!
      $pTenantId: String!
    ) {
      statelessServiceBzoGetOnboardingOpcodeCategorization(
        input: { pDms: $pDms, pStoreId: $pStoreId, pTenantId: $pTenantId }
      ) {
        statelessServiceBzoOnboardingOpcodeCategorizations {
          additionalDescription
          cprocount
          elr
          id
          lbrlabortype
          lbropcode
          lbropcodedesc
          opcategory
          retailqualhours
          retailqualsale
          totalhours
          totallabordollars
          department
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetOnboardingOpcodeCategorization
      .statelessServiceBzoOnboardingOpcodeCategorizations;
  });
};
export const GetOpcodeChoiceQuery = (pType: string) => {
  const variables = {
    pType: pType,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoGetClassificationMasterDetails($pType: String!) {
      statelessServiceBzoGetClassificationMasterDetails(
        input: { pType: $pType }
      ) {
        statelessServiceBzoClassificationMasterDetails {
          value
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetClassificationMasterDetails
      .statelessServiceBzoClassificationMasterDetails;
  });
};
export const GetManufacturersList = () => {
  return FetchConfig({
    anonymous: false,
    query: `query statefulServiceBzoValidMakes {
      statefulServiceBzoValidMakes {
        nodes {
          manufacturer
          nodeId
        }
      }
    }`,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statefulServiceBzoValidMakes.nodes;
  });
};
export const GetOnboardingChecklist = (input: any) => {
  const variables = {
    pReqType: input.reqType,
    pTenant: input.tenantId,
    pStore: input.store,
  };
  return FetchConfig({
    anonymous: false,
    query: `query statelessServiceBzoGetOnboardingChecklist($pReqType: String!, $pTenant: String!, $pStore: String) {
      statelessServiceBzoGetOnboardingChecklist(
        pReqType: $pReqType
        pTenant: $pTenant
        pStore: $pStore
      ) {
        nodes {
          checklist
          createdOn
          displayName
          displayOrder
          lastupdatedBy
          lastupdatedOn
          processName
          storeId
          status
          storeName
          tenantId
          tenantName
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetOnboardingChecklist.nodes;
  });
};
export const GetAllTechnicians = (Realm: any, storeid: any) => {
  return FetchConfig({
    anonymous: false,
    Realm: Realm,
    storeid: storeid,
    query: `query getAllTechnicians {
      statelessCcPhysicalRwGetTechnicians {
        nodes {
          id
          active
          lbrtechno
          name
          nickname
          categorized
          
        }
      }
    }
  `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwGetTechnicians.nodes;
  });
};
export const GetPayTypeMasterDetails = (Realm: any, storeid: any) => {
  const variables = {
    storeId: storeid,
  };
  return FetchConfig({
    anonymous: false,
    Realm: Realm,
    storeid: storeid,
    query: `query getPayTypeMaster($storeId: String!) {
      statelessCcPhysicalRwGetPayTypeMasters (
        condition: {storeId: $storeId}
      ){
        nodes {
          department
          id
          lbrOrPrts
          payType
          payTypeCode
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwGetPayTypeMasters.nodes;
  });
};
const GetAllServiceAdvDetails = (Realm: string, storeid: string) => {
  return FetchConfig({
    anonymous: false,
    Realm: Realm,
    storeid: storeid,
    query: `query getAllServiceAdvDetails {
      statelessCcPhysicalRwGetTblServiceAdvisors {
        nodes {
          id
          name
          serviceadvisor
          active
          nickname
          categorized
        }
      }
    }
  `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes;
  });
};
const GetRealmStatus = (id: any) => {
  const variables = {
    tenantMasterId: id,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation GetRealmStatus($tenantMasterId: Int!) {
      getRealmStatus(input: {tenantMasterId: $tenantMasterId}) {
        clientMutationId
        string
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.getRealmStatus.string;
  });
};
const GetLaborGridList = (input: any) => {
  const variables = {
    pCallType: input.callType,
    pStore: input.storeId,
    pGridType: input.gridType ? input.gridType : null,
    pGridFor: input.gridFor ? input.gridFor : null,
    pCreatedDate: input.createdDate ? input.createdDate : null,
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.realm,
    storeid: input.storeId,
    query: `query GetLaborGridList($pCallType: String!
      $pStore: String!
      $pGridType: String
      $pCreatedDate: Date
      $pGridFor: String
      ) {
      statelessDbdKpiScorecardBzoGetKpiScorecardLaborGrid(
        pCallType: $pCallType
        pStore: $pStore
        pGridType: $pGridType
        pCreatedDate: $pCreatedDate
        pGridFor: $pGridFor
      ) {
        nodes ${
          input.callType === "Grid_Type"
            ? `{
          createdDate
          gridType
          storeInstallDate
          gridOrder
          gridFor

        }`
            : `{
          col0
          col1
          col2
          col3
          col4
          col5
          col6
          col7
          col8
          col9
          hours
        }`
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessDbdKpiScorecardBzoGetKpiScorecardLaborGrid
      .nodes;
  });
};

export const GetOnboardingLaunchReports = (tenantId: any) => {
  const variables = {
    tenantId: tenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `query MyQuery ($tenantId: String) {
      statelessServiceBzoTenantOnboardingLaunchReports (condition: { tenantId: $tenantId }){
        nodes {
          bulkdatapull
          bulkdatapullcomments
          credentialemailaddedtostorefolderforlaunch
          credentialemailaddedtostorefolderforlaunchcomments
          credentialsverifiedwithstore
          credentialsverifiedwithstorecomments
          daysdealerbeginssendingenrollmentitemstoallitemsin
          daysdealerbeginssendingenrollmentitemstoallitemsincomments
          daysfromdealsignedtostorelaunched
          daysfromdealsignedtostorelaunchedcomments
          dealerbeginstosenditems
          dealerbeginstosenditemscomments
          dms
          dmsproviderconfirms
          dmsproviderconfirmscomments
          enrollmentemailtostore
          enrollmentemailtostorecomments
          enrollmentrecieved
          enrollmentrecievedcomments
          fileuploadvalidationresultonbzo
          fileuploadvalidationresultonbzocomments
          fopcadminaccess
          fopcadminaccesscomments
          fopcsiteaccessreadytolaunch
          fopcsiteaccessreadytolaunchcomments
          labourgridfromstore
          labourgridfromstorecomments
          launchdatetarget
          launchdatetargetcomments
          leadcredit
          monthlysalesreportsbystoreandadvisor
          monthlysalesreportsbystoreandadvisorcomments
          notes
          opcodescategorizedinbzo
          opcodescategorizedinbzocomments
          partspricingmatrixaddedinbzo
          partspricingmatrixaddedinbzocomments
          partspricingmatrixrecievedfromstore
          partspricingmatrixrecievedfromstorecomments
          repairpricingpolicylabourgridupdatedinbzo
          repairpricingpolicylabourgridupdatedinbzocomments
          rooftops
          salesreportprovidedtoengineeringteamfordatavalidation
          salesreportprovidedtoengineeringteamfordatavalidationcomments
          signedagreement
          signedagreementcomments
          sitereadyforreview
          sitereadyforreviewcomments
          store
          storelaunched
          storelaunchedcomments
          storenamingdropdownordersenttoengineeringteam
          storenamingdropdownordersenttoengineeringteamcomments
          storesubmitsfordmsaccess
          storesubmitsfordmsaccesscomments
          tenant
          tenantandstoredetailsaddedinbzo
          tenantandstoredetailsaddedinbzocomments
          updatecrmfopcinfoagreement
          updatecrmfopcinfoagreementcomments
        }
      }
    }
    
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoTenantOnboardingLaunchReports.nodes;
  });
};
export const GetLaunchReport = () => {
  return FetchConfig({
    anonymous: false,
    query: `query MyQuery {
      statelessServiceBzoTenantsLauncheds {
        nodes {
          count
          tenantId
          tenantName
          tenantImg
        }
      }
    }    
  `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoTenantsLauncheds.nodes;
  });
};

export const GetGridTypeOptions = (
  tenantId: any,
  storeId: any,
  realmName: any
) => {
  const variables = {
    tenantId: tenantId,
    storeId: storeId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    query: `query MyQuery ($tenantId:String!,$storeId:String!) {
      statelessCcPhysicalRwPartsMatrixTypeMasterDetails(
        condition: {storeId: $storeId, tenantId: $tenantId}
      ) {
        nodes {
          classificationType
          storeId
          tenantId
          value
        }
      }
    } 
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwPartsMatrixTypeMasterDetails
      .nodes;
  });
};
const GetActivityLog = (input: any) => {
  const variables =
    input.spec === "storeSpecific"
      ? {
          startDate: input.startDate,
          endDate: input.endDate,
          storeId: input.storeId,
        }
      : input.spec === "tenantSpecific"
      ? {
          startDate: input.startDate,
          endDate: input.endDate,
          tenantId: input.tenantId,
        }
      : {
          startDate: input.startDate,
          endDate: input.endDate,
        };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query:
      input.spec === "storeSpecific"
        ? `query GetActivityLog($startDate: Datetime, $endDate: Datetime, $storeId: String) {
      statelessServiceBzoActivityLogs(
        filter: {createdTime: {greaterThanOrEqualTo: $startDate, lessThanOrEqualTo: $endDate}}
        condition: {storeId: $storeId}
      ) {
        nodes {
          activity
          createdTime
          createdUser
          status
          module
        }
      } 
    }
    `
        : input.spec === "tenantSpecific"
        ? `query GetActivityLog($startDate: Datetime, $endDate: Datetime, $tenantId: String) {
    statelessServiceBzoActivityLogs(
      filter: {createdTime: {greaterThanOrEqualTo: $startDate, lessThanOrEqualTo: $endDate}}
      condition: {tenantId: $tenantId}
    ) {
      nodes {
        activity
        createdTime
        createdUser
        status
        storeName
        module
      }
    } 
  }
  `
        : `query GetActivityLog($startDate: Datetime, $endDate: Datetime) {
      statelessServiceBzoActivityLogs(
        filter: {createdTime: {greaterThanOrEqualTo: $startDate, lessThanOrEqualTo: $endDate}}
      ) {
        nodes {
          activity
          createdTime
          createdUser
          slno
          status
          storeName
          tenantName
          module
        }
      } 
    }
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoActivityLogs.nodes;
  });
};
export const loadMenuModelData = (realmName: any, storeId: any) => {
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: storeId,
    query: `mutation getMenuModalData {
      statelessCcPhysicalRwGetMenuModels(input: {}) {
        getModels {
          make
          menuName
          modelName
        }
      }
    }
  `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwGetMenuModels.getModels;
  });
};

const GetGeneralMainNeverEvents = (clientId: string) => {
  const variables = {
    clientId: clientId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query getGeneralMainNeverEvents($clientId: String!) {
      getGeneralMainNeverEvents(condition: {clientId: $clientId}) {
        nodes {
          clientname
          optionName
          statusData
          statusDate
          storeId
          storeName
          clientId
          optionDisplayName
          subDomain
        }
      }
    } 
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.getGeneralMainNeverEvents.nodes;
  });
};
const GetGeneralMainErrorStatuses = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query getGeneralMainErrorStatuses {
      getGeneralMainErrorStatuses {
        nodes {
          clientId
          clientName
          errCount
        }
      }
    }
     
  `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.getGeneralMainErrorStatuses.nodes;
  });
};
const GetGeneralNeverEvents = (clientId: string) => {
  const variables = {
    clientId: clientId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query getGeneralNeverEvents($clientId: String!) {
      getGeneralNeverEvents(condition: {clientId: $clientId}) {
        nodes {
          clientname
          optionName
          statusData
          statusDate
          storeId
          storeName
          clientId
          optionDisplayName
          subDomain
        }
      }
    } 
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.getGeneralNeverEvents.nodes;
  });
};
const GetGeneralNeverErrorStatuses = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query getGeneralNeverErrorStatuses {
      getGeneralNeverErrorStatuses {
        nodes {
          clientId
          clientName
          errCount
        }
      }
    }
     
  `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.getGeneralNeverErrorStatuses.nodes;
  });
};

const GetDailyLogins = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query GetDailyLogins{
      dailyLoginDetails{
        nodes {
            details
          }
      }
    }
  `,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(response.data.dailyLoginDetails.nodes[0].details);
  });
};

const GetDailyDataImportStatusFailed = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query GetDailyDataImportStatusFailed{
      dailyDataimportStatusFaileds{
        nodes {
          clientname
          dataLoadDate
          enddate
          errMessage
          startdate
          statuss
          storeId
          storeName
          dms
        }
      }
    }
  `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.dailyDataimportStatusFaileds.nodes;
  });
};

const GetDataAsOf = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query GetDataAsOf{
      statelessServiceBzoDataAsOfs {
    nodes {
      storeName
      tenantName
      tenant
      value
      dms
      subDomain
    }
  }
    }
  `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoDataAsOfs.nodes;
  });
};

const GetAduUsersList = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query GetAduUsersList {
  statelessServiceBzoAduUserslists {
    nodes {
      createdAt
      firstName
      lastname
      passwords
      role
      userName
    }
  }
}
  `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoAduUserslists.nodes;
  });
};

const GetLaunchReportsList = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query GetLaunchReportsList {
      tenantOnboardingLaunchReports {
        nodes {
          addEnrollments
          addEnrollmentsId
          addRepairPricingPolicyLaborGridToBzo
          addRepairPricingPolicyLaborGridToBzoId
          addStoreMetaData
          addStoreMetaDataId
          bulkDataPullFromDmsToDb
          bulkDataPullFromDmsToDbId
          configureBzoForStore
          configureBzoForStoreId
          configureGroupFromBzo
          configureGroupFromBzoId
          configureIdentityProvider
          configureIdentityProviderId
          configureKeycloak
          configureKeycloakId
          credentialsVerifiedWithStore
          credentialsVerifiedWithStoreId
          dmsValue
          fileUploadValidationResultOnBzo
          fileUploadValidationResultOnBzoId
          fopcSiteReadyForLaunchAws
          fopcSiteReadyForLaunchAwsId
          fopcSiteReadyForReviewDev
          fopcSiteReadyForReviewDevId
          fopcSiteReadyForReviewUat
          fopcSiteReadyForReviewUatId
          launchDateTarget
          launchDateTargetId
          launchStoreName
          leadCredit
          middlewareConfiguration
          middlewareConfigurationId
          monthlySalesReportTotalShop
          monthlySalesReportTotalShopId
          monthlySalesReportsByStoreAndAdvisor
          monthlySalesReportsByStoreAndAdvisorId
          opcodeCategorization
          opcodeCategorizationId
          partsPricingMatrixAddedToBzo
          partsPricingMatrixAddedToBzoId
          postOnboardingData
          postOnboardingDataId
          recieveConfirmationFromDmsProvider
          recieveConfirmationFromDmsProviderId
          recieveEnrollmentItemsFromStore
          recieveEnrollmentItemsFromStoreId
          requestForDmsAccess
          requestForDmsAccessId
          requestForEnrollmentItemsFromStore
          requestForEnrollmentItemsFromStoreId
          roofTops
          settingUpDailyLoad
          settingUpDailyLoadId
          setupKeycloak
          setupKeycloakId
          setupSiteAccessForStoreAdminAndGroupUsers
          setupSiteAccessForStoreAdminAndGroupUsersId
          signedAgreement
          signedAgreementId
          siteReadyForReview
          siteReadyForReviewId
          slno
          storeNaming
          storeNamingId
          storeProjectId
          storeProjectName
          tenantProjectId
          tenantProjectName
          totalStoreProjectCount
          updateCrmAgreement
          updateCrmAgreementId
        }
      }
    }
    `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.tenantOnboardingLaunchReports.nodes;
  });
};

export const loadMenuNames = (realmName: any, storeId: any) => {
  const variables = {
    storeid: storeId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: storeId,
    query: ` mutation getMenuNames($storeid: String) {
      statelessCcPhysicalRwGetMenunames(input: { storeid: $storeid }) {
        getMenuNames {
          menuEnable
          menuName
          menuIsdefault
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response?.data?.statelessCcPhysicalRwGetMenunames?.getMenuNames;
  });
};

export const loadMenuDetails = (
  menuName: any,
  realmName: any,
  storeId: any
) => {
  const variables = {
    storeid: storeId,
    menuname: menuName,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: storeId,
    query: `mutation menuDetails($storeid: String, $menuname: String) {
      statelessCcPhysicalRwGetMenuDetails(
        input: { storeid: $storeid, menuname: $menuname }
      ) {
        getMenudetails {
          intervalList
          jsonGeneral
          jsonOpcodeList
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response?.data?.statelessCcPhysicalRwGetMenuDetails?.getMenudetails;
  });
};

export const fetchMenuNames = (input: any, realmName: any) => {
  const variables = {
    storeid: input.storeid,
    username: input.username,
    inMenuname: input.inMenuname,
    inServicetype: input.inServicetype,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: input.storeid,
    query: `mutation getMenuData(
      $storeid: String
      $username: String
      $inMenuname: String
      $inServicetype: Int
    ) {
      statelessCcPhysicalRwGetMenuData(
        input: {
          storeid: $storeid
          username: $username
          inMenuname: $inMenuname
          inServicetype: $inServicetype
        }
      ) {
        getMdata {
          categoryList
          mName
          mInterval
          mServicetype
          mPrice
          mFrh
          mItems
          mOpcodes
          mSeries
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwGetMenuData.getMdata;
  });
};
export const getFilteredOpcode = (input: any, realmName: any) => {
  const variables = {
    storeid: input.storeid,
    menuName: input.inMenuname,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: input.storeid,
    query: `mutation getFilteredOpcode($storeid: String, $menuName: String) {
      statelessCcPhysicalRwGetFilteredMenuopcodes(
        input: { menuname: $menuName, storeid: $storeid }
      ) {
        getFilteredMenuopcodesViews {
          opcodeList
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwGetFilteredMenuopcodes
      .getFilteredMenuopcodesViews;
  });
};
export const getMenuPopUp = (input: any, realmName: any) => {
  const variables = {
    storeid: input.storeid,
    menuName: input.inMenuname,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: input.storeid,
    query: `mutation menuPopup($storeid: String, $menuName: String) {
      statelessCcPhysicalRwGetMenuPopup(
        input: { storeid: $storeid, argmenuName: $menuName }
      ) {
        getMenuPopupData {
          afterMiles
          beforeMiles
          maxMiles
          menuName
          milegaeInterval
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwGetMenuPopup.getMenuPopupData;
  });
};
export const getMenuServiceType = (input: any, realmName: any) => {
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: input.storeid,
    query: `query getMenuServiceType {
      statelessCcPhysicalRwGetMenuServiceType {
        nodes {
          id
          serviceType
        }
      }
    }
  `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwGetMenuServiceType.nodes;
  });
};

const GetQaTestContent = (testDate: string) => {
  const variables = {
    testDate: testDate,
  };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    // storeid: input.storeid,
    query: `query GetQaTestContent($testDate: Date!) {
      statelessServiceReportsSmokeTestResults(condition: {testDate: $testDate}) {
          nodes {
            tenant
            store
            content
            type
            date
            userRole
          }
        }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response?.data?.statelessServiceReportsSmokeTestResults?.nodes;
  });
};

export const getRevenueSummaryRevenueByPaytype = () => {
  return fetch("/data/RevenueSummaryGetAnalysisFsRevenueByPaytype.json")
    .then((res) => res.json())
    .then((data) => {
      return data.data.statelessDbdRevenueSummaryGetAnalysisFsRevenueByPaytype
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};
export const loadMonthYears = () => {
  return fetch("/data/rdDrilldownGetMonthYears.json")
    .then((res) => res.json())
    .then((data) => {
      return data.data.statelessCcDrilldownGetMonthYears.nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryLaborRevenueByCategory = () => {
  return fetch(
    "/data/RevenueSummaryGetAnalysisFsCustomerLbrRevenueByCategory.json"
  )
    .then((res) => res.json())
    .then((data) => {
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsCustomerLbrRevenueByCategory
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryPartsRevenueByCategory = () => {
  return fetch(
    "/data/RevenueSummaryGetAnalysisFsCustomerPrtsRevenueByCategory.json"
  )
    .then((res) => res.json())
    .then((data) => {
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsCustomerPrtsRevenueByCategory
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryLaborRevenueByComponent = () => {
  return fetch(
    "/data/RevenueSummaryGetAnalysisFsCustomerLbrRevenueByComponents.json"
  )
    .then((res) => res.json())
    .then((data) => {
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsCustomerLbrRevenueByComponents
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryPartsRevenueByComponent = () => {
  return fetch(
    "/data/RevenueSummaryGetAnalysisFsCustomerPrtsRevenueByComponents.json"
  )
    .then((res) => res.json())
    .then((data) => {
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsCustomerPrtsRevenueByComponents
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryCustomerJobLevelBkdown = () => {
  return fetch("/data/RevenueSummaryGetAnalysisFsCustomerJobLevelBkdown.json")
    .then((res) => res.json())
    .then((data) => {
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsCustomerJobLevelBkdown.nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryCustomerJobLevelBkdownPerc = () => {
  return fetch(
    "/data/RevenueSummaryGetAnalysisFsCustomerJobLevelBkdownPerc.json"
  )
    .then((res) => res.json())
    .then((data) => {
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsCustomerJobLevelBkdownPerc
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryWarrantyVolumesLabor = () => {
  return fetch("/data/getRevenueSummaryWarrantyVolumesLabor.json")
    .then((res) => res.json())
    .then((data) => {
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsWarrantyVolumesLaborByMonth
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryWarrantyVolumesParts = () => {
  return fetch("/data/getRevenueSummaryWarrantyVolumesParts.json")
    .then((res) => res.json())
    .then((data) => {
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsWarrantyVolumesPartsByMonth
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

const DataFetchQueries = {
  GetGeneralMainNeverEvents,
  GetGeneralMainErrorStatuses,
  GetGeneralNeverEvents,
  GetGeneralNeverErrorStatuses,
  GetGroupingDetailsQuery,
  GetOpcodeQuery,
  GetOpcodeChoiceQuery,
  GetAllServiceAdvDetails,
  GetAllTechnicians,
  GetPayTypeMasterDetails,
  GetRealmStatus,
  GetLaborGridList,
  GetOnboardingLaunchReports,
  GetLaunchReport,
  GetGridTypeOptions,
  GetActivityLog,
  GetDailyDataImportStatusFailed,
  loadMenuModelData,
  GetLaunchReportsList,
  GetQaTestContent,
  GetDataAsOf,
  GetDailyLogins,
  GetAduUsersList,
};

export default DataFetchQueries;
