interface IHeader {
  anonymous: boolean;
  authHeader: string;
}

interface IFetchConfig {
  anonymous: boolean;
  Realm?: any;
  storeid?: any;
  method?: string;
  query: string;
  variables?: any;
  timeout?: number; // Optional timeout in milliseconds
}

interface IFetchConfigReturn {
  status: number;
  data?: any;
  error?: any;
}

const { REACT_APP_API_URL } = process.env;
const Header = ({ anonymous, authHeader }: IHeader) => {
  const minimal = {
    "Content-Type": "application/json",
    Accept: "application/json",
    appid: "bzo",
    //   appid: "contact-orchestrator",
    //   realm: "Medigy",
    //   "auth-strategy": "next",
  };
  return anonymous
    ? minimal
    : { ...minimal, Authorization: `Bearer ${authHeader}` };
};

async function FetchConfig({
  anonymous,
  Realm,
  storeid,
  method = "POST",
  query,
  variables = {},
  timeout,
}: IFetchConfig): Promise<IFetchConfigReturn> {
  return await new Promise((resolve, reject) => {
    const authHeader = String(localStorage.getItem("access_token"));
    const url: any = anonymous ? REACT_APP_API_URL : REACT_APP_API_URL;
    const headers: HeadersInit = Header({ anonymous, authHeader });
    const actualRealm = Realm ? Realm : String(process.env.REACT_APP_KEYCLOAK_REALM);
    headers["Realm"] = actualRealm;
    headers["Storeid"] = storeid ? storeid : "";

    // Create AbortController to handle request cancellation
    const controller = new AbortController();

    // Determine timeout based on request type - CHROME-SPECIFIC RETRY PREVENTION
    const getRequestTimeout = () => {
      if (timeout) return timeout; // Use provided timeout

      // Detect Chrome browser (Chrome has specific 5-minute retry behavior)
      const isChrome = /Chrome/.test(navigator.userAgent) && !/Edg/.test(navigator.userAgent);
console.log('isChrome======================>',isChrome,query)
      if (query.includes('statelessServiceBzoBulkDataloadProcess')) {
        // Chrome-specific timeout to prevent 5-minute retry
        return isChrome ? 240000 : 300000; // 4 min for Chrome, 5 min for others
      }

      if (query.includes('mutation')) {
        // Shorter timeout for Chrome to prevent retries
        return isChrome ? 60000 : 120000; // 1 min for Chrome, 2 min for others
      }

      return isChrome ? 30000 : 60000; // 30 sec for Chrome, 1 min for others
    };

    const requestTimeout = getRequestTimeout();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, requestTimeout);

    fetch(url, {
      method: method,
      headers: headers,
      signal: controller.signal,
      body: JSON.stringify({
        query,
        variables,
      }),
    })
      .then((response) => {
        clearTimeout(timeoutId);
        return response.json();
      })
      .then((data) => {
        resolve({ status: 200, data: data.data, error: data.errors || [] });
      })
      .catch((error) => {
        clearTimeout(timeoutId);
        if (error.name === 'AbortError') {
          console.log("Request was aborted due to timeout");
          reject({ status: 408, error: "Request timeout" });
        } else {
          console.log("error fetch", error);
          reject({ status: 400, error });
        }
      });
  });
}

export default FetchConfig;
