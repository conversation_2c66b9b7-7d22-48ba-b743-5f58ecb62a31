import FetchConfig from "./fetchConfig";
import { IFetchConfigReturn } from "../types";

//Query
export const GetLaunchReportsListDetails = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query GetLaunchReportsListDetails {
      statelessServiceReportsWorkpackageCounts {
        nodes {
         onboardingTotal
        needsObMeeting
        dmsNotActive
        opsNotCategorized
        gridNotEntered
        modelsNotMapped
        matrixNotEntered
        usersNotCreated
        launchNotScheduled
        launchCompleted
        allStores

        }
      }
    }
    `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceReportsWorkpackageCounts.nodes;
  });
};

//Muutation
export const GetAduOpenStoreWorkpackages = () => {
  return FetchConfig({
    anonymous: false,
    query: `mutation GetAduOpenStoreWorkpackages {
      statelessServiceReportsGetAduStoreWorkpackages(input: {}) {
       results {
       agreementReceived
      coach
      coachComments
      coachValue
      dms
      dmsActiveDate
      dmsValue
      groupName
      groupNameComments
      laborPricingGridEnteredInBzo
      laborPricingGridReceived
      laborPricingGridReceivedComments
      launchCompleted
      modelMapping
      modelMappingComments
      obAge
      obMeetingCompleted
      obMeetingCompletedComments
      onboardingCoordinator
      onboardingCoordinatorValue
      opCodesAvailable
      opCodesCategorized
      partsMatrixReceived
      partsMatrixEnteredInBzo
      partsMatrixReceivedComments
      readyForReview
      requestedDmsAccess
      requestedDmsAccessComments
      requestedObMeeting
      requestedObMeetingComments
      reviewCompleted
      salesperson
      salespersonValue
      scheduledLaunch
      slno
      smokeTestCompleted
      storeName
      storeNameComments
      storeProjectId
      tagDmsAction
      tagGroupPay
      tagGroupPayValue
      tagLaborPricingGridReceived
      tagObMeetingCompletedAction
      tagPartsMatrixReceivedFollowup
      tagRequestedDmsAccessFollowup
      tagRequestedObMeetingFollowup
      tagScheduledLaunchActionNeeded
      tagStoreHold
      taggrouphold
      tenantProjectId
      totalDaysToLaunch
      usersCreated
      usersCreatedComments
    }
      }
    }
  `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceReportsGetAduStoreWorkpackages.results;
  });
};
export const UpdateTextFieldComments = (
  fieldName: string,
  fieldValue: string,
  workPackageId: string | number
) => {
  return FetchConfig({
    anonymous: false,
    query: `
      mutation UpdateTextFieldComments(
        $fieldName: String!
        $fieldValue: String!
        $workPackageId: String!
      ) {
        statelessServiceReportsUpdateTextField(
          input: {
            fieldName: $fieldName
            fieldValue: $fieldValue
            workPackageId: $workPackageId
          }
        ) {
          clientMutationId
          string
        }
      }
    `,
    variables: {
      fieldName,
      fieldValue,
      workPackageId,
    },
  }).then((response: IFetchConfigReturn) => {
    return response?.data?.statelessServiceReportsUpdateTextField;
  });
};

export const GetDropDownValues = (fieldName: string) => {
  const variables = {
    fieldName: fieldName,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation GetDropDownValues($fieldName: String) {
         statelessServiceReportsGetAllowedValuesForCustomField(input: {fieldName: $fieldName}) {
                    results {
                            customOptionId
                            customOptionValue
                                }
                            }
                        }
                    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceReportsGetAllowedValuesForCustomField
      .results;
  });
};
export const UpdateDropdownField = (
  fieldName: string,
  fieldValue: string,
  workPackageId: number
) => {
  return FetchConfig({
    anonymous: false,
    query: `
      mutation UpdateDropdownField(
        $fieldName: String!
        $fieldValue: String!
        $workPackageId: Int
      ) {
        updateOpenprojectCustomField(
          input: {
            fieldName: $fieldName
            fieldValue: $fieldValue
            workPackageId: $workPackageId
          }
        ) {
          clientMutationId
          string
        }
      }
    `,
    variables: {
      fieldName,
      fieldValue,
      workPackageId,
    },
  }).then((response: IFetchConfigReturn) => {
    return response?.data?.updateOpenprojectCustomField;
  });
};
