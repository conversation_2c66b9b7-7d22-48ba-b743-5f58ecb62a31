import FetchConfig from "../fetchConfig";
import { IFetchConfigReturn } from "../../types";

const GetSchemaConfig = () => {
  return FetchConfig({
    anonymous: false,
    query: `mutation GetSchemaConfig {
  statelessServiceBzoGetSchemaConfig(input: {}) {
    results {
      activeYn
      authPassword
      authUsername
      clientId
      configId
      dbName
      dbServer
      hostname
      port
      realmName
    }
  }
}      
    `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetSchemaConfig.results;
  });
};

const GetServers = () => {
  return FetchConfig({
    anonymous: false,
    query: `mutation GetServers {
  statelessServiceBzoGetServers(input: {}) {
    results {
      createdAt
      dbName
      dbPort
      dbUser
      ipAddress
      isFull
      serverNo
      updatedAt
    }
  }
}    
    `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetServers.results;
  });
};

const GetDbSettings = () => {
  return FetchConfig({
    anonymous: false,
    query: `mutation getServerDetailsData {
      statelessServiceBzoGetServers(input: {}) {
       results {
        dbName
        createdAt
        dbPort
        dbUser
        dbPassword
        updatedAt
        serverNo
        isFull
        ipAddress
    }
      }
    }
  `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetServers.results;
  });
};

const UpsertDbSettings = (input: any) => {

  const variables = {
    dbName: input.dbName,
    dbPort: input.dbPort,
    dbUser: input.dbUser,
    dbPassword: input.dbPassword,
    isFull: input.isFull,
    ipAddress: input.ipAddress
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation UpsertDbSettings(
        $dbName: String!
        $dbPort: Int!
        $dbUser: String!
        $dbPassword: String!
        $isFull: Boolean!
        $ipAddress: String!
      ) {
    statelessServiceBzoUpsertServer(
      input: {
        pDbName: $dbName
        pDbPort: $dbPort
        pDbUser: $dbUser
        pDbPassword: $dbPassword
        pIsFull: $isFull
        pIpAddress: $ipAddress
      }
    ) {
      clientMutationId
      string
    }
  }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoUpsertServer.string;
  });
};

const DeleteServer = (input: any) => {
  const variables = {
    dbPort: input.dbPort,
    ipAddress: input.ipAddress
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation DeleteServer(
        $dbPort: Int!
        $ipAddress: String!
      ) {
    statelessServiceBzoDeleteServer(
      input: {
        pDbPort: $dbPort
        pIpAddress: $ipAddress
      }
    ) {
      clientMutationId
      string
    }
  }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoDeleteServer.string;
  });

};



const SettingsQueries = {
  GetSchemaConfig,
  GetServers,
  GetDbSettings,
  UpsertDbSettings,
  DeleteServer
};

export default SettingsQueries;
