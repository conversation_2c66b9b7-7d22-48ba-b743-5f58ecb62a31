import { IFetchConfigReturn } from "../../types";
import FetchConfig from "../fetchConfig";
const userEmailId = localStorage.getItem("userEmail");
const username = localStorage.getItem("user");

const InsertComment = (input: any) => {
  const variables = {
    inActivity: input.activity,
    inCommentText: input.comment,
    inCreatedBy: input.activity === "Delete" ? input.inCreatedBy : username,
    inStoreId: input.storeId,
    inTenantId: input.tenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation InsertComment(
        $inActivity: String!
        $inCommentText: String!
        $inCreatedBy: String!
        $inStoreId: String!
        $inTenantId: String!
      ) {
    statelessServiceBzoInsertOrUpdateCommentMaster(
      input: {
        inActivity: $inActivity
        inCommentText: $inCommentText
        inCreatedBy: $inCreatedBy
        inStoreId: $inStoreId
        inTenantId: $inTenantId
      }
    ) {
      clientMutationId
      string
    }
  }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertOrUpdateCommentMaster.string;
  });
};
const UpdateSubmissionStatus = (
  inStoreId: string,
  inTenantId: string,
  inType: string
) => {
  const variables = {
    inStoreId,
    inTenantId,
    inType,
  };

  return FetchConfig({
    anonymous: false,
    query: `
      mutation InsertWorkPackage(
        $inStoreId: String!
        $inTenantId: String!
        $inType: String!
      ) {
        statelessServiceBzoUpdateSubmissionStatus(
          input: {
            inStoreId: $inStoreId
            inTenantId: $inTenantId
            inType: $inType
          }
        ) {
          results {
            msg
            status
          }
        }
      }
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoUpdateSubmissionStatus.results[0];
  });
};

const StoreMutations = {
  InsertComment,
  UpdateSubmissionStatus,
};

export default StoreMutations;
