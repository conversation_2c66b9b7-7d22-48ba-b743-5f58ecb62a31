import { IFetchConfigReturn } from "../../types";
import FetchConfig from "../fetchConfig";

const UpdateBillingDetails = (tenantId: any, billingDetails: any) => {
  const variables = {
    inTenantId: tenantId,
    inSubscriptionDetails: JSON.stringify(billingDetails),
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation UpdateBillingDetails($inTenantId: String!, $inSubscriptionDetails: JSON!) {
  statelessServiceBillingUpdateBillingSubscriptions(
    input: {inSubscriptionDetails: $inSubscriptionDetails, inTenantId: $inTenantId}
  ) {
    results {
      msg
      status
    }
  }
}`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBillingUpdateBillingSubscriptions
      .results[0];
  });
};

const RunTestTenants = (testTenants: any) => {
  const variables = {
    inSmokeTestTenants: JSON.stringify(testTenants),
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation RunTestTenants ($inSmokeTestTenants: JSON!) {
  statelessServiceReportsRunSmokeTestTenants(input: {inSmokeTestTenants: $inSmokeTestTenants}) {
    results {
      msg
      status
    }
  }
}`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceReportsRunSmokeTestTenants.results[0];
  });
};

const TenantMutations = {
  UpdateBillingDetails,
  RunTestTenants,
};

export default TenantMutations;
