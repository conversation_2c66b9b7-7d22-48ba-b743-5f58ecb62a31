import FetchConfig from "./fetchConfig";
import { IFetchConfigReturn } from "../types";
export const GetMissedList = (inTenantId: [String]) => {
  const variables = {
    inTenantId: inTenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation GetMissedList($inTenantId: [String]) {
         statelessServiceReportsGetMissingDataDateInfo(input: {inTenantId: $inTenantId}) {
                     json
                    }
                  }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceReportsGetMissingDataDateInfo.json;
  });
};
