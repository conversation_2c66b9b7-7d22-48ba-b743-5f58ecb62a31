import FetchConfig from "../fetchConfig";
import { IFetchConfigReturn } from "../../types";

const GetStoreListQuery = (
  tenantId: string,
  isDeleted: boolean,
  realm: string
) => {
  const variables = {
    tenantId: tenantId,
    isDeleted: isDeleted ? isDeleted : false,
    storeDmsStatus: "Active",
    realmName: realm,
  };
  return FetchConfig({
    anonymous: false,
    query: `query GetStoreListQuery($tenantId: String!, $isDeleted: Boolean!, $storeDmsStatus: String!, $realmName: String!) {
        statelessServiceBzoStoreDetails(condition: { tenantId: $tenantId, isDeleted: $isDeleted, storeDmsStatus: $storeDmsStatus, realmName: $realmName }) {
          nodes {
          id
          bulkPhase1
          bulkPhase2
          dailyLoad
          dms
          dmsImg
          isConfigured
          isDeleted
          nsQaValidation
          review
          sortOrder
          storeId
          storeLaunched
          storeLaunchedDate
          storeName
          tenantId
          storeDeletedDate
          effectiveDate
          storeDmsStatus
          }
        }
      }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoStoreDetails.nodes;
  });
};

const GetStoreNamesQuery = (tenantId: string) => {
  const variables = {
    tenantId: tenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `query GetStoreNamesQuery($tenantId: String!) {
        statelessServiceBzoStoreDetails(condition: { tenantId: $tenantId }) {
          nodes {
          id
          storeName
          storeId
          }
        }
      }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoStoreDetails.nodes;
  });
};
const GetStoreQuery = (id: number) => {
  const variables = {
    id: id,
  };
  return FetchConfig({
    anonymous: false,
    query: `query GetStoreQuery($id: Int!) {
      statelessServiceBzoStoreDetails(condition: { id: $id }) {
        nodes {
          id
          billingDate
          bulkPhase1
          bulkPhase2
          companyNumber
          dealerAddress
          dailyLoad
          dealerId
          dms
          dmsImg
          enterpriseCode
          infrastructureConfiguration
          isDeleted
          manufacturer
          nsQaValidation
          projectId
          realmName
          review
          sfStoreId
          secondProjectId
          serverName
          sortOrder
          stateCode
          storeDeletedDate
          storeDesc
          storeId
          storeLaunched
          storeLaunchedDate
          storeName
          tenantId
          website
          isLaborGridSubmitted
          isPartsMatrixSubmitted
          bulkPhase1CompletedDate
          bulkPhase1StartDate
          bulkPhase2CompletedDate
          bulkPhase2StartDate
          dailyLoadCompletedDate
          dailyLoadStartDate
          reviewCompletedDate
          reviewStartDate
          nsQaValidationStartDate
          nsQaValidationCompletedDate
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoStoreDetails.nodes[0];
  });
};

const GetQaComments = (storeId: string) => {
  const variables = {
    storeId: storeId,
  };
  return FetchConfig({
    anonymous: false,
    query: `query GetQaComments($storeId: String!) {
  statelessServiceBzoCommentsMasterDetails(condition: {storeId: $storeId})  {
    nodes {
      commentId
      commentText
      createdAt
      createdBy
      isDeleted
      storeId
      storeName
      tenantId
      tenantName
    }
  }
}`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoCommentsMasterDetails.nodes;
  });
};

const GetAllOnboardingStores = () => {
  const variables = {
    storeLaunched: "Completed",
    isTestStore: false,
    storeDmsStatus: "Active",
  };
  return FetchConfig({
    anonymous: false,
    query: `query GetAllOnboardingStores ($storeLaunched: String!, $isTestStore: Boolean!, $storeDmsStatus: String!) {
  statelessServiceBzoStorewiseDetails(
    filter: {storeLaunched: {notEqualTo: $storeLaunched}}
    condition: {isTestStore: $isTestStore, storeDmsStatus: $storeDmsStatus}
  ) {
    nodes {
       agreementDate
      billingDate
      bulkPhase1
      bulkPhase1CompletedDate
      bulkPhase1StartDate
      bulkPhase2
      bulkPhase2CompletedDate
      bulkPhase2StartDate
      companyNumber
      curlRequest
      dailyLoad
      dailyLoadCompletedDate
      dailyLoadStartDate
      dealerAddress
      dealerId
      dms
      dmsImg
      dmsType
      effectiveDate
      enterpriseCode
      groupName
      id
      infrastructureConfiguration
      isConfigured
      isDeleted
      isLaborGridSubmitted
      isPartsMatrixSubmitted
      isTestStore
      laborGridSubmittedDate
      manufacturer
      noofLaborGrids
      noofPartsMatrix
      nsQaValidation
      nsQaValidationCompletedDate
      nsQaValidationStartDate
      partsMatrixSubmittedDate
      projectId
      realmName
      remarks
      review
      reviewCompletedDate
      reviewStartDate
      secondProjectId
      serverName
      sortOrder
      stateCode
      storeDeletedDate
      storeDesc
      storeDmsStatus
      storeId
      storeLaunched
      storeLaunchedDate
      storeName
      tenantId
      tenantName
      website
    }
  }
}
 `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoStorewiseDetails.nodes;
  });
};

const GetAllLaunchedStores = () => {
  const variables = {
    storeLaunched: "Completed",
    isTestStore: false,
    storeDmsStatus: "Active",
  };
  return FetchConfig({
    anonymous: false,
    query: `query GetAllOnboardingStores ($storeLaunched: String!, $isTestStore: Boolean!, $storeDmsStatus: String!) {
    statelessServiceBzoStorewiseDetails(
      filter: {storeLaunched: {equalTo: $storeLaunched}}
      condition: {isTestStore: $isTestStore, storeDmsStatus: $storeDmsStatus}
    ) {
      nodes {
         agreementDate
      billingDate
      bulkPhase1
      bulkPhase1CompletedDate
      bulkPhase1StartDate
      bulkPhase2
      bulkPhase2CompletedDate
      bulkPhase2StartDate
      companyNumber
      curlRequest
      dailyLoad
      dailyLoadCompletedDate
      dailyLoadStartDate
      dealerAddress
      dealerId
      dms
      dmsImg
      dmsType
      effectiveDate
      enterpriseCode
      groupName
      id
      infrastructureConfiguration
      isConfigured
      isDeleted
      isLaborGridSubmitted
      isPartsMatrixSubmitted
      isTestStore
      laborGridSubmittedDate
      manufacturer
      noofLaborGrids
      noofPartsMatrix
      nsQaValidation
      nsQaValidationCompletedDate
      nsQaValidationStartDate
      partsMatrixSubmittedDate
      projectId
      realmName
      remarks
      review
      reviewCompletedDate
      reviewStartDate
      secondProjectId
      serverName
      sortOrder
      stateCode
      storeDeletedDate
      storeDesc
      storeDmsStatus
      storeId
      storeLaunched
      storeLaunchedDate
      storeName
      tenantId
      tenantName
      website
      }
    }
  }
   `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoStorewiseDetails.nodes;
  });
};

const GetFilteredModuleDetailsQuery = (input: any) => {
  // Initialize condition object
  let condition: any = {};

  // Add properties to the condition object based on input values (excluding empty strings)
  if (input.module && input.module.trim() !== "")
    condition.callType = input.module;
  if (input.store && input.store.trim() !== "")
    condition.storeName = input.store;
  if (input.tenant && input.tenant.trim() !== "")
    condition.tenantName = input.tenant;

  // Initialize query variables
  let variables: any = {};

  // Add properties to the variables object based on input values (excluding empty strings)
  if (input.module && input.module.trim() !== "")
    variables.callType = input.module;
  if (input.store && input.store.trim() !== "") variables.storeId = input.store;
  if (input.tenant && input.tenant.trim() !== "")
    variables.tenantId = input.tenant;

  // Construct the query string dynamically based on the presence of variables
  const query = `
    query GetFilteredModuleDetailsQuery (
      ${variables.callType ? "$callType: String," : ""}
      ${variables.storeId ? "$storeId: String," : ""}
      ${variables.tenantId ? "$tenantId: String," : ""}
    ) {
      statelessServiceBzoGetAllTenantsLaborPartsDetails(
        condition: {
          ${variables.callType ? "callType: $callType," : ""}
          ${variables.storeId ? "storeId: $storeId," : ""}
          ${variables.tenantId ? "tenantId: $tenantId," : ""}
        }
      ) {
        nodes {
          gridFor
          calculatedDateFrom
          callType
          dms
          gridPeriod
          gridType
          storeInstallDate
          matrixSource
          storeName
          tenantName
          tenantId
          storeId
        }
      }
    }`;

  return FetchConfig({
    anonymous: false,
    query: query,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetAllTenantsLaborPartsDetails
      .nodes;
  });
};

const GetOpcodesListQuery = (realm: any, storeid: any) => {
  const variables = {
    inStoreId: storeid,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realm,
    query: `mutation GetOpcodesListQuery($inStoreId: String!) {
  statelessCcPhysicalRwGetRoOpcodesList(input: {inStoreId: $inStoreId}) {
    results {
      opcode
      storeId
    }
  }
}`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwGetRoOpcodesList.results;
  });
};

const GetStoreNickName = (storeId: string, realm: string) => {
  const variables = {
    storeid: storeId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realm,
    query: `query GetStoreNickName($storeid: String!) {
  statelessCcPhysicalRoGetStoreNicknameDetails(storeid: $storeid) {
    nodes {
      storeId
      storeName
      storeNickname
    }
  }
}`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRoGetStoreNicknameDetails.nodes[0];
  });
};

const GetStoreWithDiffDmsQuery = (storeId: string) => {
  const variables = {
    storeId: storeId,
  };
  return FetchConfig({
    anonymous: false,
    query: `query statelessServiceBzoStoreDetails($storeId: String!) {
      statelessServiceBzoStoreDetails(condition: { storeId: $storeId }) {
        nodes {
          id
          companyNumber
          dealerId
          dms
          dmsImg
          enterpriseCode
          serverName
          effectiveDate
          storeDmsStatus
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoStoreDetails.nodes;
  });
};

const GetSchedulerDetails = (sfStoreId: any) => {
  const variables = {
    sfStoreid: sfStoreId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation GetSchedulerDetails($sfStoreid: String!) {
  getSchedulerFields(input: {sfStoreid: $sfStoreid}) {
    string
  }
}`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.getSchedulerFields.string;
  });
};

const StoreQueries = {
  GetStoreWithDiffDmsQuery,
  GetStoreListQuery,
  GetStoreNamesQuery,
  GetStoreQuery,
  GetQaComments,
  GetAllOnboardingStores,
  GetFilteredModuleDetailsQuery,
  GetOpcodesListQuery,
  GetStoreNickName,
  GetAllLaunchedStores,
  GetSchedulerDetails,
};

export default StoreQueries;
