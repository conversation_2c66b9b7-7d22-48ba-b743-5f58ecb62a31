import FetchConfig from "../fetchConfig";
import { IFetchConfigReturn } from "../../types";

const LoadBulkData = (input: any) => {
  const variables = {
    dbUsername: input.dbUsername,
    pCallType: input.pCallType,
    pDms: input.pDms,
    pStoreId: input.pStoreId,
    pTenantId: input.pTenantId,
    pUserid: localStorage.getItem("userEmail"),
  };

  // Log bulk data load initiation for debugging
  console.log(`Initiating bulk data load for ${input.pCallType} - Store: ${input.pStoreId}, Tenant: ${input.pTenantId}`);

  return FetchConfig({
    anonymous: false,
    query: `mutation LoadBulkData($dbUsername: String!
        $pCallType: String!
        $pDms: String!
        $pStoreId: String!
        $pTenantId: String!
        $pUserid: String!
        ) {
        statelessServiceBzoBulkDataloadProcess(
          input: {
            dbUsername: $dbUsername
            pCallType: $pCallType
            pDms: $pDms
            pStoreId: $pStoreId
            pTenantId: $pTenantId
            pUserid: $pUserid
          }
        ) {
          clientMutationId
        }
      }
      
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    console.log(`Bulk data load completed for ${input.pCallType} - Store: ${input.pStoreId}`);
    return response.data.statelessServiceBzoBulkDataloadProcess;
  }).catch((error) => {
    console.error(`Bulk data load failed for ${input.pCallType} - Store: ${input.pStoreId}:`, error);
    throw error;
  });
};
const GetBulkLoadStatusLog = (input: any) => {
  const variables = {
    inStoreId: input.storeId,
    inTenantId: input.tenantId,
    inCallType: input.phase,
  };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `mutation GetBulkLoadStatusLog($inStoreId: String!, $inTenantId: String!, $inCallType: String!) {
        statelessServiceBzoGetBulkDataloadProgressLog(
          input: {inStoreId: $inStoreId, inTenantId: $inTenantId, inCallType: $inCallType}
        ) {
          clientMutationId
          bulkDataloadProgressLogs {
            description
            datetime
            status
            error
            source
          }
        }
      }
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetBulkDataloadProgressLog
      .bulkDataloadProgressLogs;
  });
};
const GetDataFeedsQuery = (input: any) => {
  const variables = {
    pAction: input.pAction,
    pTenantId: input.pTenantId,
    pStoreId: input.pStoreId,
    userid: localStorage.getItem("userEmail"),
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation GetDataFeedsQuery(
        $pAction: String!
        $pTenantId: String!
        $pStoreId: String!
        $userid: String!
      ) {
        statelessServiceBzoGetOrUpdateDataFeedDetails(
          input: {
            pAction: $pAction
            pTenantId: $pTenantId
            pStoreId: $pStoreId
            userid: $userid
          }
        ) {
          statelessServiceBzoDataFeedDetails {
            companyNumber
            curlRequest
            dealerAddress
            dealerId
            groupName
            id
            dmsType
            enterpriseCode
            loadAuthApiKey
            postMethod
            projectId
            secondProjectId
            serverName
            stateCode
            storeId
            storeName
            tenantId
            effectiveDate
            storeDmsStatus
          }
        }
      }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetOrUpdateDataFeedDetails
      .statelessServiceBzoDataFeedDetails[0];
  });
};
const GetDataEvaluation = (input: any) => {
  const variables = {
    inStoreId: input.storeId,
    inTenantId: input.tenantId,
    callType: input.callType,
    dms: input.dms,
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.Realm,
    query: `mutation GetDataEvaluation($inStoreId: String!, $inTenantId: String!, $callType: String!, $dms: String!) {
  statelessCcPhysicalRwGetDataVerificationDetails(
    input: {
      inStoreId: $inStoreId
      inTenantId: $inTenantId
      callType: $callType
      dms: $dms
    }
  ) {
    results {
      label
      value
      sortOrder
      isBold
      statusFlag
    }
  }
}`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwGetDataVerificationDetails
      .results;
  });
};
const GetMissingLaborPartsDates = (input: any) => {
  const variables = {
    inStoreId: input.storeId,
    inTenantId: input.tenantId,
    callType: input.callType,
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.Realm,
    query: `mutation GetMissingLaborPartsDates($inStoreId: String!, $inTenantId: String!, $callType: String!) {
  statelessCcPhysicalRwGetMissingLaborPartsDates(
  input: {
      inStoreId: $inStoreId
      inTenantId: $inTenantId
      callType: $callType
    }
  ) {
     results {
      missingDates
      year
    }
  }
}
`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwGetMissingLaborPartsDates.results;
  });
};
const GetIngestTableCloasedDates = (input: any) => {
  const variables = {
    inStoreId: input.storeId,
    inTenantId: input.tenantId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.Realm,
    query: `mutation GetIngestTableCloasedDates($inStoreId: String!, $inTenantId: String!) {
  statelessCcPhysicalRwGetIngestTableCloseddates(
    input: {inStoreId: $inStoreId, inTenantId: $inTenantId}
  ) {
    results {
      closedDates
      tableName
    }
  }
}
`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwGetIngestTableCloseddates.results;
  });
};

const BulkLoadingQueries = {
  LoadBulkData,
  GetBulkLoadStatusLog,
  GetDataFeedsQuery,
  GetDataEvaluation,
  GetMissingLaborPartsDates,
  GetIngestTableCloasedDates,
};

export default BulkLoadingQueries;
