import { useState } from "react";
import TenantQueries from "../service/DataFetchQueries/tenantQueries";

const useAllTenantsList = (record?: any) => {
  const { GetAllTenantsListQuery } = TenantQueries;
  const [allTenantsList, setAllStoresList] = useState<any>([]);
  const [agGridApi, setAgGridApi] = useState<any>();

  const fetchAllTenants = async (status: string) => {
    try {
      const result =
      await GetAllTenantsListQuery();
      if (result) {
        setAllStoresList(result);
      }
    } catch (err) {
      console.error("Error fetching store data:", err);
    }
  };

  const onGridReady = (params: any) => {
    params?.api?.showLoadingOverlay();
    setAgGridApi(params.api);
    fetchAllTenants("onboarding");
  };

  return {
    onGridReady,
    allTenantsList,
  };
};
export default useAllTenantsList;
