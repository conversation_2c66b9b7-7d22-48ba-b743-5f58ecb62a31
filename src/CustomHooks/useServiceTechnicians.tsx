import { useState } from "react";
import { GetAllTechnicians } from "../service/dataFetchQueries";

const useServiceTechnicians = (record?: any) => {
  const [rowData, setRowData] = useState<any[]>();

  const getRowStyle = (params: any) => {
    if (
      params.data.categorized == 0 ||
      (params.data.categorized == 1 && params.data.active == 0)
    ) {
      return { background: "rgb(221, 234, 244)" };
    } else {
      return { background: "rgb(255, 255, 255)" };
    }
  };
  const fetchTechnicians = async () => {
    try {
      const res = await GetAllTechnicians(record.realmName, record.storeId);
      setRowData(res);
    } catch (error) {
      console.error("Error fetching technicians:", error);
      throw error;
    }
  };

  const onRowEditingStarted = (params: any) => {
    params.api.refreshCells({
      columns: ["action"],
      rowNodes: [params.node],
      force: true,
    });
  };

  return {
    getRowStyle,
    fetchTechnicians,
    rowData,
    onRowEditingStarted,
  };
};
export default useServiceTechnicians;
