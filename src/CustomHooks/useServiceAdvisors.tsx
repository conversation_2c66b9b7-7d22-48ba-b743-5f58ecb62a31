import { useState } from "react";
import DataFetchQueries from "../service/dataFetchQueries";

const useServiceAdvisors = (record?: any) => {
  const { GetAllServiceAdvDetails } = DataFetchQueries;
  const [advisorsData, setAdvisorsData] = useState<any[]>();

  const getRowStyle = (params: any) => {
    if (
      params.data.categorized == 0 ||
      (params.data.categorized == 1 && params.data.active == 0)
    ) {
      return { background: "rgb(221, 234, 244)" };
    } else {
      return { background: "rgb(255, 255, 255)" };
    }
  };
  const fetchAdvisors = () => {
    try {
      GetAllServiceAdvDetails(record.realmName, record.storeId).then(
        (res: any) => {
          setAdvisorsData(res);
        }
      );
    } catch (error) {
      console.error("Error fetching Advisors:", error);
      throw error;
    }
  };
  const onRowEditingStarted = (params: any) => {
    params.api.refreshCells({
      columns: ["action"],
      rowNodes: [params.node],
      force: true,
    });
  };

  return {
    getRowStyle,
    fetchAdvisors,
    advisorsData,
    onRowEditingStarted,
  };
};
export default useServiceAdvisors;
