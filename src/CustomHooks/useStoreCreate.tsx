import { useState } from "react";
import { GetManufacturersList } from "../service/dataFetchQueries";
import { SelectInputChoices } from "../types";
import { Constants } from "../utils/constants";
import { InsertStore, StoreOnboardingEmail } from "../service/mutations";
import { useTranslate } from "react-admin";
import { PageRoutes } from "../utils/pageRoutes";
import { useNavigate } from "react-router-dom";
import dayjs from "dayjs";

const useStoreCreate = (props: any) => {
  const { action, record, isStoreLaunched, getStoreQuery } = props;
  const translate = useTranslate();
  const navigate = useNavigate();
  const [estLaunchDate, setEstLaunchDate] = useState<any>(null);
  const [manufacturers, setManufacturers] = useState<SelectInputChoices[]>([]);
  const [isMakesLoading, setIsMakesLoading] = useState<boolean>(true);
  const [updatingBilling, setUpdatingBilling] = useState(false);
  const [showBillingSaveButton, setShowBillingSaveButton] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState<boolean>(false);
  const [statusMessage, setStatusMessage] = useState<any>("");
  const getManufacturers = () => {
    let tempList: SelectInputChoices[] = [];
    GetManufacturersList()
      .then((response) => {
        response.map((item: any) => {
          tempList.push({ id: item.nodeId, name: item.manufacturer });
          return null;
        });
      })
      .then(() => {
        setIsMakesLoading(false);
        setManufacturers(tempList);
      });
  };

  const handleSubmit = async (values: any) => {
    const storeId =
      action === Constants.actions.edit
        ? record.storeId
        : action === "launchDate" || action === "billingdate"
        ? values.storeId
        : `${record.tenantId}_store${values.sortOrder}`;
    const input =
      action === "launchDate" || action === "billingdate"
        ? {
            ...values,
          }
        : {
            ...values,
            realmName: record.realmName,
            dms: values.dms,
            tenantId: record.tenantId,
            storeLaunchedDate: estLaunchDate
              ? dayjs(estLaunchDate).format("MM/DD/YYYY")
              : null,
            storeId:
              action === Constants.actions.edit
                ? record.storeId
                : `${record.tenantId}_store${values.sortOrder}`,
          };
          
    InsertStore(input).then((response) => {
      if (response === Constants.success) {
        // action === "create" &&
        //   InsertWorkPackage(storeId).then((res: any) => {
        //     res.status === 1 && InsertWorkPackageHierarchies(storeId);
        //   });
        const emailQueryInput = {
          pDms: values.dms,
          pNewStore: 0,
          pStore: values.storeName.trim(),
          pStoreId: storeId,
          pTenant:
            action === "launchDate" || action === "billingdate"
              ? values.tenantName
              : record.tenantName,
          pTenantId:
            action === "launchDate" || action === "billingdate"
              ? values.tenantId
              : record.tenantId,
        };
        action === "billingdate" && getStoreQuery();
        action === "create" && StoreOnboardingEmail(emailQueryInput);
        setOpenSnackbar(true);
        setStatusMessage(
          translate(
            action === "launchDate" && isStoreLaunched
              ? "Store has been launched successfully"
              : action === "launchDate" && !isStoreLaunched
              ? "Estimated store launch date updated successfully"
              : action === "billingdate"
              ? "Billing date updated successfully"
              : action === Constants.actions.edit
              ? "SUCCESS_MESSAGES.UPDATE_MESSAGE"
              : "SUCCESS_MESSAGES.CREATE_MESSAGE",
            {
              entityName: Constants.store,
            }
          )
        );
        setUpdatingBilling(false);
        setShowBillingSaveButton(false);
        setTimeout(() => {
          setOpenSnackbar(false);
          action === "launchDate" || action === "billingdate"
            ? navigate(PageRoutes.getStoreDetailsRoute(values.id))
            : action === Constants.actions.edit
            ? navigate(PageRoutes.getStoreDetailsRoute(record.id))
            : navigate(PageRoutes.tenantShowPageRoute(record.id));
        }, 1000);
      }
    });
  };
  return {
    getManufacturers,
    handleSubmit,
    isMakesLoading,
    manufacturers,
    openSnackbar,
    setOpenSnackbar,
    statusMessage,
    setStatusMessage,
    updatingBilling,
    setUpdatingBilling,
    showBillingSaveButton,
    setShowBillingSaveButton,
    estLaunchDate,
    setEstLaunchDate,
  };
};

export default useStoreCreate;
