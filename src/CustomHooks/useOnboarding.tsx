import { useState } from "react";
import { Store } from "../types";
import DataMutationQueries from "../service/mutations";
import { Constants } from "../utils/constants";
import useStoreCreate from "./useStoreCreate";
import dayjs from "dayjs";
import StoreQueries from "../service/DataFetchQueries/storeQueries";
import BulkLoadingQueries from "../service/DataFetchQueries/bulkLoadingQueries";

const useOnboarding: any = (props?: any) => {
  const { record, phase, id, bulkStoreQuery } = props;
  const [rowData, setRowData] = useState([]);
  const [storeRecord, setStoreRecord] = useState<Store>();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [dataFeedDetails, setDataFeedDetails] = useState("");
  const [dataFeedLoading, setDataFeedLoading] = useState<boolean>(true);

  const { handleSubmit } = useStoreCreate({
    action: "launchDate",
    record: record,
    isStoreLaunched: true,
  });
  const [comments, setComments] = useState([]);

  const { GetBulkLoadStatusLog, LoadBulkData, GetDataFeedsQuery } =
    BulkLoadingQueries;
  const { GetStoreQuery, GetQaComments } = StoreQueries;
  const { UpdateBulkStatus } = DataMutationQueries;
  const getStoreQuery = () => {
    GetStoreQuery(Number(id)).then((data: any) => {
      setStoreRecord(data);
      setIsLoading(false);
    });
  };

  const getBulkLoadStatuslog = (props?: any) => {
    GetBulkLoadStatusLog({
      storeId: record.storeId,
      tenantId: record.tenantId,
      phase: phase ? phase : props.phaseType,
    }).then((res: any) => {
      if (res.length !== 0) {
        setRowData(res);
      }
    });
  };

  const handleBulkLoad = (phase: string, status: string) => {
    const bulkInput = {
      dbUsername: record.realmName,
      pCallType: phase,
      pDms: record.dms,
      pStoreId: record.storeId,
      pTenantId: record.tenantId,
    };
    UpdateBulkStatus({ ...bulkInput, status: status })
      .then((res) => {
        (status === "In Progress" || status === "Disabled") && bulkStoreQuery();
        if (res === "Success" && status === "In Progress") {
          (phase === "bulk_phase_1" ||
            phase === "bulk_phase_2" ||
            phase === "daily_load") &&
            LoadBulkData(bulkInput)
              .then((res) => {
                getBulkLoadStatuslog({ phaseType: phase });
                bulkStoreQuery();
              })
              .catch((error) => {
                console.log("error", error);
              });
        } else if (res === "Success" && status === "Completed") {
          let submitdata = {
            ...record,
            storeLaunchedDate: dayjs().format("MM/DD/YYYY"),
          };
          phase === "store_launched" && handleSubmit(submitdata);
          bulkStoreQuery();
        }
      })
      .catch((error) => {
        console.log("error", error);
      });
  };

  const getDataFeed = () => {
    let inputParameters = {
      pAction: Constants.actions.get,
      pTenantId: record.tenantId,
      pStoreId: record.storeId,
    };
    GetDataFeedsQuery(inputParameters).then((data) => {
      localStorage.setItem("dmsType", data.dmsType);
      setDataFeedDetails(data);
      setDataFeedLoading(false);
    });
  };

  const getQaComments = () => {
    GetQaComments(record.storeId).then((res: any) => {
      const sortedComments = res.sort((a: any, b: any) => {
        return (
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
      });
      setComments(sortedComments);
    });
  };

  return {
    getBulkLoadStatuslog,
    handleBulkLoad,
    getStoreQuery,
    rowData,
    storeRecord,
    isLoading,
    dataFeedDetails,
    dataFeedLoading,
    getDataFeed,
    getQaComments,
    comments,
  };
};

export default useOnboarding;
