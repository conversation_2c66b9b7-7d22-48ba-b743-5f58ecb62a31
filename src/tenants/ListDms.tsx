import { Delete } from "@mui/icons-material";
import * as React from "react";
import {
  ImageField,
  Datagrid,
  TextField,
  List,
  useRecordContext,
  Button,
  Confirm,
  useRefresh,
  useTranslate,
  TopToolbar,
  ExportButton,
  downloadCSV,
  useListContext,
} from "react-admin";
import DmsQuickCreate from "./DmsQuickCreate";
import { MutateDms } from "../service/mutations";
import { DmsListRecord } from "../types";
import { Constants } from "../utils/constants";
import { useNavigate } from "react-router-dom";
import { PageRoutes } from "../utils/pageRoutes";
import BackButton from "../Layout/BackButton";
import SnackBarMessage from "../components/SnackBarMessage";
import Loading from "../components/Loading";

const DMSGrid = () => {
  const translate = useTranslate();
  const { isLoading } = useListContext();

  if (isLoading) return <Loading />;
  return (
    <Datagrid
      bulkActionButtons={false}
      sx={{
        "& .RaDatagrid-headerCell": {
          fontWeight: "bold",
        },
      }}
    >
      <TextField source="dms" label={translate("LABELS.DMS_NAME")} />
      <ImageField
        source="dmsImg"
        label={translate("LABELS.DMS_LOGO")}
        sx={{ "& img": { maxWidth: 50, maxHeight: 50, objectFit: "contain" } }}
      />

      <DmsQuickCreate />
      <DeleteDmsButton />
    </Datagrid>
  );
};

const DeleteDmsButton = () => {
  const record: DmsListRecord = useRecordContext();
  const [open, setOpen] = React.useState(false);
  const handleClick = () => setOpen(true);
  const handleDialogClose = () => setOpen(false);
  const refresh = useRefresh();
  const translate = useTranslate();
  const deleteDmsMessage = translate("SUCCESS_MESSAGES.DELETE_MESSAGE", {
    entityName: Constants.dms,
  });
  const [openSnackbar, setOpenSnackbar] = React.useState(false);
  const handleConfirm = () => {
    MutateDms(Constants.actions.delete, record.dms, "").then(
      (result: any) => {
        if (result.string === Constants.success) {
          refresh();
          setOpenSnackbar(true);
        } else {
          // ToDo: Need show Error Message
        }
      }
    );
    setOpen(false);
  };

  return (
    <>
      <Button onClick={handleClick} label={"ra.action.delete"}>
        <Delete />
      </Button>
      <Confirm
        isOpen={open}
        // loading={isLoading}
        title={`Delete ${record?.dms}`}
        content={translate("DIALOG_BOX.DMS_DELETE")}
        onConfirm={handleConfirm}
        onClose={handleDialogClose}
      />
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={deleteDmsMessage}
      />
    </>
  );
};
const DmsListActions = () => {
  const navigate = useNavigate();
  const handleClick = () => {
    navigate(PageRoutes.statelessServiceBzoTenants);
  };
  return (
    <TopToolbar sx={{ width: "100%", justifyContent: "space-between" }}>
      <BackButton />
      <ExportButton
        exporter={(records: any) => {
          downloadCSV(records, "DMS List");
        }}
      />
    </TopToolbar>
  );
};

export const DMSListGrid = () => {
  return (
    <List
      actions={<DmsListActions />}
      sx={{
        "& .css-vunk5k-MuiToolbar-root-RaListToolbar-root": {
          justifyContent: "start",
        },
      }}
    >
      <DMSGrid />
    </List>
  );
};
