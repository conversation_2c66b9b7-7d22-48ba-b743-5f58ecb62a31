import React from "react";
import { Box, Tooltip, Typography, useTheme } from "@mui/material";
import { AgGridReact } from "ag-grid-react";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { Constants } from "../../utils/constants";
import AllTenantsGridDefs from "./AllTenantsGridDefs";
import useAllTenantsList from "../../CustomHooks/useAllTenantsList";
const AllTenantsList = () => {
  const { columnDefs, defaultColDef, onBtnExport } = AllTenantsGridDefs();
  const { onGridReady, allTenantsList } = useAllTenantsList();
  const theme = useTheme();
  return (
    <Box sx={{ paddingX: "10px", mt: "15px", width: "100%" }}>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Box></Box>
        <Typography
          sx={{
            fontSize: "18px",
            fontWeight: "bold",
            color: Constants.colors.modalHeading,
          }}
        >
          All Tenants
        </Typography>
        <Tooltip title="Export To Excel">
          <div>
            <FileDownloadIcon
              onClick={onBtnExport}
              style={{ color: theme.palette.primary.main }}
            />
          </div>
        </Tooltip>
      </Box>

      <Box sx={{ width: "100%", marginTop: "15px" }}>
        <div
          className={Constants.ag_grid_theme}
          style={{ height: "85vh", marginTop: 2, maxWidth: "100vw" }}
        >
          <AgGridReact
            columnDefs={columnDefs}
            rowData={allTenantsList}
            defaultColDef={defaultColDef}
            onGridReady={onGridReady}
            pagination={true}
          />
        </div>
      </Box>
    </Box>
  );
};

export default AllTenantsList;
