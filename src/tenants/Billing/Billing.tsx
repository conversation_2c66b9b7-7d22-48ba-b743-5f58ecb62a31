import { <PERSON>, Button, Typography } from "@mui/material";
import { DatePicker, Divider, Input, InputNumber } from "antd";
import React, {
  useEffect,
  useState,
} from "react";
import ArrowRightIcon from "@mui/icons-material/ArrowRight";
import { useRecordContext } from "react-admin";
import Loading from "../../components/Loading";
import useBilling from "../../CustomHooks/useBilling";
import { Subscription, Tenant } from "../../types";
import CancelModal from "./CancelModal";
import dayjs from "dayjs";
import SnackBarMessage from "../../components/SnackBarMessage";

const Billing = () => {
  const record: Tenant = useRecordContext();
  const [cancelId, setCancelId] = useState<string>("");
  const [invoiceDateMsg, setInvoiceDateMsg] = useState<any>({
    id: "",
    msg: "",
  });
  const {
    getSubscriptionList,
    isLoading,
    subscriptionDetails,
    disableDates,
    handleSave,
    handleInputChange,
    setInvoiceDate,
    cancelledDate,
    setCancelledDate,
    invoiceDate,
    statusChanged,
    openConfirmModal,
    setOpenConfirmModal,
    modal,
    setModal,
    setOpenSnackbar,
    openSnackbar,
    statusMessage,
    statusMessageType,
    billingEdited,
    setBillingEdited,
  } = useBilling(record);

  useEffect(() => {
    getSubscriptionList();
  }, [statusChanged]);

  return isLoading ? (
    <Loading />
  ) : (
    <Box sx={{ display: "flex", marginTop: "24px", flexDirection: "column" }}>
      <Box>
        {subscriptionDetails &&
          subscriptionDetails.map((item: Subscription) => {
            return (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  backgroundColor: "#EAEAEA",
                  padding: "16px",
                  mb: 1,
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    height: "60px",
                    alignItems: "end",
                    width: "1000px",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      flex: 2,
                      alignItems: "space-between",
                      paddingLeft: "4px",
                      height: "100%",
                      flexDirection: "column",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography
                      sx={{
                        marginLeft: "-20px",
                        display: "flex",
                        justifyContent: "center",
                        color: "white",
                        padding: "0 18px",
                        fontSize: "12px",
                        width: "85px",
                        fontWeight: 500,
                        fontFamily: "Montserrat",
                        backgroundColor:
                          item.subscriptionStatus === "Active"
                            ? "green"
                            : item.subscriptionStatus === "Cancelled"
                            ? "red"
                            : "orange",
                      }}
                    >
                      {item.subscriptionStatus}
                    </Typography>
                    <Typography
                      sx={{
                        alignSelf: "center",
                        fontSize: "14px",
                        fontWeight: "bold",
                        fontFamily: "Montserrat",
                      }}
                    >
                      Subscription {item.subscriptionId}
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      width: "300px",
                      marginX: 1,
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <Typography
                        color={"grey"}
                        fontWeight={500}
                        fontSize={"12px"}
                        fontFamily={"Montserrat"}
                      >
                        FOPC Monthly Invoice Date
                      </Typography>
                    </Box>
                    <DatePicker
                      disabled={item.subscriptionStatus === "Cancelled"}
                      placeholder="Select Date"
                      format={"MM/DD/YYYY"}
                      disabledDate={disableDates}
                      value={invoiceDate[item.subscriptionId]}
                      onChange={(date, dateString) => {
                        date && setInvoiceDateMsg({
                          id: '',
                          msg: ''})
                        date && setBillingEdited(true);
                        setInvoiceDate((prevDates) => ({
                          ...prevDates,
                          [item.subscriptionId]: date ? dayjs(date) : null,
                        }));
                        handleInputChange(
                          dateString,
                          "invoiceDate",
                          item.subscriptionId
                        );
                      }}
                    ></DatePicker>
                  </Box>
                  <Box
                    sx={{
                      display: "flex",
                      flex: 2,
                      // justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    {item.subscriptionStatus === "Active" ? (
                      <Button
                        sx={{
                          marginX: 1,
                          textTransform: "none",
                          fontFamily: "Montserrat",
                          backgroundColor: "#59aaaa",
                          color: "white",
                          flex: 1,
                          maxWidth: "150px",
                          padding: "5px",
                          "&:hover": {
                            backgroundColor: "#208f8f",
                          },
                        }}
                        onClick={() => {
                          setCancelId(item.subscriptionId);
                          setOpenConfirmModal(true);
                          setModal("cancel");
                        }}
                      >
                        Cancel Subscription
                      </Button>
                    ) : (
                      <Box sx={{ display: "flex", alignItems: "end" }}>
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: "column",
                            flex: 1,
                            maxWidth: "150px",
                            marginX: 1,
                            // width: "211px",
                          }}
                        >
                          <Typography
                            color={"grey"}
                            fontWeight={500}
                            fontSize={"12px"}
                            fontFamily={"Montserrat"}
                          >
                            Cancellation Effective
                          </Typography>
                          <DatePicker
                            placeholder="Select Date"
                            format={"MM/DD/YYYY"}
                            // disabled={item.subscriptionStatus === "Cancelled"}
                            disabled
                            value={cancelledDate[item.subscriptionId]}
                            onChange={(date, dateString) => {

                              setCancelledDate((prevDates) => ({
                                ...prevDates,
                                [item.subscriptionId]: date ? dayjs(date): null,
                              }));
                              handleInputChange(
                                dateString,
                                "cancelledDate",
                                item.subscriptionId
                              );
                            }}
                          ></DatePicker>
                        </Box>
                        {item.subscriptionStatus === "Review" &&
                        item.cancelledBy === localStorage.getItem("user") ? (
                          <Button
                            sx={{
                              marginX: 1,
                              textTransform: "none",
                              fontFamily: "Montserrat",
                              backgroundColor: "#59aaaa",
                              color: "white",
                              flex: 1,
                              maxWidth: "150px",
                              padding: "5px",
                              "&:hover": {
                                backgroundColor: "#208f8f",
                              },
                            }}
                            onClick={() => {
                              setCancelId(item.subscriptionId);
                              setOpenConfirmModal(true);
                              setModal("cancelReview");
                              handleInputChange(
                                "cancelReview",
                                "",
                                item.subscriptionId
                              );
                            }}
                          >
                            Cancel Review
                          </Button>
                        ) : item.subscriptionStatus === "Review" &&
                          item.cancelledBy !== localStorage.getItem("user") ? (
                          <Box sx={{ display: "flex", height: "fit-content" }}>
                            <Button
                              sx={{
                                marginX: 1,
                                textTransform: "none",
                                fontFamily: "Montserrat",
                                backgroundColor: "#59aaaa",
                                color: "white",
                                flex: 1,
                                maxWidth: "150px",
                                minWidth: "100px",
                                padding: "5px",
                                "&:hover": {
                                  backgroundColor: "#208f8f",
                                },
                              }}
                              onClick={() => {
                                if (item.invoiceDate) {
                                  setCancelId(item.subscriptionId);
                                  setOpenConfirmModal(true);
                                  setModal("cancelApprove");
                                  handleInputChange(
                                    "cancelApprove",
                                    "",
                                    item.subscriptionId
                                  );
                                } else {
                                  setInvoiceDateMsg({
                                    id: item.subscriptionId,
                                    msg: "Please provide invoice date ",
                                  });
                                }
                              }}
                            >
                              Approve
                            </Button>
                            <Button
                              sx={{
                                marginX: 1,
                                textTransform: "none",
                                fontFamily: "Montserrat",
                                backgroundColor: "#59aaaa",
                                color: "white",
                                flex: 1,
                                maxWidth: "150px",
                                minWidth: "100px",
                                padding: "5px",
                                "&:hover": {
                                  backgroundColor: "#208f8f",
                                },
                              }}
                              onClick={() => {
                                setCancelId(item.subscriptionId);
                                setOpenConfirmModal(true);
                                setModal("cancelDeny");
                                handleInputChange(
                                  "cancelDeny",
                                  "",
                                  item.subscriptionId
                                );
                              }}
                            >
                              Deny
                            </Button>
                          </Box>
                        ) : null}
                      </Box>
                    )}
                  </Box>
                </Box>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    width: "1000px",
                    mt: 1,
                  }}
                >
                  <Box sx={{ width: "300px" }}>
                  <Typography sx={{ fontSize: "14px", color: "red", mb: "4px", ml: "6px" }}>
                    {invoiceDateMsg.id === item.subscriptionId &&
                      invoiceDateMsg.msg}
                  </Typography>
                    {item.storeName?.map((store: string) => {
                      return (
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <ArrowRightIcon
                            sx={{ fontSize: "16px", color: "grey" }}
                          />
                          <Typography
                            sx={{
                              fontSize: "12px",
                              fontWeight: 500,
                              fontFamily: "Montserrat",
                              color: "primary.main",
                            }}
                          >
                            {store}
                          </Typography>
                        </Box>
                      );
                    })}
                  </Box>
                </Box>
              </Box>
            );
          })}
      </Box>
      <Divider style={{ margin: "12px 0" }} />
      <Box sx={{ display: "flex", justifyContent: "end", padding: "0px 16px" }}>
        <Button
          disabled={!billingEdited}
          color="primary"
          variant="contained"
          size="small"
          sx={{
            margin: 0,
            width: "100px",
            textTransform: "none",
            fontFamily: "Montserrat",
          }}
          onClick={() => handleSave("update")}
        >
          Save
        </Button>
      </Box>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={statusMessageType}
      />
      <CancelModal
        openConfirmModal={openConfirmModal}
        setOpenConfirmModal={setOpenConfirmModal}
        cancelId={cancelId}
        cancelledDate={cancelledDate}
        setCancelledDate={setCancelledDate}
        handleInputChange={handleInputChange}
        handleSave={handleSave}
        modal={modal}
      />
    </Box>
  );
};

export default Billing;
