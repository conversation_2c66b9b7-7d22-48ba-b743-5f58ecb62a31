import React from "react";
import {
  Avatar,
  Box,
  Button,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Tooltip,
  Typography,
} from "@mui/material";
import { useState, useEffect } from "react";
import {
  RecordContextProvider,
  useRecordContext,
  useTranslate,
} from "react-admin";
import { Link as RouterLink, useNavigate } from "react-router-dom";
import { Store } from "../types";
import { PageRoutes } from "../utils/pageRoutes";
import StoreIcon from "@mui/icons-material/Store";
import DataMutationQueries from "../service/mutations";
import SnackBarMessage from "../components/SnackBarMessage";
import moment from "moment";
import DeleteStore from "../stores/storeDeleteButton";
import StoreQueries from "../service/DataFetchQueries/storeQueries";
import dayjs from "dayjs";

const StoresIterator = () => {
  const record = useRecordContext();
  const { GetStoreListQuery } = StoreQueries;
  const translate = useTranslate();
  const [storeData, setStoreData] = useState<Store[]>();
  const [isStoreDeleted, setIsStoreDeleted] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState("");
  const dashboardCard = JSON.parse(localStorage.getItem("dashboard") || "[]");
  const getStoreData = () => {
    GetStoreListQuery(
      record.tenantId,
      dashboardCard.filter === "cancelled" ? true : false,
      record.realmName
    )
      .then((data) => {
        const sortedStoreData =
          data && [...data].sort((a, b) => a.sortOrder - b.sortOrder);
        setStoreData(sortedStoreData);
      })
      .finally(() => {
        setTimeout(() => {
          setOpenSnackbar(false);
        }, 1000);
      });
    setIsStoreDeleted(false);
  };
  useEffect(() => {
    getStoreData();
  }, []);
  useEffect(() => {
    if (isStoreDeleted) {
      getStoreData();
      setStatusMessage("Store deleted successfully");
      setOpenSnackbar(true);
    }
  }, [isStoreDeleted]);

  const checkStatus = (storeStatus: any) => {
    return storeStatus.bulkPhase1 === "Inactive" ||
      storeStatus.bulkPhase1 === "Active"
      ? "Onboarding not started"
      : storeStatus.bulkPhase1 === "In Progress"
      ? "Bulk load phase 1 in progress"
      : storeStatus.bulkPhase1 === "Failed"
      ? "Bulk load phase 1 failed"
      : storeStatus.bulkPhase1 === "Completed" &&
        storeStatus.bulkPhase2 === "Inactive"
      ? "Bulk load phase 1 completed. Opcode categorization pending."
      : storeStatus.bulkPhase1 === "Completed" &&
        storeStatus.bulkPhase2 === "Active"
      ? "Opcode categorization completed"
      : storeStatus.bulkPhase2 === "In Progress"
      ? "Bulk load phase 2 in progress"
      : storeStatus.bulkPhase2 === "Failed"
      ? "Bulk load phase 2 failed"
      : storeStatus.bulkPhase2 === "Completed" &&
        storeStatus.dailyLoad === "Active"
      ? "Bulk load phase 2 completed"
      : storeStatus.dailyLoad === "Failed"
      ? "Daily load failed"
      : storeStatus.dailyLoad === "In Progress"
      ? "Daily Load in progress"
      : storeStatus.dailyLoad === "Completed" &&
        storeStatus.nsQaValidation === "Active" &&
        storeStatus.review === "Active"
      ? "Daily load completed. NS QA validation and review pending."
      : storeStatus.nsQaValidation === "Active" &&
        storeStatus.review === "In Progress"
      ? "NS QA validation pending and review in progress."
      : storeStatus.nsQaValidation === "Active" &&
        storeStatus.review === "Completed" &&
        storeStatus.storeLaunched === "Active"
      ? "NS QA validation pending and review completed. Store is ready to launch."
      : storeStatus.nsQaValidation === "In Progress" &&
        storeStatus.review === "Active"
      ? "NS QA validation in progress. Review pending."
      : storeStatus.nsQaValidation === "In Progress" &&
        storeStatus.review === "In Progress"
      ? "NS QA validation and review in progress."
      : storeStatus.nsQaValidation === "In Progress" &&
        storeStatus.review === "Completed" &&
        storeStatus.storeLaunched === "Active"
      ? "NS QA validation in progress and review completed. Store is ready to launch."
      : storeStatus.nsQaValidation === "Completed" &&
        storeStatus.review === "Active"
      ? "NS QA validation completed and review pending."
      : storeStatus.nsQaValidation === "Completed" &&
        storeStatus.review === "In Progress"
      ? "NS QA validation completed and review in progress."
      : storeStatus.nsQaValidation === "Completed" &&
        storeStatus.review === "Completed" &&
        storeStatus.storeLaunched === "Active"
      ? "NS QA validation and store review completed. Store is ready to launch."
      : storeStatus.storeLaunched === "Completed"
      ? "Store launched"
      : "";
  };

  return (
    <Box>
      <List>
        <Box
          display="flex"
          justifyContent={"space-between"}
          alignItems={"center"}
        >
          <Typography
            variant="h6"
            marginLeft={"10px"}
            fontWeight={600}
            display={"flex"}
          >
            {translate("TITLES.SINGLE_STORES")}
          </Typography>
          {!record.cancelled && <CreateRelatedStoreButton />}
        </Box>

        {storeData && storeData.length !== 0 ? (
          storeData.map((stores: any) => (
            <RecordContextProvider key={stores.id} value={stores}>
              <Box display={"flex"}>
                <ListItem
                  component={stores.isDeleted ? "div" : RouterLink}
                  to={
                    stores.isDeleted
                      ? undefined
                      : PageRoutes.getStoreDetailsRoute(stores.id)
                  }
                  sx={{
                    cursor: stores.isDeleted ? "default" : "pointer",
                    flex: 17,
                  }}
                >
                  <Tooltip title={stores.dms}>
                    <Avatar
                      src={stores.dmsImg}
                      alt={stores.dms}
                      sx={{ marginX: 1, width: "50px", height: "50px" }}
                    />
                  </Tooltip>
                  <ListItemText
                    primary={
                      <Typography color={stores.isDeleted ? "grey" : "#003d6b"}>
                        {stores.storeName}
                      </Typography>
                    }
                    secondary={
                      stores.isDeleted ? (
                        <Typography
                          sx={{
                            marginRight: "10px",
                            display: "flex",
                            alignItems: "baseline",
                          }}
                          fontSize={"0.875rem"}
                          color={"red"}
                        >
                          This store was deleted on{" "}
                          {dayjs(stores.storeDeletedDate).format("MM/DD/YYYY")}.
                        </Typography>
                      ) : (
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "baseline",
                            flexDirection: "column",
                          }}
                        >
                          {stores.storeLaunchedDate && (
                            <Typography
                              sx={{ marginRight: "10px" }}
                              fontSize={"0.875rem"}
                            >
                              {stores.storeLaunched === "Completed"
                                ? "Launched date:"
                                : "Estimated launch date:"}{" "}
                              {moment(stores.storeLaunchedDate).format(
                                "MM/DD/YYYY"
                              )}
                            </Typography>
                          )}

                          <Typography
                            sx={{ marginRight: "10px" }}
                            fontSize={"0.875rem"}
                          >
                            {checkStatus(stores)}
                          </Typography>
                        </Box>
                      )
                    }
                  />
                </ListItem>
                {!stores.isDeleted && (
                  <StoreConfiguration
                    getStoreData={getStoreData}
                    tenantConfigureStatus={record.isConfigured}
                  />
                )}
                {/* {!stores.isDeleted && (
                  <Box
                    sx={{
                      // marginRight: 1, width: "9rem" ,
                      display: "flex",
                      justifyContent: "flex-end",
                      alignSelf: "center",
                      flex: 1,
                    }}
                  >
                    <DeleteStore
                      page={"tenantPage"}
                      setIsStoreDeleted={setIsStoreDeleted}
                    />
                    <SnackBarMessage
                      onClose={() => setOpenSnackbar(false)}
                      open={openSnackbar}
                      message={statusMessage}
                    />
                  </Box>
                )} */}
              </Box>
            </RecordContextProvider>
          ))
        ) : (
          <Box
            sx={{ display: "flex", justifyContent: "center", margin: "2rem" }}
          >
            <Typography color={"grey"}>
              {translate("MESSAGES.NO_STORES")}
            </Typography>
          </Box>
        )}
      </List>
    </Box>
  );
};

const CreateRelatedStoreButton = () => {
  const record = useRecordContext();
  const translate = useTranslate();
  return (
    <Button
      component={RouterLink}
      to={PageRoutes.createStoreMaster}
      state={{ record: record, action: "create" }}
      color="primary"
      variant="contained"
      size="small"
      startIcon={<StoreIcon />}
      sx={{ m: 2, width: "10rem" }}
    >
      {translate("BUTTONS.Add_STORE")}
    </Button>
  );
};

const StoreConfiguration = (props: any) => {
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [storeConfigured, setStoreConfigured] = useState<boolean>(false);
  const [storeConfiguring, setStoreConfiguring] = useState<boolean>(false);
  const [statusMessage, setStatusMessage] = useState("");
  const { getStoreData, tenantConfigureStatus } = props;
  const record = useRecordContext();
  const translate = useTranslate();
  const navigate = useNavigate();

  const { ConfigureStore } = DataMutationQueries;
  const handleConfigureStore = () => {
    setStoreConfiguring(true);
    ConfigureStore(record.id).then(async (res) => {
      const entries = Object.entries(res.Add_user_to_group_response);
      const formattedEntries = entries
        .map(([key, value]) => `${key}: ${value}`)
        .join(", ");
      let msg = `Add user to group response: ${formattedEntries}`;
      setOpenSnackbar(true);
      setStatusMessage(translate(msg));
      getStoreData();
    });
  };
  useEffect(() => {
    record.isConfigured && setStoreConfiguring(false);
    setStoreConfigured(record.isConfigured);
  }, [record]);
  return (
    <Box
      display={"flex"}
      alignItems={"center"}
      justifyContent={"flex-end"}
      sx={{ flex: 2 }}
    >
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
      />
      {!storeConfigured ? (
        <Button
          sx={{ height: "fit-content", marginRight: 1, width: "9rem" }}
          onClick={() => {
            handleConfigureStore();
          }}
          disabled={!tenantConfigureStatus}
        >
          {storeConfiguring ? (
            <CircularProgress size={20} />
          ) : (
            " Configure Store "
          )}
        </Button>
      ) : null}
    </Box>
  );
};

export default StoresIterator;
