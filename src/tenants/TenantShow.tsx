import * as React from "react";
import { useState, ChangeEvent, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Tabs,
  Tab,
  Divider,
  CircularProgress,
  Switch,
  FormGroup,
  FormControlLabel,
  Avatar,
  Tooltip,
  Link,
  Alert,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import "../style.css";
import StoresIterator from "./StoresIterator";
import { useParams } from "react-router-dom";
import { GroupIterator } from "../GroupDetails/GroupIterator";
import { TabPanel } from "../Layout/TabPanel";
import { GroupStore, TenantRecord } from "../types";
import { RecordContextProvider, useTranslate } from "react-admin";
import DataFetchQueries, {
  GetGroupingDetailsQuery,
} from "../service/dataFetchQueries";
import { ArrowBack } from "@mui/icons-material";
import { PageRoutes } from "../utils/pageRoutes";
import DataMutationQueries from "../service/mutations";
import SnackBarMessage from "../components/SnackBarMessage";
import Loading from "../components/Loading";
import moment from "moment";
import ActivityLog from "../ActivityLog/ActivityLog";
import TenantQueries from "../service/DataFetchQueries/tenantQueries";
import Billing from "./Billing/Billing";
import BugStatus from "./BugStatus/BugStatus";

export const TenantShow = () => {
  const { TenantShowQuery } = TenantQueries;
  const { EnableDisableTenant, ConfigureTenant } = DataMutationQueries;
  const { GetRealmStatus } = DataFetchQueries;
  const navigate = useNavigate();
  const { id } = useParams();
  const translate = useTranslate();
  const [tabValue, setTabValue] = useState<number>(0);
  const [realmStatus, setRealmStatus] = useState<string>("");
  const [tenantData, setTenantData] = useState<TenantRecord>();
  const [groupData, setGroupData] = useState<GroupStore[]>();
  const [isConfigured, setIsConfigured] = useState<boolean>(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState("");
  const [tenantConfiguring, setTenantConfiguring] = useState<boolean>(false);
  const [checked, setChecked] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const dashboardCard = JSON.parse(localStorage.getItem("dashboard") || "[]");
  const handleTabChange = (event: ChangeEvent<{}>, newValue: number) => {
    setTabValue(newValue);
  };

  const getTenantData = () => {
    TenantShowQuery(Number(id)).then(async (data) => {
      setTenantData(data);
      localStorage.setItem("tenant-id", data.id.toString());
      data.isConfigured && getRealmStatus();
      setIsConfigured(data.isConfigured);
      setTenantConfiguring(false);
      setIsLoading(false);
    });
  };

  useEffect(() => {
    getTenantData();
  }, []);

  const getRealmStatus = () => {
    GetRealmStatus(Number(id)).then((res) => {
      res === "Enabled" && setChecked(true);
      setRealmStatus(res);
    });
  };

  const getGroupingDetalis = () => {
    tenantData &&
      GetGroupingDetailsQuery(tenantData.tenantId).then((data) => {
        setGroupData(data);
      });
  };
  const handleBackClick = (url: any) => {
    navigate(url);
  };

  const handleEnableDisableBtn = () => {
    setChecked(!checked);
    EnableDisableTenant(Number(id), realmStatus).then((res) => {
      if (res.disableRealm?.string === "Success") {
        setOpenSnackbar(true);
        setStatusMessage(translate("SUCCESS_MESSAGES.TEN_DISABLE"));
      } else if (res.enableRealm?.string === "Success") {
        setOpenSnackbar(true);
        setStatusMessage(translate("SUCCESS_MESSAGES.TEN_ENABLE"));
      }
      getRealmStatus();
      //Notify tenant disabled or enabled
    });
  };
  const handleTenantConfigure = () => {
    setTenantConfiguring(true);
    ConfigureTenant(Number(id))
      .then(async (res) => {
        setTenantConfiguring(false);
        if (res.realm_creation_response === "success") {
          setOpenSnackbar(true);
          setStatusMessage(
            translate("SUCCESS_MESSAGES.CONFIG_COMPLETE", {
              entityName: "Tenant",
            })
          );
          getTenantData();
        } else {
          setOpenSnackbar(true);
          setStatusMessage(res.realm_creation_response);
          getTenantData();
        }
      })
      .catch((err: any) => {});
  };

  if (isLoading) return <Loading />;

  return (
    <RecordContextProvider value={tenantData}>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
      />
      <Button
        startIcon={<ArrowBack />}
        color="primary"
        onClick={() =>
          handleBackClick(
            PageRoutes.statelessServiceBzoTenants +
              "/dashboard/" +
              dashboardCard.filter
          )
        }
        sx={{ mt: 1.5 }}
      >
        BACK
      </Button>
      <Box mt={1} display="flex">
        <Box flex="1">
          {tenantData && (
            <Card>
              <CardContent>
                <Box display="flex">
                  <Box flex="1" display={"flex"}>
                    <Tooltip title={tenantData.tenantName}>
                      <Avatar
                        src={tenantData.tenantImg}
                        alt={tenantData.tenantName}
                        sx={{ marginX: 1, width: "50px", height: "50px" }}
                      />
                    </Tooltip>
                    <Box sx={{ ml: "4px" }}>
                      <Typography variant="h5">
                        {tenantData.tenantName}
                      </Typography>
                      {tenantData.subDomain && (
                        <Typography variant="subtitle2">
                          FOPC website url:{" "}
                          <Link
                            href={`https://${tenantData.subDomain}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            underline="hover"
                          >
                            {tenantData.subDomain}
                          </Link>
                        </Typography>
                      )}
                      {tenantData.agreementDate && (
                        <Typography variant="subtitle2">
                          Signed On:{" "}
                          {moment(tenantData.agreementDate).format("MM/DD/YY")}
                        </Typography>
                      )}
                      {tenantData.tenantDeletedDate && (
                        <Typography variant="subtitle2" color="red">
                          Cancelled On:{" "}
                          {moment(tenantData.tenantDeletedDate).format(
                            "MM/DD/YY"
                          )}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                  {!isConfigured && !tenantData.cancelled && (
                    <Button
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        width: "10rem",
                      }}
                      onClick={() => handleTenantConfigure()}
                    >
                      {tenantConfiguring ? (
                        <CircularProgress size={20} />
                      ) : (
                        "Configure Tenant"
                      )}
                    </Button>
                  )}
                  {isConfigured && !tenantData.cancelled && (
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Switch
                            onChange={() => {
                              handleEnableDisableBtn();
                            }}
                            checked={checked}
                          />
                        }
                        label={
                          <Typography
                            fontSize={"13px"}
                            fontWeight={"bold"}
                            color={checked ? "primary" : "grey"}
                          >
                            {checked ? "ENABLED" : "DISABLED"}
                          </Typography>
                        }
                      />
                    </FormGroup>
                  )}
                </Box>
                <Tabs
                  value={tabValue}
                  indicatorColor="primary"
                  textColor="primary"
                  onChange={handleTabChange}
                >
                  <Tab
                    label={translate("TABS.STORES")}
                    style={{ width: "15%" }}
                  />
                  <Tab
                    label={translate("Billing")}
                    style={{ width: "15%" }}
                  />
                  <Tab label={"Activity Log"} style={{ width: "15%" }} />
                  <Tab
                    label={translate("Bug Status")}
                    style={{ width: "15%" }}
                  />
                </Tabs>
                <Divider />
                <TabPanel value={tabValue} index={0}>
                  <Alert
                    severity={
                      tenantData.infrastructureConfiguration === "Completed"
                        ? "success"
                        : tenantData.infrastructureConfiguration ===
                          "In Progress"
                        ? "info"
                        : "warning"
                    }
                    sx={{ mt: 1 }}
                  >
                    Infrastructure configuration{" "}
                    {tenantData.infrastructureConfiguration.toLowerCase()}.
                  </Alert>
                  <StoresIterator />
                  {dashboardCard.filter !== "cancelled" && (
                    <GroupIterator
                      getGroupingDetalis={getGroupingDetalis}
                      groupData={groupData}
                    />
                  )}
                </TabPanel>
                <TabPanel value={tabValue} index={1}>
                  <Billing />
                </TabPanel>
                <TabPanel value={tabValue} index={2}>
                  <ActivityLog spec={"tenantSpecific"} />
                </TabPanel>
                <TabPanel value={tabValue} index={3}>
                  <BugStatus />
                </TabPanel>
              </CardContent>
            </Card>
          )}
        </Box>
      </Box>
    </RecordContextProvider>
  );
};
