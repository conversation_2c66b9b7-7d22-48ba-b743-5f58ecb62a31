import React, { useEffect, useState } from "react";
import { Box } from "@mui/material";
import useBugStatus from "../../CustomHooks/useBugStatus";
import { useRecordContext } from "react-admin";
import { PieChartComponent } from "./PieChart";

const BugStatus = () => {
  const record = useRecordContext();
  const [hoveredIndexOpen, setHoveredIndexOpen] = useState<number | null>(null);
  const [hoveredIndexClosed, setHoveredIndexClosed] = useState<number | null>(
    null
  );
  const [hoveredIndexPriority, setHoveredIndexPriority] = useState<number | null>(null);
  const [hoveredData, setHoveredData] = useState<number | null>(null);
  const [hoveredPerformance, setHoveredPerformance] = useState<number | null>(null);
  const [hoveredUi, setHoveredUi] = useState<number | null>(null);

  const {
    getBugStatus,
    openBugs,
    closedBugs,
    priorityLevel,
    allBugs,
    dataRelated,
    performanceRelated,
    uiRelated,
  } = useBugStatus({
    record,
  });

  useEffect(() => {
    getBugStatus();
  }, []);
  return (
    <>
      <Box
        sx={{
          paddingX: "10px",
          mt: "15px",
          width: "100%",
          display: "flex",
          justifyContent: "space-between",
          flexWrap: "wrap",
        }}
      >
        <PieChartComponent
          hoveredIndex={hoveredIndexOpen}
          setHoveredIndex={setHoveredIndexOpen}
          data={openBugs}
          allBugs={allBugs}
          category="open"
        />

        <PieChartComponent
        hoveredIndex={hoveredIndexClosed}
        setHoveredIndex={setHoveredIndexClosed}
          data={closedBugs}
          category="closed"
          allBugs={allBugs}
        />
        <PieChartComponent
        hoveredIndex={hoveredIndexPriority}
        setHoveredIndex={setHoveredIndexPriority}
          data={priorityLevel}
          category="priority"
          allBugs={allBugs}
        />
        <PieChartComponent
        hoveredIndex={hoveredData}
        setHoveredIndex={setHoveredData}
          data={dataRelated}
          category="data"
          allBugs={allBugs}
        />
        <PieChartComponent
        hoveredIndex={hoveredPerformance}
        setHoveredIndex={setHoveredPerformance}
          data={performanceRelated}
          category="performance"
          allBugs={allBugs}
        />
        <PieChartComponent
        hoveredIndex={hoveredUi}
        setHoveredIndex={setHoveredUi}
          data={uiRelated}
          category="ui"
          allBugs={allBugs}
        />
      </Box>
    </>
  );
};

export default BugStatus;
