import * as React from "react";
import { useState } from "react";
import {
  Paper,
  Typography,
  Link as MuiLink,
  Box,
  IconButton,
  CircularProgress,
  AvatarGroup,
  Tooltip,
} from "@mui/material";
import StoreMallDirectoryIcon from "@mui/icons-material/StoreMallDirectory";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import { Link, useNavigate } from "react-router-dom";
import { Avatar } from "@mui/material";
import { TenantAvatar } from "./TenantAvatar";
import { Tenant } from "../types";
import { Constants } from "../utils/constants";
import {
  useCreatePath,
  useRecordContext,
  Confirm,
  useRefresh,
  useTranslate,
} from "react-admin";
import { DeleteTenant } from "../service/mutations";
import { PageRoutes } from "../utils/pageRoutes";
import SnackBarMessage from "../components/SnackBarMessage";
import moment from "moment";
import { DatePicker, DatePickerProps } from "antd";
import dayjs from "dayjs";
import InsertLinkIcon from "@mui/icons-material/InsertLink";

export const TenantCard = (props: any) => {
  const { getDashboardCardDetails } = props;
  const [elevation, setElevation] = useState(1);
  const createPath = useCreatePath();
  const record = useRecordContext<Tenant>();
  const dashboardCard = JSON.parse(localStorage.getItem("dashboard") || "[]");
  if (!record) return null;

  return (
    <MuiLink
      component={Link}
      to={createPath({
        resource: "statelessServiceBzoTenants",
        id: record.id,
        type: "show",
      })}
      underline="none"
      onMouseEnter={() => setElevation(3)}
      onMouseLeave={() => setElevation(1)}
    >
      <Paper
        sx={{
          height: 150,
          width: 145,
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
          padding: "0.5em",
          border: "1px solid #1976d2",
          borderRadius: "20px",
        }}
        elevation={elevation}
      >
        <Box sx={{ position: "relative", justifyContent: "space-between" }}>
          {!record.cancelled && (
            <>
              <EditTenantButton />
              {/* <DeleteTenantButton getDashboardCardDetails={getDashboardCardDetails}/> */}
              <MuiLink
                href={`https://${record.subDomain}`}
                target="_blank"
                rel="noopener noreferrer"
                onClick={(e: any) => e.stopPropagation()}
              >
                <IconButton
                  color="primary"
                  // aria-label="edit"
                  // onClick={(e: any) => handleClick(e)}
                  sx={{
                    position: "absolute",
                    top: 0,
                    right: 0,
                    mt: 0,
                    ml: 0,
                  }}
                >
                  <InsertLinkIcon sx={{ width: "0.7em", height: "0.7em" }} />
                </IconButton>
              </MuiLink>
            </>
          )}
        </Box>
        <Box display="flex" flexDirection="column" alignItems="center">
          <TenantAvatar />
          <Box textAlign="center" marginTop={1}>
            <Typography variant="subtitle1" style={{ fontSize: "0.700rem" }}>
              {record.tenantName}
            </Typography>
            {(record.agreementDate || record.tenantDeletedDate) && (
              <Typography
                variant="subtitle2"
                style={{
                  fontSize: "0.600rem",
                  color: record.tenantDeletedDate ? "red" : "grey",
                }}
              >
                {record.tenantDeletedDate
                  ? `Cancelled On: ${moment(record.tenantDeletedDate).format(
                      "MM/DD/YY"
                    )}`
                  : record.agreementDate
                  ? `Signed On: ${moment(record.agreementDate).format(
                      "MM/DD/YY"
                    )}`
                  : ""}
              </Typography>
            )}
          </Box>
        </Box>
        <Box display="flex" justifyContent="space-between" width="100%">
          <Box display="flex" alignItems="center" justifyContent="center">
            <StoreMallDirectoryIcon sx={{ mr: 0.5, fontSize: "1em" }} />
            <Typography
              variant="subtitle2"
              sx={{ fontSize: "0.800rem", lineHeight: 0 }}
            >
              {dashboardCard.filter === "cancelled"
                ? record.deletedStoreCount
                : record.activeStoreCount}
            </Typography>
          </Box>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <div>
              <AvatarGroup
                max={3}
                sx={{
                  "& .MuiAvatar-circular": {
                    width: "25px",
                    height: "25px",
                    fontSize: "smaller",
                  },
                }}
              >
                {record.dms &&
                  JSON.parse(record.dms).map((dmsData: any) => {
                    return (
                      dmsData.dms_img && (
                        <Avatar
                          src={dmsData.dms_img}
                          alt={dmsData.dms}
                          style={{ marginLeft: -15, width: 25, height: 25 }}
                        />
                      )
                    );
                  })}
              </AvatarGroup>
            </div>
          </Box>
        </Box>
      </Paper>
    </MuiLink>
  );
};
const DeleteTenantButton = (props: any) => {
  const { getDashboardCardDetails } = props;
  const record = useRecordContext();
  const [open, setOpen] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState<any>();
  const refresh = useRefresh();
  const translate = useTranslate();
  const [selectedDate, setSelectedDate] = useState<any>("");

  const handleClick = (event: any) => {
    event.preventDefault();
    event.stopPropagation();
    setOpen(true);
  };
  const handleDialogClose = () => setOpen(false);
  const handleConfirm = () => {
    setLoading(true);
    DeleteTenant(
      Constants.actions.delete,
      record.tenantId,
      record.tenantName,
      selectedDate ? dayjs(selectedDate).format("MM/DD/YYYY") : null
    )
      .then((response: any) => {
        if (response.string === Constants.success) {
          setStatusMessage(
            translate("SUCCESS_MESSAGES.DELETE_MESSAGE", {
              entityName: record.tenantName,
            })
          );
          setOpenSnackbar(true);
          getDashboardCardDetails();
          refresh();
        } else {
          // ToDo: Need show Update Error Message
        }
      })
      .finally(() => {
        setLoading(false);
        setOpen(false);
        // setOpenSnackbar(true);
      });
  };

  const onDateChange: DatePickerProps["onChange"] = (date, dateString) => {
    setSelectedDate(date);
  };
  return (
    <>
      <Tooltip title="Delete Tenant">
        <IconButton
          // color="error"
          aria-label="delete"
          onClick={(e: any) => handleClick(e)}
          sx={{
            position: "absolute",
            top: 0,
            right: 0,
            mt: 0,
            mr: 0,
          }}
        >
          {loading ? (
            <CircularProgress size={24} />
          ) : (
            <DeleteIcon sx={{ width: "0.7em", height: "0.7em" }} />
          )}
        </IconButton>
      </Tooltip>
      <Confirm
        isOpen={open}
        loading={loading}
        title={`Delete ${record.tenantName}`}
        content={
          <Box>
            <Typography variant="body1">
              {translate("DIALOG_BOX.TENANT_DELETE")}
            </Typography>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                mt: 2,
                width: "200px",
              }}
            >
              <Typography
                sx={{
                  color: "#1976D2",
                  fontSize: "12px",
                }}
              >
                Deleted Date
              </Typography>
              <DatePicker
                popupStyle={{ zIndex: 9999 }}
                onChange={onDateChange}
                format={"MM/DD/YYYY"}
                value={selectedDate}
              />
            </Box>
          </Box>
        }
        onConfirm={handleConfirm}
        onClose={handleDialogClose}
      />
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={translate("SUCCESS_MESSAGES.DELETE_MESSAGE", {
          entityName: record.tenantName,
        })}
      />
    </>
  );
};

const EditTenantButton = () => {
  const record = useRecordContext();
  const navigate = useNavigate();
  const handleClick = (event: any) => {
    event.preventDefault();
    event.stopPropagation();
    navigate(PageRoutes.editTenantRoute(record.id));
  };

  return (
    <>
      <IconButton
        color="primary"
        // aria-label="edit"
        onClick={(e: any) => handleClick(e)}
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          mt: 0,
          ml: 0,
        }}
      >
        <EditIcon sx={{ width: "0.7em", height: "0.7em" }} />
      </IconButton>
    </>
  );
};
