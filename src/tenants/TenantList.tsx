import * as React from "react";
import {
  List,
  TopToolbar,
  CreateButton,
  useTranslate,
  SortButton,
} from "react-admin";
import { ImageList } from "./GridList";
import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Typography,
} from "@mui/material";
import Paper from "@mui/material/Paper";
import InputBase from "@mui/material/InputBase";
import IconButton from "@mui/material/IconButton";
import SearchIcon from "@mui/icons-material/Search";
import CloseIcon from "@mui/icons-material/Close";
export const TenantList = (props: any) => {
  const {
    isSelected,
    getDashboardCardDetails,
    tenantNameSearch,
    setTenantNameSearch,
  } = props;
  const [dmsFilter, setDmsFilter] = React.useState("All");

  // Reset the search input filter when the dashboard card changes
  React.useEffect(() => {
    setTenantNameSearch(""); // Clear search input
  }, [isSelected, setTenantNameSearch]);

  const filter = React.useMemo(() => {
    switch (isSelected) {
      case "onboarding":
        return { onboarding: true };
      case "nsQaValidation":
        return { nsQaValidation: true };
      case "review":
        return { review: true };
      case "readyToLaunch":
        return { readyToLaunch: true };
      case "launched":
        return { launched: true };
      case "cancelled":
        return { deletedstoreortenant: true };
      default:
        return { isTestTenant: true };
    }
  }, [isSelected]);

  const nameFilter = React.useMemo(() => {
    return tenantNameSearch.length !== 0
      ? {
          tenantName: {
            operator: "includesInsensitive", // Use case-insensitive search
            value: tenantNameSearch,
          },
        }
      : {};
  }, [tenantNameSearch]);

  const dmsFilterObject = React.useMemo(() => {
    return dmsFilter !== "All"
    ? {
      dmsList: {
        operator: "contains", // Use case-insensitive search
        value: dmsFilter,
      },
    } : {};
  }, [dmsFilter]);

  const combinedFilter = React.useMemo(() => {
    return {
      ...filter,
      ...nameFilter,
      ...dmsFilterObject,
    };
  }, [filter, nameFilter, dmsFilterObject]);

  return (
    <List
      resource="statelessServiceBzoTenants"
      actions={
        <TenantListActions
          setTenantNameSearch={setTenantNameSearch}
          tenantNameSearch={tenantNameSearch}
          dmsFilter={dmsFilter}
          setDmsFilter={setDmsFilter}
        />
      }
      perPage={500}
      pagination={false}
      component="div"
      filter={combinedFilter}
      empty={
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          width={"100%"}
        >
          {" "}
          <TenantListActions
            setTenantNameSearch={setTenantNameSearch}
            tenantNameSearch={tenantNameSearch}
            dmsFilter={dmsFilter}
            setDmsFilter={setDmsFilter}
          />
          <NoRecords />
        </Box>
      }
    >
      <ImageList
        tenantNameSearch={tenantNameSearch}
        dmsFilter={dmsFilter}
        getDashboardCardDetails={getDashboardCardDetails}
      />
    </List>
  );
};

const TenantListActions = (props: any) => {
  const { setTenantNameSearch, tenantNameSearch, dmsFilter, setDmsFilter } =
    props;
  const translate = useTranslate();
  const dashboardCard = JSON.parse(localStorage.getItem("dashboard") || "[]");

  return (
    <TopToolbar
      sx={{
        display: "flex",
        marginBottom: "8px",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%",
      }}
    >
      <SortButton fields={["agreementDate", "tenantName"]} />
      <Stack
        direction="row"
        justifyContent="space-between"
        sx={{
          display: "flex",
          alignItems: "center",
        }}
      >
        {/* DMS Filter */}
        <FormControl
          sx={{ margin: 0, width: "200px", mr: 1 }}
          size="small"
          variant="outlined"
        >
          <InputLabel
            id="demo-simple-select-small-outlined-label"
            sx={{
              mt: "-4px",
              fontSize: "14px",
            }}
          >
            Dms
          </InputLabel>
          <Select
            variant="outlined"
            labelId="demo-simple-select-small-outlined-label"
            id="demo-simple-select-small-outlined"
            value={dmsFilter}
            label="Age"
            onChange={(e: any) => setDmsFilter(e.target.value)}
            sx={{
              height: "30px",
              fontSize: "12px",
              width: "100%"
            }}
          ><MenuItem sx={{ fontSize: "12px" }} value={"All"}>
          All
          </MenuItem>
            <MenuItem sx={{ fontSize: "12px" }} value={"Automate"}>
            Automate
            </MenuItem>
            <MenuItem sx={{ fontSize: "12px" }} value={"CDK Global"}>
            CDK Global
            </MenuItem>
            <MenuItem sx={{ fontSize: "12px" }} value={"Dealertrack"}>
            Dealertrack
            </MenuItem>
            <MenuItem sx={{ fontSize: "12px" }} value={"Reynolds"}>
              Reynolds
            </MenuItem>
            <MenuItem sx={{ fontSize: "12px" }} value={"Tekion"}>
              Tekion
            </MenuItem>
          </Select>
        </FormControl>
        {/* <ExportButton /> */}
        <Paper
          component="form"
          sx={{
            p: "2px 4px",
            display: "flex",
            alignItems: "center",
            width: 400,
            height: "36px",
            marginRight: "8px",
          }}
        >
          <InputBase
            sx={{ ml: 1, flex: 1 }}
            placeholder="Search Tenants"
            inputProps={{ "aria-label": "search google maps" }}
            onChange={(e) => {
              setTenantNameSearch(e.target.value);
            }}
            value={tenantNameSearch}
          />
          <IconButton type="button" sx={{ p: "10px" }} aria-label="search">
            {tenantNameSearch ? (
              <CloseIcon
                onClick={() => {
                  setTenantNameSearch("");
                }}
              />
            ) : (
              <SearchIcon />
            )}
          </IconButton>
        </Paper>
        {dashboardCard.filter === "onboarding" && (
          <CreateButton
            variant="contained"
            label={translate("BUTTONS.NEW_TENANT")}
            sx={{
              margin: 0,
              height: "fit-content",
              padding: "11px 22px",
              borderRadius: "20px",
            }}
          />
        )}
      </Stack>
    </TopToolbar>
  );
};

const NoRecords = () => {
  return (
    <Box
      sx={{
        margin: "10px 0px",
        width: "100%",
        display: "flex",
        justifyContent: "center",
      }}
    >
      <Typography variant="h6">No tenants found</Typography>
    </Box>
  );
};
