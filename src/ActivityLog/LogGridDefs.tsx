import { ColDef } from "ag-grid-community";
import moment from "moment";
import React, { useCallback } from "react";
import DataFetchQueries from "../service/dataFetchQueries";
import dayjs from "dayjs";

const LogGridDefs = (props: any) => {
  const { spec, storeId, tenantId } = props;
  const { GetActivityLog } = DataFetchQueries;
  const [allLogList, setAllLogList] = React.useState([]);
  const [selectedDateRange, setSelectedDateRange] = React.useState<any>([
    dayjs().subtract(1, "week"),
    dayjs(),
  ]);
  const [showDateSaveButton, setShowDateSaveButton] = React.useState(false);
  const getActivityLog = () => {
    const logInput = {
      startDate: selectedDateRange[0],
      endDate: selectedDateRange[1],
      spec: spec,
      storeId: storeId,
      tenantId: tenantId,
    };
    GetActivityLog(logInput).then((res: any) => {
      setAllLogList(res);
    });
  };

  const onGridReady = useCallback((params: any) => {
    params?.api?.showLoadingOverlay();
    getActivityLog();
  }, []);
  const storeColumnDefs = [
    {
      headerName: "Module",
      field: "module",
      cellStyle: { textAlign: "left" },
      flex: 2,
    },
    {
      headerName: "Activity",
      field: "activity",
      flex: 4.5,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "User",
      field: "createdUser",
      cellStyle: { textAlign: "left" },
      flex: 4,
    },
    {
      headerName: "Date & Time",
      field: "createdTime",
      valueFormatter: (params: any) =>
        moment(params.value).format("MM/DD/YYYY, h:mm a"),
      flex: 2.8,
    },
    {
      headerName: "Status",
      field: "status",
      flex: 1.7,
    },
  ];
  const tenantColumnDefs = [
    {
      headerName: "Store Name",
      field: "storeName",
      flex: 4,
      cellStyle: { textAlign: "left" },
    },
    ...storeColumnDefs,
  ];
  const columnDefs: ColDef[] = [
    {
      headerName: "Sl No",
      field: "slno",
      cellStyle: { textAlign: "right" },
      flex: 1.5,
    },
    {
      headerName: "Tenant Name",
      field: "tenantName",
      cellStyle: { textAlign: "left" },
      flex: 4,
    },
    {
      headerName: "Store Name",
      field: "storeName",
      flex: 4,
      cellStyle: { textAlign: "left" },
    },
    ...storeColumnDefs,
  ];
  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { fontSize: "12px", textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      flex: 3,
      filterParams: {
        applyMiniFilterWhileTyping: true,
      },
    };
  }, []);
  return {
    columnDefs,
    defaultColDef,
    onGridReady,
    allLogList,
    selectedDateRange,
    setSelectedDateRange,
    showDateSaveButton,
    setShowDateSaveButton,
    getActivityLog,
    storeColumnDefs,
    tenantColumnDefs,
  };
};

export default LogGridDefs;
