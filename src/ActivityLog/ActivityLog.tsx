import React from "react";
import { But<PERSON>, DatePicker, Form, Space } from "antd";
import { useRecordContext } from "react-admin";
import LogGridDefs from "./LogGridDefs";
import dayjs from "dayjs";
import { AgGridReact } from "ag-grid-react";
import { Box, Typography } from "@mui/material";
import { RangePickerProps } from "antd/es/date-picker";
import { Constants } from "../utils/constants";

const { RangePicker } = DatePicker;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

export default function ActivityLog(props?: any) {
  const { spec } = props;
  const record = useRecordContext();
  const {
    allLogList,
    columnDefs,
    defaultColDef,
    onGridReady,
    selectedDateRange,
    setSelectedDateRange,
    setShowDateSaveButton,
    getActivityLog,
    storeColumnDefs,
    tenantColumnDefs,
  } = LogGridDefs({
    spec,
    storeId: record?.storeId,
    tenantId: record?.tenantId,
  });

  const onFinish = () => {
    updateDateRange();
  };

  const updateDateRange = () => {
    getActivityLog();
    setShowDateSaveButton(false);
  };

  const onDateChange: RangePickerProps["onChange"] = (
    dates: any,
    dateStrings: [string, string]
  ) => {
    if (dates && dates?.length === 2) {
      const startDate = dates[0].startOf("day");
      const endDate = dates[1].endOf("day");
      setSelectedDateRange([startDate, endDate]);
      setShowDateSaveButton(true);
    } else {
      setSelectedDateRange(dates);
      setShowDateSaveButton(true);
    }
  };

  return (
    <Space direction="vertical" style={{ marginTop: "16px" }}>
      <Box display="flex" alignItems="center">
        <Form
          {...formItemLayout}
          onFinish={onFinish}
          initialValues={{
            RangePicker: [dayjs().subtract(1, "week"), dayjs()],
          }}
          style={{ maxWidth: 600, display: "flex" }}
        >
          <Form.Item
            label={
              <span style={{ color: "#003D6B", fontWeight: "bold" }}>
                Date Range
              </span>
            }
            name="RangePicker"
            rules={[{ required: true }]}
            // validateTrigger="onSubmit"
            help=""
          >
            <RangePicker
              format={"MM/DD/YYYY"}
              inputReadOnly={true}
              onChange={onDateChange}
              style={{ width: 290 }}
            />
          </Form.Item>

          <Form.Item>
            <Button
              style={{ backgroundColor: "#1976D2", borderColor: "#1976D2" }}
              type="primary"
              size="middle"
              htmlType="submit"
            >
              Apply
            </Button>
          </Form.Item>
        </Form>
      </Box>
      <Box>
        <div
          className={Constants.ag_grid_theme}
          style={{
            height: "83vh",
            width:
              spec === "storeSpecific"
                ? "81vw"
                : spec === "tenantSpecific"
                ? "83vw"
                : "85vw",
            marginTop: "-15px",
          }}
        >
          <AgGridReact
            columnDefs={
              spec === "storeSpecific"
                ? storeColumnDefs
                : spec === "tenantSpecific"
                ? tenantColumnDefs
                : columnDefs
            }
            editType="fullRow"
            rowData={allLogList}
            defaultColDef={defaultColDef}
            rowSelection="single"
            onGridReady={(params: any) => onGridReady(params)}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            stopEditingWhenCellsLoseFocus={true}
          />
        </div>
      </Box>
    </Space>
  );
}
