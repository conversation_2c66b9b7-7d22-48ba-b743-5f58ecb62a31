{"name": "bzo", "version": "0.1.0", "private": true, "dependencies": {"@ag-grid-community/react": "^29.3.5", "@ag-grid-enterprise/all-modules": "^27.3.0", "@apollo/client": "^3.7.14", "@apollo/react-hooks": "^4.0.0", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@fortawesome/fontawesome": "^1.1.8", "@fortawesome/fontawesome-free-solid": "^5.0.13", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@handsontable/react": "^15.3.0", "@handsontable/react-wrapper": "^15.3.0", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@mui/icons-material": "^5.11.11", "@mui/material": "^5.11.13", "@promitheus/ra-data-postgrest": "^1.2.2", "@react-keycloak/web": "^3.4.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.12", "@types/react": "^18.0.28", "@types/react-datepicker": "^4.19.1", "ag-grid-community": "^29.3.5", "ag-grid-enterprise": "^29.3.5", "ag-grid-react": "^29.3.5", "antd": "^5.16.1", "formik": "^2.4.5", "graphql": "^16.6.0", "handsontable": "^15.3.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "keycloak-js": "^21.0.1", "markdown-to-jsx": "^7.4.3", "moment": "^2.29.4", "notistack": "^3.0.2", "ra-data-graphql": "^4.8.0", "ra-i18n-polyglot": "^4.11.0", "ra-postgraphile": "^6.1.0", "react": "^18.2.0", "react-admin": "^4.8.1", "react-bootstrap-daterangepicker": "^8.0.0", "react-datepicker": "^4.21.0", "react-dom": "^18.2.0", "react-minimal-pie-chart": "^8.4.1", "react-router-dom": "^6.22.3", "react-scripts": "4.0.3", "react-select": "^5.7.4", "react-timezone-select": "^2.1.2", "split-pane-react": "^0.1.3", "styled-components": "^6.1.8", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts --openssl-legacy-provider start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^16.14.21", "@types/react-dom": "^18.0.11", "@types/react-router-dom": "^5.1.9"}}